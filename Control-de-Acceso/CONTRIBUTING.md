## ¿Cómo contribuir a este repositorio?

Si desea contribuir a este repositorio, asegúrese de discutir los cambios que deseas realizar con el equipo de desarrollo.

## Pasos para contribuir

### 1. Clonar el repositorio

```bash
git clone https://github.com/WellnessCenterTec/Control-de-Acceso.git
cd Control-de-Acceso
```

### 2. <PERSON><PERSON>r una rama

```bash
git checkout -b <nombre-rama>
```

### 3. Instalar dependencias

```bash
pnpm install
```

> **Nota:** Si no tiene instalado pnpm, puede instalarlo con el siguiente comando:
>
> ```bash
> npm install -g pnpm
> ```

### 4. Crear archivo .env

```bash
cp .env.example .env
```

> **Nota:** Si está en Windows, puede usar el siguiente comando:
>
> ```bash
> copy .env.example .env
> ```

### 5. Ejectuar el proyecto en modo desarrollo

```bash
pnpm dev
```

### 6. Hacer commit de los cambios

```bash
git add .
pnpm commit
```

> **Nota:** Este repositorio usa [commitlint](https://commitlint.js.org/#/) para validar los mensajes de commit. Si no está familiarizado con esta herramienta, puede leer [este artículo](https://www.conventionalcommits.org/es/v1.0.0/) para aprender a escribir mensajes de commit.

### 7. Hacer push de los cambios

```bash
git push origin <nombre-rama>
```
