"use client";

import axiosClient from "@/config/axiosClient";

import useAuthConfig from "@/hooks/useAuthConfig";
import { UserInt } from "@/types/ModelTypes";
import { handleError } from "@/utils/errorHandler";
import {
  Button,
  Input,
  Modal,
  ModalBody,
  ModalContent,
  ModalFooter,
  ModalHeader,
  Radio,
  RadioGroup,
  Select,
  SelectItem,
} from "@nextui-org/react";
import { set } from "date-fns";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { mutate } from "swr";

interface Props {
  isAddModalOpen: boolean;
  onAddModalChange: () => void;
  updateUser: UserInt | null;
  setUpdateUser: (user: UserInt | null) => void;
  mutate : () => void;
}

export function ModalCUAlumno({
  isAddModalOpen,
  onAddModalChange,
  updateUser,
  setUpdateUser,
  mutate
}: Props) {
  // Estados de la modal
  const [nombreCompleto, setNombreCompleto] = useState(updateUser?.name || "");
  const [matricula, setMatricula] = useState(updateUser?.registration || "");
  const [genero, setGenero] = useState(updateUser?.gender || "");
  const [programa, setPrograma] = useState(updateUser?.program || "");
  const [descPrograma, setDescPrograma] = useState(
    updateUser?.program_key || ""
  );
  const [academicExercise, setAcademicExercise] = useState(
    updateUser?.academic_exercise || ""
  );
  const [observation, setObservation] = useState(
    updateUser?.observation || ""
  );

  useEffect(() => {
    if (updateUser) {
      setNombreCompleto(updateUser.name);
      setMatricula(updateUser.registration);
      setGenero(updateUser.gender)
      setPrograma(updateUser.program ?? "");
      setDescPrograma(updateUser.program_key ?? "");
      setAcademicExercise(updateUser.academic_exercise ?? "");
      setObservation(updateUser.observation ?? "");
    }else{
      setNombreCompleto("");
      setMatricula("");
      setGenero("");
      setPrograma("");
      setDescPrograma("");
      setAcademicExercise("");
      setObservation("");
    }
  }, [updateUser]);

  // Variables calculadas

  // Obtenemos la configuracion de la autenticacion
  const { getConfig } = useAuthConfig();

  // Actuadores

  const handleAddStudent = () => {
    // Verificar si todos los campos obligatorios están llenos
    if (!nombreCompleto || !matricula || !genero || !programa) {
      toast.error("Por favor, llena todos los campos obligatorios.");
      return; // Salir de la función para no continuar con el proceso de agregar al alumno
    }

    const newStudentData = {
      name: nombreCompleto,
      registration: matricula,
      gender: genero,
      program_key: programa,
      program: descPrograma,
      academic_exercise: academicExercise,
      observation: observation,
    } as UserInt;

    if(updateUser){
      newStudentData.id = updateUser.id;
      updateAlumno(newStudentData);
      return;
    }

    addAlumno(newStudentData);
  };

  const addAlumno = async (newStudentData: UserInt) => {
    try {
      await axiosClient.post(
        "/alumno/addAlumno",
        newStudentData,
        await getConfig()
      );

      mutate();
      toast.success("Alumno agregado exitosamente.");
      onAddModalChange(); // Cerrar el modal
    } catch (error: any) {
      return handleError(error);
    }
  };

  const updateAlumno = async (newStudentData: UserInt) => {
    try {
      await axiosClient.put(
        "/alumno/updateAlumno",
        newStudentData,
        await getConfig()
      );

      mutate();
      toast.success("Alumno actualizado exitosamente.");
      onAddModalChange(); // Cerrar el modal
      setUpdateUser(null);
    } catch (error: any) {
      return handleError(error);
    }
  }

  return (
    <Modal isOpen={isAddModalOpen} onOpenChange={onAddModalChange}>
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1">
              Agregar Alumno
            </ModalHeader>
            <ModalBody>
              <Input
                type="text"
                placeholder="Nombre Completo*"
                required
                value={nombreCompleto}
                onChange={(e) => setNombreCompleto(e.target.value)} // Aquí extraemos el valor
              />
              <Input
                type="text"
                placeholder="Matrícula*"
                required
                value={matricula}
                onChange={(e) => setMatricula(e.target.value.toUpperCase())} // Aquí extraemos el valor
              />
              <RadioGroup
                label="Género*"
                orientation="horizontal"
                value={genero}
                onChange={(e) => setGenero(e.target.value)} // Aquí ya extraemos el valor
              >
                <Radio value="1">Masculino</Radio>
                <Radio value="0">Femenino</Radio>
              </RadioGroup>

              <Input
                type="text"
                placeholder="Programa Académico"
                required
                value={programa}
                onChange={(e) => setPrograma(e.target.value)} // Aquí extraemos el valor
              />
              <Input
                type="text"
                placeholder="Desc Programa Académico"
                required
                value={descPrograma}
                onChange={(e) => setDescPrograma(e.target.value)} // Aquí extraemos el valor
              />
              <Input
                type="text"
                placeholder="Periodo Academico"
                required
                value={academicExercise}
                onChange={(e) => setAcademicExercise(e.target.value)} // Aquí extraemos el valor
              />
              <Input
                type="text"
                placeholder="Observaciones"
                required
                value={observation}
                onChange={(e) => {setObservation(e.target.value)}} 
              />
            </ModalBody>
            <ModalFooter>
              <Button
                onClick={() => {
                  handleAddStudent();
                }}
              >
                {updateUser ? "Editar" : "Crear"} Alumno
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}
