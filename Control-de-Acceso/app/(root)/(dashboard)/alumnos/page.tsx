// alumnos/page.tsx

"use client";

import {
  Button,
  CircularProgress,
  Progress,
  Skeleton,
  RadioGroup,
  Radio,
} from "@nextui-org/react";
import { useEffect, useRef, useState } from "react";

import {
  <PERSON>dal,
  ModalBody,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  ModalHeader,
  useDisclosure,
} from "@nextui-org/modal";


import { AxiosProgressEvent } from "axios";
import { DataTable } from "./data-table";

import { useAuth, useOrganization } from "@clerk/nextjs";
import { toast } from "sonner";

import axiosClient from "@/config/axiosClient";
import { ServerResponse } from "@/types/AppTypes";


import useClerkSWR from "@/hooks/useClerkSWR";
import usePagination from "@/hooks/usePagination";
import { handleError } from "@/utils/errorHandler";
import { ModalCUAlumno } from "./ModalCUAlumno";

import { ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal } from "lucide-react";


import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { UserInt } from "@/types/ModelTypes";
import { DataTableColumnHeader } from "./data-table-column-header";
import { Pencil1Icon } from "@radix-ui/react-icons";

interface UsersPaginationInt {
  users: UserInt[];
  totalPages: number;
}

export default function Alumnos() {
  const { membership } = useOrganization();
  const isAdmin = membership?.role === "admin";

  // === Reducer que tendrá el estado de la lógica de paginación ===
  const [paginationState, paginationDispatch] = usePagination({
    query: "",
    filterBy: "name",
    limit: 10,
    page: 1,
  });

  const [updateUser, setUpdateUser] = useState<null | UserInt>(null)

  const columns: ColumnDef<UserInt>[] = [
    {
      accessorKey: "registration",
      header: "Matricula",
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        // Ascendente, descendente, ocultar
        <DataTableColumnHeader column={column} title="Nombre Completo" />
      ),
    },
    {
      accessorKey: "gender",
      header: "Género",
    },
    {
      accessorKey: "program_key",
      header: "Clave del Programa Académico",
    },
    {
      accessorKey: "program",
      header: "Descripción del Programa Académico",
    },
    
    {
      id: "actions",
      cell: ({ row }) => {
        const alumno = row.original;
        return (
          <div className="flex items-center justify-center">

              <Button 
               onClick={() => {
              
                setUpdateUser(alumno)
                onAddModalOpen()
              }}
              variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <Pencil1Icon className="h-4 w-4" />
              </Button>
          </div>
          
        );
      },
    },
  ];

  const {
    data: users,
    error,
    isLoading,
    mutate,
  } = useClerkSWR<UsersPaginationInt>(
    `/alumno/getAlumnos?filterBy=${
      paginationState.filterBy || "registration"
    }&page=${paginationState.page}&limit=${paginationState.limit}&query=${
      paginationState.query
    }`
  );

  // Cada ves que cambie la informacion de la busqueda, reseteamos la pagina a 0
  useEffect(() => {
    paginationDispatch({ type: "SET_PAGE", page: 0 });
  }, [paginationState.query, paginationState.filterBy, paginationState.limit]);

  const {
    isOpen: isAddModalOpen,
    onOpen: onAddModalOpen,
    onOpenChange: onAddModalChange,
  } = useDisclosure();
  const {
    isOpen: isExportModalOpen,
    onOpen: onExportModalOpen,
    onOpenChange: onExportModalChange,
  } = useDisclosure();
  const {
    isOpen: isUploadModalOpen,
    onOpen: onUploadModalOpen,
    onOpenChange: onUploadModalChange,
  } = useDisclosure();

  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const [uploadProgress, setUploadProgress] = useState<number | null>(null);
  const [insertProgress, setInsertProgress] = useState(false);
  const [exportProgress, setExportProgress] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadMode, setUploadMode] = useState<'replace' | 'add'>('replace');

  // Obtenemos la configuracion de la autenticacion
  const {getToken} = useAuth() 

  const handleUploadCSV = async (file: File, mode: 'replace' | 'add') => {
    try {
      const formData = new FormData();
      formData.append("alumnos", file, file.name);
      formData.append("mode", mode);

      setInsertProgress(true);

      const { data } = await axiosClient.post<ServerResponse>(
        "/data/updateStudentsCsv",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${await getToken()}`,
          },
          onUploadProgress: (progressEvent: AxiosProgressEvent) => {
            if (typeof progressEvent.total === "number") {
              const progress = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total
              );
              setUploadProgress(progress);
            }
          },
        }
      );

      toast.success(data.msg);
      mutate();
    } catch (error: any) {
      return handleError(error)

    } finally {
      setUploadProgress(null); // Resetear el progreso
      setInsertProgress(false);
    }
  };

  const handleFileSelection = (file: File) => {
    setSelectedFile(file);
    onUploadModalOpen();
  };

  const handleConfirmUpload = () => {
    if (selectedFile) {
      handleUploadCSV(selectedFile, uploadMode);
      setSelectedFile(null);
      onUploadModalChange();
    }
  };

  const handleExportClick = async (deleteData: boolean) => {
    setExportProgress(true);
    try {
      const { data } = await axiosClient.post(
        "/data/exportDataCsv",
        { deleteData: deleteData },
        { responseType: "blob", headers: { Authorization: `Bearer ${await getToken()}`}}
      );

      // Crear un enlace temporal para descargar el archivo
      const url = window.URL.createObjectURL(new Blob([data]));
      const link = document.createElement("a");
      link.href = url;

      // Si deleteData es true, descargamos el archivo .zip, de lo contrario descargamos alumnos.csv
      // Por el momento sin eliminar datos
      if (false) {
        link.setAttribute("download", "data.zip");
      } else {
        link.setAttribute("download", "alumnos.csv");
      }

      document.body.appendChild(link);
      link.click();

      if (link.parentNode) {
        link.parentNode.removeChild(link);
      }
    } catch (error:any) {
      return handleError(error)

    } finally {
      setExportProgress(false);
      if (deleteData) {
        window.location.reload();
      }
    }
  };

  if (!isAdmin) {
    return null;
  }
  return (
    <>
      <div className="flex justify-end">
        <Button color="success" onClick={()=>{
          setUpdateUser(null)
          onAddModalChange()
        }} className="mt-4">
          Agregar nuevo alumno
        </Button>
      </div>
      <Button
        color="secondary"
        onClick={() => fileInputRef.current?.click()}
        className="mt-4"
      >
        Subir CSV
      </Button>
      {uploadProgress !== null && (
        <Progress value={uploadProgress} className="w-full mt-2" />
      )}
      {insertProgress !== false && (
        <div className="flex items-center gap-2 mt-2">
          <CircularProgress />
          <span>Insertando datos...</span>
        </div>
      )}
      <input
        type="file"
        accept=".csv"
        style={{ display: "none" }}
        ref={fileInputRef}
        onChange={(e) => {
          console.log("Change");
          const file = e.target.files?.[0];
          if (file) {
            handleFileSelection(file);
          }
          // Limpiar el input file después de subir el archivo
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
        }}
      />
      <br></br>
      <Button color="warning" onClick={onExportModalOpen} className="mt-4 mb-4">
        Exportar a CSV
      </Button>
      <ModalCUAlumno
        isAddModalOpen={isAddModalOpen}
        onAddModalChange={onAddModalChange}
        setUpdateUser={setUpdateUser}
        updateUser={updateUser}
        mutate={mutate}
      />

      <Modal isOpen={isExportModalOpen} onOpenChange={onExportModalChange}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                Opciones de Exportación
              </ModalHeader>
              <ModalBody>
                <p>
                  ¿Deseas eliminar los registros de alumnos después de la
                  exportación?
                </p>
                <p>
                  En caso de que sí, también se exportarán los registros de
                  alumnos y se borrarán igualmente.
                </p>
              </ModalBody>
              <ModalFooter>
                {exportProgress ? (
                  <CircularProgress />
                ) : (
                  <div className="flex justify-end space-x-3">
                    <Button
                      color="primary"
                      onClick={() => {
                        handleExportClick(false);
                        onClose();
                      }}
                    >
                      Solo Exportar Alumnos
                    </Button>
                    <Button
                      color="danger"
                      onClick={() => {
                        handleExportClick(true);
                        onClose();
                      }}
                    >
                      Exportar y Eliminar
                    </Button>
                  </div>
                )}
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>

      <Modal isOpen={isUploadModalOpen} onOpenChange={onUploadModalChange}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                Opciones de carga de estudiantes
              </ModalHeader>
              <ModalBody>
                <p className="mb-4">
                  Selecciona cómo deseas procesar el archivo CSV:
                </p>
                <RadioGroup
                  value={uploadMode}
                  onValueChange={(value) => setUploadMode(value as 'replace' | 'add')}
                  className="gap-4"
                >
                  <Radio value="replace" className="items-start">
                    <div className="ml-2">
                      <div className="font-semibold text-foreground">
                        Reemplazar estudiantes existentes
                      </div>
                      <div className="text-sm text-foreground-500 mt-1">
                        Los estudiantes actuales que no estén en el CSV serán eliminados.
                      </div>
                    </div>
                  </Radio>
                  <Radio value="add" className="items-start">
                    <div className="ml-2">
                      <div className="font-semibold text-foreground">
                        Agregar estudiantes nuevos
                      </div>
                      <div className="text-sm text-foreground-500 mt-1">
                        Solo se agregarán o actualizarán los estudiantes del CSV.
                      </div>
                    </div>
                  </Radio>
                </RadioGroup>
              </ModalBody>
              <ModalFooter>
                <Button
                  color="danger"
                  variant="light"
                  onPress={() => {
                    setSelectedFile(null);
                    onClose();
                  }}
                >
                  Cancelar
                </Button>
                <Button
                  color="primary"
                  onPress={handleConfirmUpload}
                >
                  Procesar CSV
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
      <>
        {!users ? (
          <div className="w-[calc(100vw-3rem)] md:w-full">
            <div className="flex items-center justify-between py-4 gap-4 flex-wrap">
              <div className="md:w-[100%-111px] flex items-center flex-wrap gap-4">
                <Skeleton className="w-full md:w-[300px] h-[48px] rounded-lg" />
                <Skeleton className="w-full md:w-[125px] h-[48px] rounded-lg" />
              </div>
              <div className="md:w-[111px] flex items-center justify-end">
                <Skeleton className="w-[111px] h-[40px] rounded-lg" />
              </div>
            </div>
            <Skeleton className="w-full h-[140px] rounded-lg" />
            <div className="flex justify-end mt-4">
              <Skeleton className="w-[470px] h-[32px] rounded-lg" />
            </div>
          </div>
        ) : (
          <div className="w-[calc(100vw-3rem)] md:w-full">
            <DataTable<UserInt, string>
              columns={columns}
              data={users?.users}
              paginationDispatch={paginationDispatch}
              paginationState={paginationState}
              pages={users.totalPages}
            />
          </div>
        )}
      </>
    </>
  );
}
