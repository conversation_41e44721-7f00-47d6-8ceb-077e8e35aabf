"use client";

import { Dispatch, useEffect, useState } from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input, Select, SelectItem } from "@nextui-org/react";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { DataTablePagination } from "./data-table-pagination";
import { PaginationAction, PaginationState } from "@/hooks/usePagination";
import { useDebounce } from "@/hooks/useDebounce";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  // Estados para la paginacion
  pages:number
  paginationState: PaginationState
  paginationDispatch: Dispatch<PaginationAction>
}

export function DataTable<TData, TValue>({
  columns,
  data,
  ...props
}: DataTableProps<TData, TValue>) {
  const {paginationState:{query},paginationDispatch} = props

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>(
    []
  );
  const [columnVisibility, setColumnVisibility] =
    useState<VisibilityState>({});
  const [filterBy, setFilterBy] = useState("matricula");

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
  });

  const [search, setSearch] = useState(query)

  // Usamos nuestro hook de useDebounce para esperar a que el usuario deje de escribir
  const debouncedSearch = useDebounce(search, 1000)

  // Usamos el hook useEffect para actualizar el estado de la busqueda
  useEffect(() => {
    paginationDispatch({type:"SET_QUERY",query:debouncedSearch})
  }, [debouncedSearch])

  return (
    <div className="w-[calc(100vw-3rem)] overflow-hidden md:w-full">
      <div className="flex items-center py-4 gap-4 flex-wrap">
        <Input
          type="search"
          variant="bordered"
          id="student-search"
          label="Buscar Alumno"
          className="w-full md:max-w-[300px]"
          required
          size="sm"
          value={search}
          onChange={(event) => setSearch(event.target.value)}
          onSubmit={(e) => e.preventDefault()}
        />
        <Select
          label="Filtrar por"
          placeholder="Nombre"
          variant="bordered"
          size="sm"
          onChange={(e) => {
            paginationDispatch({type:"SET_FILTER_BY",filterBy:e.target.value as unknown as "name" | "registration" })
          }}
          fullWidth={false}
          className="w-full md:max-w-[200px]"
        >
          <SelectItem key={"name"} value={"nombre"}>
            Nombre
          </SelectItem>
          <SelectItem key={"registration"} value={"registration"}>
            Matrícula
          </SelectItem>
        </Select>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="ml-auto">
              Columnas
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                );
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div className="rounded-md border">
        <Table className="w-[calc(100vw-3rem)] overflow-hidden md:w-full">
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No hay resultados.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination 
      {...props}
      pages={props.pages}
      table={table} />
    </div>
  );
}
