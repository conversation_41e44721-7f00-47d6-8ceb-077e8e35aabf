import {
  ChevronLeftIcon,
  ChevronRightIcon,
  DoubleArrowLeftIcon,
  DoubleArrowRightIcon,
} from "@radix-ui/react-icons";
import { Table } from "@tanstack/react-table";

import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PaginationAction, PaginationState } from "@/hooks/usePagination";
import { Dispatch } from "react";
import Page from "../reportes/page";

interface DataTablePaginationProps<TData> {
  table: Table<TData>;
  // Estados para la paginacion
  paginationState: PaginationState;
  paginationDispatch: Dispatch<PaginationAction>;
  pages: number;
}

export function DataTablePagination<TData>({
  table,
  pages,
  paginationState,
  paginationDispatch,
}: DataTablePaginationProps<TData>) {
  const { page, limit } = paginationState;

  return (
    <div className="flex items-center justify-center md:justify-end px-2 mt-4 flex-wrap">
      <div className="flex items-center space-x-6 lg:space-x-8 flex-wrap gap-4 md:gap-0 justify-center">
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium">Registros por página</p>
          <Select
            value={`${table.getState().pagination.pageSize}`}
            onValueChange={(value: any) => {
              table.setPageSize(Number(value));
              paginationDispatch({
                type: "SET_LIMIT",
                limit: Number(value),
              });
            }}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue placeholder={limit} />
            </SelectTrigger>
            <SelectContent side="top">
              {[10, 20, 30, 40, 50].map((pageSize) => (
                <SelectItem key={pageSize} value={`${pageSize}`}>
                  {pageSize}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex w-[110px] items-center justify-center text-sm font-medium">
          Página {page + 1} de {pages}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={() => {
              paginationDispatch({
                type: "SET_PAGE",
                page: 0,
              });
            }}
            disabled={page === 0}
          >
            <span className="sr-only">Ir a primer página</span>
            <DoubleArrowLeftIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => {
              table.previousPage();
              paginationDispatch({
                type: "SET_PAGE",
                page: page - 1,
              });
            }}
            disabled={page === 0}
          >
            <span className="sr-only">Pagina Anterior</span>

            <ChevronLeftIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() =>{
              
              paginationDispatch({
                type: "SET_PAGE",
                page: page + 1
              })
             
            }}
            disabled={!(page< pages)}
          >
            <span className="sr-only">Siguiente página</span>
            <ChevronRightIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={() => {
              paginationDispatch({
                type: "SET_PAGE",
                page: pages - 1
              })
            }}
            disabled={page=== pages - 1}
          >
             <span className="sr-only">Ultima Pagina Página</span>
            <DoubleArrowRightIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
