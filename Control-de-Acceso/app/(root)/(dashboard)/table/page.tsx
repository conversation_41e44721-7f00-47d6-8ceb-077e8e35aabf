import { Payment, columns } from "./columns";
import { DataTable } from "./data-table";

async function getData(): Promise<Payment[]> {
  // Fetch data from your API here.
  return [
    {
      id: "728ed52f",
      amount: 100,
      status: "pending",
      email: "<EMAIL>",
    },
    {
      id: "489e1d42",
      amount: 125,
      status: "processing",
      email: "<EMAIL>",
    },
    {
      id: "728ed52f",
      amount: 100,
      status: "pending",
      email: "<EMAIL>",
    },
    {
      id: "489e1d42",
      amount: 125,
      status: "processing",
      email: "<EMAIL>",
    },
    {
      id: "728ed52f",
      amount: 100,
      status: "pending",
      email: "<EMAIL>",
    },
    {
      id: "489e1d42",
      amount: 125,
      status: "processing",
      email: "<EMAIL>",
    },
    {
      id: "728ed52f",
      amount: 100,
      status: "pending",
      email: "<EMAIL>",
    },
    {
      id: "489e1d42",
      amount: 125,
      status: "processing",
      email: "<EMAIL>",
    },
    {
      id: "728ed52f",
      amount: 100,
      status: "pending",
      email: "<EMAIL>",
    },
    {
      id: "489e1d42",
      amount: 125,
      status: "processing",
      email: "<EMAIL>",
    },
    {
      id: "728ed52f",
      amount: 100,
      status: "pending",
      email: "<EMAIL>",
    },
    {
      id: "489e1d42",
      amount: 125,
      status: "processing",
      email: "<EMAIL>",
    },
    {
      id: "728ed52f",
      amount: 100,
      status: "pending",
      email: "<EMAIL>",
    },
    {
      id: "489e1d42",
      amount: 125,
      status: "processing",
      email: "<EMAIL>",
    },
  ];
}

export default async function DemoPage() {
  const data = await getData();

  return (
    <div className="container mx-auto py-10">
      <DataTable columns={columns} data={data} />
    </div>
  );
}
