"use client";

import { Input, Spinner } from "@nextui-org/react";
import React, { useState, useRef } from "react";
import StudentCard from "@/components/studentCard";
import { toast } from "sonner";
import { useOrganization } from "@clerk/nextjs";

import axiosClient from "@/config/axiosClient";

import { AttendanceUser } from "@/types/CustomTypes";
import useClerkSWR from "@/hooks/useClerkSWR";
import useAuthConfig from "@/hooks/useAuthConfig";
import { handleError } from "@/utils/errorHandler";

interface Student {
  nombre_completo: string;
  matricula: string;
  clave_programa: string;
  id: number;
  desc_genero: number;
  desc_ejercicio_academico: string;
  lastAttendance: string;
  attendedToday: boolean;
}

const ControlRapido = () => {
  const { membership } = useOrganization();
  const inputAlumno = useRef<HTMLInputElement>(null);

  const [student, setStudent] = useState<AttendanceUser | null>(null);

  const [inputMatricula, setInputMatricula] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const { data: attendancesCount } = useClerkSWR<number>(
    "/asistencia/getAsistenciaCount"
  );

  if (!membership?.role === null || !membership?.role === undefined) {
    return (
      <div className="main-container text-center">
        <h1 className="text-heading3-bold">
          No tienes permisos para acceder a esta página
        </h1>
        <p className="text-body-normal mt-4">
          Si es la primera vez que inicias sesión, debes solicitar acceso al
          administrador del sistema.
        </p>
      </div>
    );
  }

  const { getConfig } = useAuthConfig();

  const toggleAttendance = async (
    studentId: number,
    attendedToday: boolean
  ) => {
    setIsLoading(true);
    try {
      const { data } = await axiosClient.post<AttendanceUser>(
        "/asistencia/updateAttendance",
        {
          id_alumno: studentId,
          attendedToday: !attendedToday,
        },
        await getConfig()
      );

      setStudent(data);
    } catch (error: any) {
      return handleError(error);
    }
    setIsLoading(false);
  };

  // Marcar asistencia
  const markAttendance = async (matricula: string) => {
    setIsLoading(true);
    try {
      const { data } = await axiosClient.post<AttendanceUser>(
        "/asistencia/markAttendance",
        { matricula },
        await getConfig()
      );

      toast.success("Asistencia registrada");

      // Colocamos el usuario de marcar la asistencia
      setStudent(data);
    } catch (error: any) {
      return handleError(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <div className="flex flex-row gap-4 justify-center items-center">
        <Input
          type="search"
          variant="bordered"
          id="default-search"
          label="Registrar Alumno"
          required
          size="lg"
          value={inputMatricula}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            setInputMatricula(e.target.value);
          }}
          onSubmit={(e: React.FormEvent<HTMLInputElement>) => {
            e.preventDefault();
          }}
          ref={inputAlumno}
          onKeyUp={(e: React.KeyboardEvent<HTMLInputElement>) => {
            if (e.key === "Enter") {
              // console.log("Enter");
              e.preventDefault();
              markAttendance(inputMatricula);
              setInputMatricula("");
              inputAlumno.current?.focus();
            }
          }}
          autoCapitalize="on"
          autoComplete="off"
        />
        {isLoading && <Spinner />}
      </div>

      <p className="text-small-medium mt-2">
        <span className="text-small-semibold">{attendancesCount ?? 0}</span>{" "}
        Alumnos registrados hoy
      </p>

      {/* Alumnos */}
      <div className="flex flex-col justify-center items-center gap-4 mt-6">
        {student && (
          <StudentCard
            {...student}
            onAttendanceToggle={() =>
              toggleAttendance(student.id, student.attendedToday)
            }
          />
        )}
      </div>
    </div>
  );
};

export default ControlRapido;
