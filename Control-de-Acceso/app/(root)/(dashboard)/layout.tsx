import "../../globals.css";
import type { Metadata } from "next";
import Topbar from "@/components/shared/Topbar";
import LeftSidebar from "@/components/shared/LeftSidebar";
import Bottombar from "@/components/shared/Bottombar";
import Protected from "@/components/shared/Protected";

export const metadata: Metadata = {
  title: "Wellness Center",
  description: "An app to manage access control to the gym.",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <Protected>
      <Topbar />
      <main className="flex flex-row">
        <LeftSidebar />

        <section className="main-container">
          <div className="w-full max-w-4xl">{children}</div>
        </section>
      </main>

      <Bottombar />
    </Protected>
  );
}
