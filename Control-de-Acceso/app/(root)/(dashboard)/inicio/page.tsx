// page.tsx
"use client"; // Indica que este componente es un componente del cliente

// Importaciones de bibliotecas y componentes
import { useEffect, useState } from "react";

import StudentCard from "@/components/studentCard";
import SearchBar from "@/components/searchBar";
import { toast } from "sonner";

import axiosClient from "@/config/axiosClient";

import { AttendanceUser } from "@/types/CustomTypes";
import useClerkSWR from "@/hooks/useClerkSWR";
import useAuthConfig from "@/hooks/useAuthConfig";
import { handleError } from "@/utils/errorHandler";

// Interfaz para el tipo de datos del estudiante

export default function Home() {
  // Estados locales
  const [searchQuery, setSearchQuery] = useState("");

  // Handler para la búsqueda
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  const {
    data: students,
    error,
    mutate,
  } = useClerkSWR<AttendanceUser[]>(
    `/alumno/getUsersLastAttendance/${searchQuery}`
  ); // Estado y funciones de SWR para manejar la petición de datos

  const { getConfig } = useAuthConfig();

  // Cambiar el estado de asistencia
  const toggleAttendance = async (
    studentId: number,
    attendedToday: boolean
  ) => {
    try {
      await axiosClient.post(
        "/asistencia/updateAttendance",
        {
          id_alumno: studentId,
          attendedToday: !attendedToday,
        },
        await getConfig()
      );

      toast.success("Asistencia actualizada");

      // Volver a buscar los datos
      mutate();
    } catch (error: any) {
      return handleError(error);
    }
  };

  // JSX del componente
  return (
    <>
      <SearchBar onSearch={handleSearch} />
      <div className="flex justify-center items-center flex-col gap-4 mt-5">
        {students && students.length > 0
          ? students.map((student, index) => (
              <StudentCard
                key={index}
                {...student}
                onAttendanceToggle={toggleAttendance}
              />
            ))
          : searchQuery && <p className="text-white">No hay coincidencias</p>}
      </div>
    </>
  );
}
