"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON>, CardBody, Skeleton, Spinner } from "@nextui-org/react";
import { PiUsersThreeBold, PiGraduationCapBold, PiUserBold } from "react-icons/pi";
import RealtimeGender<PERSON>ie from "@/components/charts/gender";
import RealtimeAttendanceBar from "@/components/charts/attendance";

import { DatePickerWithRange } from "@/components/date-range-picker";

import { DateRange } from "react-day-picker";
import useSWR from "swr";
import { GraphResponse } from "@/types/AppTypes";
import useClerkSWR from "@/hooks/useClerkSWR";
import { DateTime } from "luxon";



const GraphDisplay = () => {
  const today = DateTime.now();


  const todayDateStart = today.startOf("day");
  const todayDateEnd = today.endOf("day");

  const [date, setDate] = useState<DateRange | undefined>({
    from: todayDateStart.toJSDate(),
    to: todayDateEnd.toJSDate(),
  });

  // Usaremos un estado debounced para que no se haga una peticion en el instante que se cambia la fecha
  // sino que se espere a que el usuario deje de colocarla

  const from = date?.from?.toISOString() ?? todayDateStart.toISO();
  const to = date?.to?.toISOString() ?? todayDateEnd.toISO();

  const { data: graphData, isLoading } = useClerkSWR<GraphResponse>(
    `/asistencia/graphs/${from}/${to}`
  );


  return (
    <>
      <div className="flex justify-end gap-6 mb-4">
        <DatePickerWithRange
          date={date}
          setDate={(date) => {
            setDate(date);
          }}
        />

        <Button
          variant="ghost"
          type="button"
          onClick={() => {
            setDate({ from: todayDateStart.toJSDate(), to: todayDateEnd.toJSDate() });
          }}
        >
          Hoy
        </Button>
        {isLoading && <Spinner className="ml-2" />}
      </div>
      {graphData ? (
        <>
          <div className="flex justify-between items-center gap-2 flex-wrap w-full mb-4 mt-4">
            <div className="flex flex-col justify-between items-center gap-4 flex-wrap ">
              <Card className="min-w-[270px]" isHoverable>
                <CardBody>
                  <div className="flex justify-between items-center">
                    <p className="text-heading2-bold font-bold">
                      {graphData?.asistencias}
                    </p>
                    <PiUsersThreeBold size={48} className="text-body-medium" />
                  </div>
                  <h3 className="text-body-medium justify-end">Asistencias</h3>
                </CardBody>
              </Card>
              <Card className="min-w-[270px]" isHoverable>
                <CardBody>
                  <div className="flex justify-between items-center">
                    <p className="text-heading2-bold font-bold">
                      {graphData?.alumnos}
                    </p>
                    <PiUserBold
                      size={48}
                      className="text-body-medium"
                    />
                  </div>
                  <h3 className="text-body-medium justify-end">Alumnos</h3>
                </CardBody>
              </Card>
            </div>
            <Card className="w-full md:w-3/5">
              <CardBody>
                <RealtimeGenderPie
                  hombres={graphData.genderStats.hombres}
                  mujeres={graphData.genderStats.mujeres}
                />
              </CardBody>
            </Card>
          </div>
          <Card className="mb-4">
            <CardBody>
              <RealtimeAttendanceBar data={graphData.hours} />
            </CardBody>
          </Card>
        </>
      ) : (
        <Skeleton className="w-full h-72 rounded-lg" />
      )}

      {/* <Card>
        <CardBody>
          <CareerChart />
        </CardBody>
      </Card> */}
    </>
  );
};

export default GraphDisplay;
