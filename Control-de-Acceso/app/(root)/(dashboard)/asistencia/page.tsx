"use client";

import {
  <PERSON><PERSON><PERSON>,
 
  Button,
  CircularProgress,
} from "@nextui-org/react";
import { columns } from "./columns";
import { DataTable } from "./data-table";

import { useDisclosure } from "@nextui-org/modal";
import { useState, useEffect, useReducer } from "react";
import { useAuth, useOrganization } from "@clerk/nextjs";

import { UserAttendanceFlattened } from "@/types/CustomTypes";
import axiosClient from "@/config/axiosClient";
import usePagination from "@/hooks/usePagination";
import useClerkSWR from "@/hooks/useClerkSWR";
import { handleError } from "@/utils/errorHandler";
import { DateRange } from "react-day-picker";
import { DateTime } from "luxon";
import { DatePickerWithRange } from "@/components/date-range-picker";

interface AttendanceDateInt {
  attendances: UserAttendanceFlattened[];
  totalPages: number;
}

export default function AsistenciaPage() {
  const { membership } = useOrganization();
  const isAdmin = membership?.role === "admin";

  if (!isAdmin) {
    return null;
  }

  // === Reducer que tendrá el estado de la lógica de paginación ===
  const [paginationState, paginationDispatch] = usePagination({
    query: "",
    filterBy: "name",
    limit: 10,
    page: 1,
  });

  const today = DateTime.now().startOf("day").toJSDate();

  const [date, setDate] = useState<DateRange | undefined>({
    from: today,
    to: today,
  });

  // Nuestro SWR para obtener los datos de la API y aplicar la busqueda del usuario de manera automatica
  const {
    data: attendances,
    isLoading,
    mutate,
  } = useClerkSWR<AttendanceDateInt>(
    `asistencia/getAsistencia?filterBy=${
      paginationState.filterBy || "registration"
    }&page=${paginationState.page}&limit=${paginationState.limit}&query=${
      paginationState.query
    }`
  );

  // Cada ves que cambie la informacion de la busqueda, reseteamos la pagina a 0
  useEffect(() => {
    paginationDispatch({ type: "SET_PAGE", page: 0 });
  }, [paginationState.query, paginationState.filterBy, paginationState.limit]);

  // Estados de las modales

  const [exportProgress, setExportProgress] = useState(false);

  const [exportUniqueProgress, setExportUniqueProgress] = useState(false);

  const { getToken } = useAuth();

  const handleExportAttendances = async () => {
    setExportProgress(true);
    try {
      const { data } = await axiosClient.post(
        "/data/exportAsistenciaDataCsv",
        {
          startDate: date?.from?.toISOString(),
          endDate: date?.to?.toISOString(),
        },
        {
          responseType: "blob",
          headers: { Authorization: `Bearer ${await getToken()}` },
        }
      );

      const url = window.URL.createObjectURL(new Blob([data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "asistencias.csv");
      document.body.appendChild(link);
      link.click();

      if (link.parentNode) {
        link.parentNode.removeChild(link);
      }
    } catch (error: any) {
      return handleError(error);
    } finally {
      setExportProgress(false); // Ocultar el CircularProgress
    }
  };

  const handleExportUniqueAttendances = async () => {
    setExportUniqueProgress(true);
    try {
      const { data } = await axiosClient.post(
        "/data/exportUniqueAttendancesDataCsv",
        {
          startDate: date?.from?.toISOString(),
          endDate: date?.to?.toISOString(),
        },
        {
          responseType: "blob",
          headers: { Authorization: `Bearer ${await getToken()}` },
        }
      );

      const url = window.URL.createObjectURL(new Blob([data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "asistenciasUnicas.csv");
      document.body.appendChild(link);
      link.click();

      if (link.parentNode) {
        link.parentNode.removeChild(link);
      }
    } catch (error: any) {
      return handleError(error);
    } finally {
      setExportUniqueProgress(false); // Ocultar el CircularProgress
    }
  };

  return (
    <div className="">
      <div className="flex items-center my-4 gap-6 mb-4">
      <Button
        color="warning"
        onClick={handleExportAttendances}
        className=""
        isLoading={exportProgress}
      >
        Exportar asistencias
      </Button>
      <Button
        color="primary"
        onClick={handleExportUniqueAttendances}
        className=" ml-4"
        isLoading={exportUniqueProgress}
      >
        Exportar asistencias únicas
      </Button>

        <DatePickerWithRange
          date={date}
          setDate={(date) => {
            setDate(date);
          }}
        />

        <Button
          variant="ghost"
          type="button"
          onClick={() => {
            setDate({ from: today, to: today });
          }}
        >
          Hoy
        </Button>
        
      </div>

      {!attendances ? (
        <div className="w-[calc(100vw-3rem)] md:w-full">
          <div className="flex items-center justify-between py-4 gap-4 flex-wrap">
            <div className="md:w-[100%-111px] flex items-center flex-wrap gap-4">
              <Skeleton className="w-full md:w-[300px] h-[48px] rounded-lg" />
              <Skeleton className="w-full md:w-[125px] h-[48px] rounded-lg" />
            </div>
            <div className="md:w-[111px] flex items-center justify-end">
              <Skeleton className="w-[111px] h-[40px] rounded-lg" />
            </div>
          </div>
          <Skeleton className="w-full h-[140px] rounded-lg" />
          <div className="flex justify-end mt-4">
            <Skeleton className="w-[470px] h-[32px] rounded-lg" />
          </div>
        </div>
      ) : (
        <div className="w-[calc(100vw-3rem)] md:w-full">
          {isLoading && <CircularProgress />}{" "}
          {/* Indicador de carga de más datos */}
          <DataTable<UserAttendanceFlattened, string>
            columns={columns}
            data={attendances.attendances}
            paginationDispatch={paginationDispatch}
            paginationState={paginationState}
            pages={attendances.totalPages}
          />
        </div>
      )}
    </div>
  );
}
