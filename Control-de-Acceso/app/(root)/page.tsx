"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useAuth, useOrganization } from "@clerk/nextjs";
import { useState } from "react";
import { Spinner } from "@nextui-org/react";
import { VERSION } from "@/constants";
export default function Home() {
  const router = useRouter();
  const { isSignedIn } = useAuth();
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const { membership } = useOrganization();
  const isAdmin = membership?.role === "admin";

  return (
    <section className="w-full h-screen py-[50%] md:py-24 lg:py-32 xl:py-48 bg-black relative">
      <div className="container px-4 md:px-6">
        <div className="grid gap-6 items-center">
          <div className="flex flex-col justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h1 className="text-heading1-bold font-bold tracking-tighter sm:text-5xl xl:text-6xl/none bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-500">
                Wellness Center
              </h1>
              <h2 className="max-w-[600px] text-zinc-200 md:text-xl dark:text-zinc-100 mx-auto">
                Control de Acceso
              </h2>
            </div>
            <div className="w-full max-w-sm space-y-2 mx-auto">
              {isLoading ? (
                <Spinner />
              ) : (
                <Button
                  className="bg-white text-black"
                  type="button"
                  onClick={() => {
                    router.push(
                      `${
                        isSignedIn
                          ? isAdmin
                            ? "/inicio"
                            : "/control-rapido"
                          : "/sign-in"
                      }`
                    );
                    setIsLoading(true);
                  }}
                >
                  {isSignedIn
                    ? isAdmin
                      ? "Dashboard"
                      : "Control de Acceso"
                    : "Iniciar Sesión"}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
      <footer className="absolute bottom-10 w-full text-center">
        <p className="text-xs text-zinc-400">Versión {VERSION}</p>
      </footer>
    </section>
  );
}
