"use client";

import { Spinner } from "@nextui-org/react";
import { useRouter } from "next/navigation";
import React, { useEffect } from "react";


const Error = () => {
  const router = useRouter();


  return (
    // Return the error and a link to go back to the home page
    <div className="main-container">
      {/* <h1 className="text-heading1-bold">Ocurrió un error</h1> */}
      <h1 className="text-heading3-bold">Redirigiendo...</h1>
      <Spinner className="mt-5" color="primary" size="lg" />
      {/* <p className="mt-5">
        Por favor actualiza la página y si el error persiste, contacta al
        administrador.
      </p>
      <Button
        className="mt-3"
        onClick={() => {
          window.location.reload();
          router.push("/control-rapido");
        }}
      >
        Regresar
      </Button> */}
    </div>
  );
};

export default Error;
