import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { Inter } from "next/font/google";

import "../globals.css";
import Image from "next/image";

export const metadata = {
  title: "Wellness Center",
  description: "An app to manage access control to the gym.",
};

const inter = Inter({ subsets: ["latin"] });

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="es">
      <body className={`${inter.className} bg-dark-1`}>
        <ClerkProvider>
          <Image
            src="/wellness-bg.jpeg"
            alt="Wellness Center"
            layout="fill"
            objectFit="cover"
            objectPosition="center"
            quality={100}
            style={{ position: "absolute", top: 0, left: 0 }}
          />
          <main className="h-screen flex justify-center items-center ">
            {children}
          </main>
        </ClerkProvider>
      </body>
    </html>
  );
}
