import { AxiosError } from "axios";
import { toast } from "sonner";
import { play } from "./sound";

export interface RequestResponse {
  msg: string;
}

export const handleError = (error: AxiosError<RequestResponse>) => {
  // Si no es un error de axios
  if (!error?.response) {
    const standardError = error as Error;
    toast.error(standardError.message);
    play("/assets/error.mp3");
    return;
  }

  // Si si lo es

  // Checking error type
  if (error?.code === "ERR_NETWORK") {
    play("/assets/error.mp3");
    
    return toast.error(
      "Hay un error con tu conexión a internet, mejora tu conexión y reintenta"
    );
  }

  if (error?.code === "ERR_CONNECTION_REFUSED") {
    play("/assets/error.mp3");
    
    return toast.error(
      "El servidor no está aceptando conexiones en este momento, intenta de nuevo más tarde"
    );
  }

  if (error?.code === "ERR_TIMEOUT") {
    play("/assets/error.mp3");
    
    return toast.error(
      "La solicitud está tardando demasiado en responder, verifica tu conexión"
    );
  }

  if (error?.code === "ECONNRESET") {
    play("/assets/error.mp3");
    
    return toast.error(
      "La conexión con el servidor fue cerrada abruptamente, intenta nuevamente"
    );
  }

  // Verificamos el status HTTP del error
  switch (error?.response?.status ?? error?.status) {
    case 400:
    play("/assets/error.mp3");
      
    return toast.error(
        error?.response?.data?.msg ??
          "La solicitud no pudo ser procesada, revisa los datos e intenta nuevamente"
      );

    case 401:
    play("/assets/error.mp3");
        
    return toast.error(
            error?.response?.data?.msg ??
          "No tienes autorización para realizar esta operación, inicia sesión nuevamente",
          );
      

    case 403:
    play("/assets/error.mp3");
        
    return toast.error(
            error?.response?.data?.msg ??
            "No tienes permiso para acceder a este recurso, contacta al administrador del sistema"
          );
      

    case 404:
    play("/assets/error.mp3");
        
    return toast.error(
            error?.response?.data?.msg ??
            "No se encontro el recurso, intenta recargar la pagina"
          );
      

    case 500:
    play("/assets/error.mp3");
        
    return toast.error(
            error?.response?.data?.msg ??
            "Ha ocurrido un error de nuestro lado, reportalo lo antes posible"
          );


    default:
      
    play("/assets/error.mp3");
      
    return toast.error(
        error?.response?.data?.msg ??
        JSON.stringify(error)
      );
  }
};
