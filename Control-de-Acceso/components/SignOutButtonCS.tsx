"use client";

import { useOrganization, SignOutButton, SignedIn } from "@clerk/nextjs";
import Image from "next/image";

export default function SignOutButtonCS(): JSX.Element {
  const { membership } = useOrganization();
  const isAdmin = membership?.role === "admin";

  return (
    <div className={`block ${isAdmin && "md:hidden"}`}>
      <SignedIn>
        <SignOutButton>
          <div className="flex cursor-pointer">
            <Image
              src="/assets/logout.svg"
              alt="logout"
              width={24}
              height={24}
            />
            <p className="ml-1 hidden md:block"><PERSON><PERSON><PERSON></p>
          </div>
        </SignOutButton>
      </SignedIn>
    </div>
  );
}
