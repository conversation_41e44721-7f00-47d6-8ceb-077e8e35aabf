"use client";

import React from "react";

import ReactECharts from "echarts-for-react";

const WeeklyAttendance: React.FC = () => {
  // prettier-ignore
  const hours = [
  '12am', '1am', '2am', '3am', '4am', '5am', '6am',
  '7am', '8am', '9am','10am','11am',
  '12pm', '1pm', '2pm', '3pm', '4pm', '5pm',
  '6pm', '7pm', '8pm', '9pm', '10pm', '11pm'
];

  // prettier-ignore
  const days = [
  'Sabado', 'Viernes', 'Ju<PERSON>',
  'Mi<PERSON>rcoles', 'Mart<PERSON>', 'Lunes', 'Domingo'
];

  // DATA: DIA/HORA/VALOR
  // prettier-ignore
  const data = [[1,3,2]]
    .map(function (item) {
        return [item[1], item[0], item[2] || '-'];
    });

  const options = {
    tooltip: {
      position: "top",
    },
    grid: {
      height: "50%",
      top: "10%",
    },
    xAxis: {
      type: "category",
      data: hours,
      splitArea: {
        show: true,
      },
    },
    yAxis: {
      type: "category",
      data: days,
      splitArea: {
        show: true,
      },
    },
    visualMap: {
      min: 0,
      max: 10,
      calculable: true,
      orient: "horizontal",
      left: "center",
      bottom: "15%",
    },
    series: [
      {
        name: "Asistencias",
        type: "heatmap",
        data: data,
        label: {
          show: true,
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  };

  return <ReactECharts option={options} />;
};

export default WeeklyAttendance;
