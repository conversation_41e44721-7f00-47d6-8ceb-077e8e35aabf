"use client";

import React from "react";
import ReactECharts from "echarts-for-react";
import { AxisGraphFormat } from "@/types/AppTypes";

interface AttendanceChartProps{
  data: AxisGraphFormat
}

const AttendanceChart = ({
  data,
}:AttendanceChartProps) => {
  const options = {
    title: {
      text: "Asistencias por hora",
      left: "center",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: [
      {
        type: "category",
        // data: [
        //   "5am",
        //   "6am",
        //   "7am",
        //   "8am",
        //   "9am",
        //   "10am",
        //   "11am",
        //   "12pm",
        //   "1pm",
        //   "2pm",
        //   "3pm",
        //   "4pm",
        //   "5pm",
        //   "6pm",
        //   "7pm",
        //   "8pm",
        //   "9pm",
        // ],
        data: data.labels,
        axisTick: {
          alignWithLabel: true,
        },
      },
    ],
    yAxis: [
      {
        type: "value",
      },
    ],
    series: [
      {
        name: "Alumnos",
        type: "bar",
        barWidth: "60%",
        // data: [
        //   10, 52, 200, 334, 390, 330, 220, 100, 52, 200, 334, 390, 330, 220,
        //   100, 52, 200,
        // ],
        data: data.values,
      },
    ],
    backgroundColor: "transparent",
  };

  return <ReactECharts option={options} theme={"dark"} />;
};

export default AttendanceChart;
