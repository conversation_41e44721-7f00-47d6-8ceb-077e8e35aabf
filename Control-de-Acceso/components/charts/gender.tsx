"use client";

import ReactECharts from "echarts-for-react";

const GenderChart = ({
  hombres,
  mujeres,
}: {
  hombres: number;
  mujeres: number;
}) => {
  const options = {
    title: {
      text: "<PERSON>étricas por Género",
      subtext: "",
      left: "center",
    },
    tooltip: {
      trigger: "item",
    },
    // legend: {
    //   orient: "vertical",
    //   left: "left",
    // },
    series: [
      {
        name: "Asistentes",
        type: "pie",
        radius: "50%",
        data: [
          { value: hombres, name: `<PERSON><PERSON><PERSON>` },
          { value: mujeres, name: `<PERSON><PERSON><PERSON>` },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
        color: ["#37a2da", "#df67e3"],
      },
    ],
    backgroundColor: "transparent",
    label: {
      // color: "inherit",
      formatter: "{b}: {c} \n({d}%)",
    },
  };

  return <ReactECharts option={options} theme={"dark"} />;
};

export default GenderChart;
