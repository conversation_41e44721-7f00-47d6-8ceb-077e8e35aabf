"use client";

import React from "react";
import ReactECharts from "echarts-for-react";

const CapacityChart = () => {
  const options = {
    series: [
      {
        type: "gauge",
        axisLine: {
          lineStyle: {
            width: 30,
            color: [
              [0.3, "#67e0e3"],
              [0.7, "#37a2da"],
              [1, "#fd666d"],
            ],
          },
        },
        pointer: {
          itemStyle: {
            color: "auto",
          },
        },
        axisTick: {
          distance: -30,
          length: 8,
          lineStyle: {
            color: "#fff",
            width: 2,
          },
        },
        splitLine: {
          distance: -30,
          length: 30,
          lineStyle: {
            color: "#fff",
            width: 4,
          },
        },
        axisLabel: {
          color: "inherit",
          distance: 40,
          fontSize: 20,
        },
        detail: {
          valueAnimation: true,
          formatter: "{value} alumnos",
          color: "inherit",
        },
        data: [
          {
            value: 70,
          },
        ],
      },
    ],
  };

  return <ReactECharts option={options} theme={"dark"} />;
};

export default CapacityChart;
