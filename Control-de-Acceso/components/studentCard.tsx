import { FC, useEffect } from "react";
import "../public/styles/studentCard.css";
import { <PERSON><PERSON>, <PERSON>, CardBody, CardHeader, Divider } from "@nextui-org/react";

import { AttendanceUser } from "@/types/CustomTypes";

// Define la interfaz para las propiedades del componente

interface StudentCardProps extends AttendanceUser {
  onAttendanceToggle: (id: number, attendedToday: boolean) => void;
}

// Define el componente StudentCard
const StudentCard: FC<StudentCardProps> = ({
  id,
  name,
  registration,
  program_key,
  academic_exercise,
  lastAttendance,
  attendedToday,
  onAttendanceToggle,
  observation,
}) => {
  // Renderiza el componente
  return (
    <>
      <Card className="w-full p-0 md:p-5">
        <CardHeader className="justify-between flex-wrap gap-2">
          <div className="flex gap-5">
            <div className="flex flex-col gap-1 items-start justify-center">
              <h4 className="text-body-semibold md:text-heading2-semibold font-semibold leading-none text-default-600">
                {name}
              </h4>
              {observation && (
                <div className="text-orange-500 bg-orange-200 px-2 py-1 font-bold rounded-md ">
                  {observation}
                </div>
              )}
              <div className="flex gap-2">
                <h5 className="text-small-semibold md:text-small tracking-tight text-default-400">
                  {registration}
                </h5>
                <h5 className="text-small-semibold md:text-small tracking-tight text-default-300">
                  {" "}
                  |{" "}
                </h5>
                <h5 className="text-small-semibold md:text-small tracking-tight text-default-400">
                  {program_key}
                </h5>
                <h5 className="text-small-semibold md:text-small tracking-tight text-default-300">
                  {" "}
                  |{" "}
                </h5>
                <h5 className="text-small-semibold md:text-small tracking-tight text-default-400">
                  {academic_exercise}
                </h5>
              </div>
            </div>
          </div>
          <Button
            color={!attendedToday ? "danger" : "success"}
            radius="full"
            size="md"
            variant={!attendedToday ? "bordered" : "solid"}
            onPress={() => onAttendanceToggle(id, attendedToday)}
          >
            {attendedToday ? "Desmarcar asistencia" : "Marcar asistencia"}
          </Button>
        </CardHeader>
        <Divider />

        <CardBody className="px-3 text-medium text-default-400 p-4">
          <div className="flex gap-2">
            <p className="text-default-foreground">Asistencia de Hoy: </p>
            <p>
              {attendedToday && lastAttendance
                ? `${new Date(lastAttendance).toLocaleDateString("es-MX", {
                    weekday: "long",
                    // year: "numeric",
                    month: "long",
                    day: "numeric",
                  })} a las ${new Date(lastAttendance).toLocaleString("es-MX", {
                    hour: "numeric",
                    minute: "numeric",
                    hour12: true,
                  })}`
                : "No hay asistencia"}
            </p>
          </div>
        </CardBody>
      </Card>
    </>
  );
};

export default StudentCard;
