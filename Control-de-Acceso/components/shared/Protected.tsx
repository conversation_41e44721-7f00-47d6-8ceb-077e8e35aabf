"use client";

import { useOrganization } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function Protected({ children }: { children: React.ReactNode }) {
  const { membership } = useOrganization();
  const isAdmin = membership?.role === "admin";
  const router = useRouter();

  useEffect(() => {
    if (!isAdmin) {
      router.push("/control-rapido");
    }
  }, [isAdmin]);

  return <>{children}</>;
}
