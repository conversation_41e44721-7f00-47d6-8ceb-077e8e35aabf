"use client";

import {
  UserButton,
  useOrganization,
  OrganizationSwitcher,
} from "@clerk/nextjs";
import { dark } from "@clerk/themes";

export default function TopbarCS(): JSX.Element {
  const { membership } = useOrganization();
  const isAdmin = membership?.role === "admin";

  return (
    <>
      <OrganizationSwitcher
        appearance={{
          baseTheme: dark,
          elements: {
            organzationSwitcherTrigger: "py-2 px-4",
          },
        }}
      />
      {isAdmin && (
        <UserButton
          afterSignOutUrl="/"
          appearance={{
            baseTheme: dark,
            elements: {
              organzationSwitcherTrigger: "py-2 px-4",
            },
          }}
          showName={false}
        />
      )}
    </>
  );
}
