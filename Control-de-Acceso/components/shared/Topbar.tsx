import Image from "next/image";
import Link from "next/link";
import TopbarCS from "./TopbarCS";
import SignOutButtonCS from "../SignOutButtonCS";

export const metadata = {
  title: "Wellness Center",
  description: "An app to manage access control to the gym.",
};

function Topbar() {
  return (
    <nav className="topbar">
      <Link href="/" className="flex items-center gap-4">
        <Image
          src="/assets/borrego-white.svg"
          alt="logo"
          width={48}
          height={28}
        />
        <p className="hidden md:block text-heading3-bold text-light-1 max-xs:hidden">
          Control de Acceso
        </p>
      </Link>

      <div className="flex items-center gap-2">
        <div className="flex gap-2 items-center justify-center">
          <TopbarCS />
        </div>
        <SignOutButtonCS />
      </div>
    </nav>
  );
}

export default Topbar;
