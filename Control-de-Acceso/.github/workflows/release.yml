name: Release

on:
  push:
    branches:
      - main
jobs:
  release:
    name: Release
    permissions:
      contents: write
      issues: write
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: 18
      - run: npm i -g pnpm
      - run: pnpm install
      - name: Setup environment variables
        run: echo "${{ secrets.ENV }}" >> .env
      - run: pnpm build
      - run: npx semantic-release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
