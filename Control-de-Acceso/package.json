{"name": "control-de-acceso", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "commit": "git-cz"}, "dependencies": {"@clerk/nextjs": "^4.23.5", "@clerk/themes": "^1.7.5", "@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "@material-tailwind/react": "^2.0.7", "@nextui-org/react": "^2.1.13", "@nextui-org/system": "^2.0.10", "@nextui-org/theme": "^2.1.9", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-slot": "^1.0.2", "@tanstack/react-table": "^8.10.0", "@types/luxon": "^3.4.2", "adm-zip": "^0.5.10", "archiver": "^6.0.1", "autoprefixer": "10.4.14", "axios": "^1.5.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "csv-parser": "^3.0.0", "date-fns": "^2.30.0", "echarts-for-react": "^3.0.2", "eslint": "8.44.0", "eslint-config-next": "13.4.9", "excel4node": "^1.8.2", "formidable": "^3.5.1", "framer-motion": "^10.16.4", "git-cz": "^4.9.0", "json2csv": "6.0.0-alpha.2", "jszip": "^3.10.1", "lucide-react": "^0.279.0", "luxon": "^3.4.4", "micro": "^10.0.1", "mysql": "^2.18.1", "mysql2": "^3.6.0", "next": "14.0.3", "next-pwa": "^5.6.0", "next-themes": "^0.2.1", "pg-hstore": "^2.3.4", "postcss": "8.4.25", "react": "18.2.0", "react-day-picker": "^8.9.1", "react-dom": "18.2.0", "react-icons": "^4.11.0", "react-modal": "^3.16.1", "sequelize": "^6.32.1", "sonner": "^1.0.3", "swr": "^2.2.2", "tailwind-merge": "^1.14.0", "tailwindcss": "3.3.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@semantic-release/git": "^10.0.1", "@semantic-release/github": "^9.2.1", "@types/node": "20.4.2", "@types/react": "18.2.14", "@types/react-dom": "18.2.7", "@types/react-modal": "^3.16.0", "semantic-release": "^22.0.5", "typescript": "5.1.6"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}