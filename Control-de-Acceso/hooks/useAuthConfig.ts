import { useAuth } from "@clerk/nextjs";
import React, { useEffect, useState } from "react";

interface ConfigInt{
    headers: {
        Authorization: string
    }
}

export default function useAuthConfig() {
  const { getToken } = useAuth();

  async function getConfig(){
    const token = await getToken();
    return {
      headers: { Authorization: `Bearer ${token}`,"ngrok-skip-browser-warning":true },
    };
  }

  return {getConfig};
}