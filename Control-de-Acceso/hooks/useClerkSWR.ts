import axiosClient from '@/config/axiosClient'
import { useAuth } from '@clerk/nextjs'
import React from 'react'
import useSWR from 'swr'


export default function useClerkSWR<T>(key:string) {
  
    const {getToken} = useAuth()

    // Definimos el fetcher

    const fetcher = async (url: string) => {
        const token = await getToken();
        const response = await axiosClient(url, {
          headers: { Authorization: `Bearer ${token}`,"ngrok-skip-browser-warning":true }
        });
        return response.data;
      };

    return useSWR<T>(key,fetcher)

}
