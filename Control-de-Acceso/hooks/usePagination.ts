// Definimos la lógica de la paginación en un hook para poder reutilizarla en otros componentes

import { useReducer } from "react";


// == Reducer de paginacion

// Definimos el tipo de dato para nuestro estado
export type PaginationState = {
    query: string;
    filterBy: "registration" | "name"
    limit:number
    page: number;
  };
  
  // === Definimos el tipo de dato para nuestras acciones
  // == SET_TOTAL_PAGES sera definido por la api que nos dará el total de paginas
  // == SET_PAGE_SIZE sera definido por el usuario
  // == NEXT_PAGE sera definido por el usuario
  // == PREVIOUS_PAGE sera definido por el usuario
  
  export type PaginationAction =
    | { type: "SET_PAGE"; page: number }
    | { type: "SET_QUERY"; query: string }
    | { type: "SET_FILTER_BY"; filterBy: "registration" | "name" }
    | { type: "SET_LIMIT"; limit: number }
    
  
  // Reducer para nuestra paginacion
  export const paginationReducer = (
    state: PaginationState,
    action: PaginationAction
  ) => {
    switch (action.type) {
      case "SET_PAGE":
        return {
          ...state,
          page: action.page
        };
      case "SET_QUERY":
        return {
          ...state,
          query: action.query
        };
      case "SET_FILTER_BY":
        return {
          ...state,
          filterBy: action.filterBy
        };
      case "SET_LIMIT":
        return {
          ...state,
          limit: action.limit
        };
      default:
        return state;
    }
  };
  


export default function usePagination(initialState:PaginationState) {
    // Definimos el reducer para nuestra paginacion y
    // Devolvemos el dispatch y el estado de la paginacion
    return useReducer(paginationReducer, initialState);
}
