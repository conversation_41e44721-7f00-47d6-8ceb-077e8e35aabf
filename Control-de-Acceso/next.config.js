// //past

// /** @type {import('next').NextConfig} */
// const nextConfig = {};

// module.exports = nextConfig;

// // new
// const withPWA = require("next-pwa")({
//   dest: "public",
//   register: true,
//   skipWaiting: true,
// });

// module.exports = withPWA({
//   reactStrictMode: true,
// });

// mixed
const withPWA = require("next-pwa")({
  dest: "public",
  register: true,
  skipWaiting: true,
});

module.exports = withPWA({
  reactStrictMode: true,
  env:{
    BACKEND_URL: process.env.BACKEND_URL,
  }
});
