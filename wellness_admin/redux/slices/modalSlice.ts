import { createSlice } from "@reduxjs/toolkit"

export const modalSlice = createSlice({

    name: "modal",
    initialState: {
        modalCerrar: false,
        modalEditar: false,
        modalEspacio: false,
        modalReservable : false

    },

    reducers: {

        toggleModalCerrar: (state) => {
            state.modalCerrar = !state.modalCerrar

        },
        toggleModalEditar: (state) => {
            state.modalEditar = !state.modalEditar

        },
        toggleModalEspacio: (state) => {
            state.modalEspacio = !state.modalEspacio

        },
        toggleModalReservable :(state)=>{
            state.modalReservable = !state.modalReservable
        }

    }
})

export const {toggleModalEspacio,toggleModalEditar,toggleModalCerrar,toggleModalReservable} = modalSlice.actions;

export default modalSlice.reducer