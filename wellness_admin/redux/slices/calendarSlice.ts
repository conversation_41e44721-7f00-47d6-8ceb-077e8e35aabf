import { ReservableInt } from "@/types/ModelTypes";
import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { set } from "date-fns";



const calendarSlice = createSlice({
    name:"calendar",
    initialState:{
        reservable: null as ReservableInt | null,
       
    },
    reducers:{
        setReservable(state,action:PayloadAction<ReservableInt | null>){
            state.reservable = action.payload
        }
       
        
    }
})

export const {setReservable} = calendarSlice.actions;



export default calendarSlice.reducer;