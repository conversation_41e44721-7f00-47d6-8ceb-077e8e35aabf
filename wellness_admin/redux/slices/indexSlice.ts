import { SpaceInt } from "@/types/ModelTypes";
import { PayloadAction, createSlice } from "@reduxjs/toolkit";

const indexSlice = createSlice({
    name:"index",
    initialState:{
        spaceId:null as null | number,
        aforoactual: 0 as 0 | number,
        space: null as SpaceInt | null
    },
    reducers:{
        setSpaceIdo:(state,action:PayloadAction<number>)=>{
            state.spaceId = action.payload
        },
        setAforoo:(state,action:PayloadAction<number>)=>{
            state.aforoactual = action.payload
        },
        setSpace:(state,action:PayloadAction<SpaceInt | null>)=>{
            state.space = action.payload
        }
    }
})


export const {setSpaceIdo,setAforoo,setSpace} = indexSlice.actions;
export default indexSlice.reducer;