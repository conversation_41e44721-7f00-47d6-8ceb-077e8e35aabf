import { configureStore } from "@reduxjs/toolkit";
import AuthSlice from "./slices/authSlice";
import modalSlice from "./slices/modalSlice";
import calendarSlice from "./slices/calendarSlice";
import indexSlice from "./slices/indexSlice";





const store = configureStore({
  reducer: {
    auth:AuthSlice,
    modal:modalSlice,
    calendar:calendarSlice,
    index:indexSlice
  },
});
export default store;
// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = typeof store.dispatch;
