"use client";

import React from "react";
import Image from "next/image";

import "react-responsive-carousel/lib/styles/carousel.min.css"; // requires a loader
import { Carousel } from 'react-responsive-carousel';

import useSWR from "swr";
import { fetcher } from "@/config/fetcher";
import { eventosInt } from "@/types/ModelTypes";
import Loader from "@/components/shared/Loader";
import { formatearFecha, formatearHora } from "@/utils/helpers";



const eventos: React.FC = () => {
    
  const { data : eventos} = useSWR<eventosInt[]>(`/reservable/displayReservable`, fetcher);

  if (!eventos) {
    return <Loader />
  }



  return (

    <div >

        {eventos.length > 0 ? (
            <Carousel showArrows={false} stopOnHover={false} 
            autoPlay={true} infiniteLoop = {true} 
            interval = {7000} 
            showThumbs = {false} 
            showStatus = {false} 
            useKeyboardArrows = {true}>

            {eventos.map((element) => (
                <div className="relative w-full h-screen overflow-hidden">
                <img src = {element.foto} className="object-cover w-full h-full" alt={element.nombre}/>

                {/* Overlay */}
                <div className="w-full h-full bg-gray-950 opacity-60 absolute top-0 left-0 z-20 inline-block"></div>

                {/* Contenido */}

                    <div className="w-full h-full grid place-items-center absolute top-0 left-0 z-30">
                        <div>
                            <p className="text-gray-100 font-bold lg:text-8xl xl:text-8xl" > {element.nombre}</p>
                            <p className="text-gray-100 font-bold lg:text-5xl xl:text-5xl mt-8 font-Roboto" > Coach: {element.coach}</p>
                            <p className="text-gray-100  lg:text-5xl xl:text-5xl mt-8 font-Roboto" >  {formatearHora(element.init_date.toString())} -  {formatearHora(element.end_date.toString())} </p>
                            <p className="text-gray-100  lg:text-5xl xl:text-5xl mt-8 font-inter" >Aforo: {element.actual} / {element.quota} </p>
                            
                        </div>
                    </div>
                </div>
            ))}

            </Carousel>

        ) : (

            <Carousel showArrows={false} autoPlay={false} showIndicators ={false} infiniteLoop = {true} interval = {3000} showThumbs = {false} showStatus = {false} >
                <div>
                    <img src = "/images/borregos.jpg"/>
                    {/* Overlay */}
                    <div className="w-full h-full bg-gray-600 opacity-60 absolute top-0 left-0 z-20 inline-block "></div>

                    {/* Contenido */}
                    <div className="w-full h-full grid place-items-center absolute top-0 left-0 z-30">
                        <div>
                            <p className="text-gray-100 font-bold lg:text-4xl xl:text-7xl" > No hay eventos próximos</p>
                        </div>
                    </div>
                </div>
                <div/>
            </Carousel>

        )}

    </div>

  );
};

export default eventos;
