"use client"

import React,{useState} from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { handleError } from "@/utils/errorHandler";
import { axiosConfig } from "@/config/axiosConfig";
import clienteAxios from "@/config/clienteAxios";
import { useDispatch } from "react-redux";

import jsCookie from "js-cookie"
import { setAuth } from "@/redux/slices/authSlice";


const LoginPage: React.FC = () => {

  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")

  const dispatch = useDispatch()

  const handleSubmit = async (e:any)=>{
    e.preventDefault()

    try {
      
      // Hacemos la peticion

      const {data} = await clienteAxios.post("/admin/login",{
        email, password
      })

      // Colocamos la cookie

      jsCookie.set("token", data.token, {
        expires: new Date().setMonth(new Date().getMonth() + 1),
      });

      // Guardamos la autenticacion

      dispatch(setAuth(data))

      // Mandamos a la pantalla
      window.location.href = "/"

    } catch (error:any) {
      return handleError(error)
    }
  }

  const router = useRouter()
  return (
    <div className="min-h-screen flex flex-col bg-white  ">
      {/*Parte de arriba */}
      <div className="flex-col relative">
        {/* Background image with dark background overlay */}
        <div className=" relative h-45vh md:h-50vh">
          <Image
            className="absolute top-0 left-0 z-10 h-45vh md:h-50vh w-full object-cover object-center"
            src={"/images/borregos.jpg"}
            width={500}
            height={500}
            alt="Borregos"
          />

          <div className="absolute top-0 left-0 w-full h-full bg-[#194DD3] opacity-30"></div>
          {/* Triangulo */}
          <div className="absolute bottom-0 w-full z-30">
            <div className="relative w-full h-28 md:h-30 lg:h-40">
              <Image
                className="absolute bottom-0 z-10 w-full h-28 md:h-30 lg:h-40"
                src={"/images/triangulo.svg"}
                width={801}
                height={723}
                alt="Tringulo"
              />
              <div className="w-full grid place-items-center h-28 md:h-30 lg:h-40">
                <Image
                  className="z-20 w-20 md:w-24 lg:w-28"
                  src={"/images/borrego-blue.png"}
                  alt="Logo"
                  width={220}
                  height={170}
                />
              </div>
            </div>
            {/* Logo */}
          </div>
        </div>
      </div>

      {/* Parte de Abajo*/}
      <form 
      onSubmit={handleSubmit}
      className="lg:mt-10 xl:mt-10 flex flex-col justify-center items-center">
        {/* Text Inputs*/}
        <div className="w-3/4 lg:w-auto xl:auto">
          <h2>Email</h2>
          <input
            className="text-lg w-full  px-0 py-1 border-b border-black placeholder-gray-500"
            type="text"
            placeholder="Email"
            value={email}
            onChange={(e)=>setEmail(e.target.value)}
          />
          <input
            className="text-lg w-full px-0 py-1 border-b border-black placeholder-gray-500 mt-6"
            type="password"
            placeholder="Contraseña"
            value={password}
            onChange={(e)=>setPassword(e.target.value)}
          />
        </div>
        <button
          className="mt-8  bg-[#0E369C] hover:bg-blue-800 px-10 py-3 text-white rounded text-lg font-bold tracking-wider"
          type="submit"
          
        >
          Iniciar Sesión
        </button>
      </form>
    </div>
  );
};

export default LoginPage;
