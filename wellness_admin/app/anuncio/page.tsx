"use client";

import CircleGraph from "@/components/Graphs/CircleGraph";
import ProyectionGraph from "@/components/Graphs/ProyectionGraph";
import { fetcher, microServiceFetcher } from "@/config/fetcher";
import { GraphData, ProyectionGraphData } from "@/types/AppTypes";
import Image from "next/image";
import React from "react";
import useSWR from "swr";

export default function page() {
  const { data: hourStatus } = useSWR<ProyectionGraphData[]>(
    "/space/wellnessAttendancesApp",
    microServiceFetcher
  );
  const { data:liveStatus } = useSWR<GraphData[]>("/space/liveStatusApp", microServiceFetcher,{
    refreshInterval: 60000 // 1 minuto
  });

  return (
    <div className="w-full h-screen relative">
      <Image
        src="/images/borregos.jpg"
        className="absolute top-0 left-0 object-center object-cover w-full h-screen z-0"
        width={1000}
        height={1000}
        alt="Imagen del wellness"
      />
      <div className="bg-blue-500 w-full h-screen absolute top-0 left-0 opacity-40 z-10"></div>

      <div className="w-full h-screen absolute top-0 left-0 grid place-items-center z-20">
        <h2 className="text-4xl text-white font-bold">Llenado del Wellness en Vivo</h2>
        <div className="grid place-items-center grid-cols-2 w-5/6  bg-gray-100 rounded-2xl py-4">
          <div className="w-full ">
            <p className="text-center font-bold text-2xl my-8">
              Afluencia del Gimnasio en tiempo real
            </p>
            <div className="flex items-center justify-center">
              <CircleGraph
                data={liveStatus}
                className=""
              />
              

              {liveStatus && (

              <div className="flex flex-col items-start gap-3 ">

                <div className="flex items-center gap-2">
                  <div className={`w-8 h-8 rounded-md bg-[#F43F5E]`}></div>
                  <p className="text-2xl">Lleno</p>
                  <p className="text-2xl">{liveStatus[0].value}</p>


                </div>
                <div className="flex items-center gap-2">
                  <div className={`w-8 h-8 rounded-md bg-[#60A5FA]`}></div>
                  <p className="text-2xl">Disponible</p>
                  <p className="text-2xl">{liveStatus[1].value}</p>

                </div>

              </div>
              )}

            </div>
          </div>

          <div className="w-full">
            <p className="text-center font-bold text-2xl my-8">
              Afluencia del Gimnasio por hora
            </p>
            <div className=" mx-auto">
              {hourStatus && <ProyectionGraph data={hourStatus} />}
            </div>
          </div>
        </div>

        <div className="grid place-items-center gap-5">

            <h2 className="text-2xl text-white font-bold">Registrate y tenlo en tu telefono</h2>
            <Image 
              src={"/qr.png"}
              // className="w-full h-full"
              width={300}
              height={300}
              alt="Codigo QR"
            />

        </div>
      </div>
    </div>
  );
}
