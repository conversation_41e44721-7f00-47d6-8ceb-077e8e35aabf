"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import useSWR from "swr";
import { fetcher } from "@/config/fetcher";
import { toggleModalEspacio } from "@/redux/slices/modalSlice";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { SpaceInt } from "@/types/ModelTypes";

import PageHeader from "@/components/shared/PageHeader";
import Spinner from "@/components/shared/Spinner";
import Space from "@/components/screens/Inicio/Space";
import ModalEditar from "@/components/screens/Inicio/modaleditar";
import ModalCerrar from "@/components/screens/Inicio/modalcerrar";
import { ModalCUSpace } from "@/components/screens/Inicio/ModalCUSpace";
import CircleGraph from "@/components/Graphs/CircleGraph";
import ProyectionGraph from "@/components/Graphs/ProyectionGraph";

const Inicio: React.FC = () => {
  const { data } = useSWR(`/space/spaces`, fetcher);
  const { data: hourStatus } = useSWR(
    "/space/wellnessAttendances",
    fetcher
  );
  const { data: liveStatus } = useSWR("/space/liveStatus", fetcher);

  const { auth } = useSelector((state: RootState) => state.auth);
  const dispatch = useDispatch();

  const [currentPage, setCurrentPage] = useState(0);
  const [filteredData, setFilteredData] = useState<SpaceInt[]>([]);
  const [search, setSearch] = useState<string>("");

  useEffect(() => {
    if (!data) return;
    if (search === "") setFilteredData(data);
    else
      setFilteredData(
        data.filter((space: SpaceInt) =>
          space.name.toLowerCase().trim().includes(search.toLowerCase().trim())
        )
      );
  }, [data, search]);

  if (!data || !hourStatus || !liveStatus) {
    return <Spinner />;
  }

  const itemsPerPage = 10;
  const offset = currentPage * itemsPerPage;
  const currentItems = filteredData.slice(offset, offset + itemsPerPage);
  const pageCount = Math.max(1, Math.ceil(filteredData.length / itemsPerPage));

  const handleClickNext = () => {
    if (currentPage < pageCount - 1) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handleClickPrev = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  };

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Header */}
      <PageHeader
        title={`Bienvenido ${auth?.name}`}
        image={"/images/borregos.jpg"}
      />

      {/* QR Button */}
      <div className="flex items-center justify-center my-4">
        <Link
          className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-6 rounded-md font-semibold transition-colors flex items-center gap-2"
          href="/readqr"
        >
          <i className="fa-solid fa-qrcode"></i>
          QR de asistencia
        </Link>
      </div>

      {/* Dashboard Section */}
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          {/* Real-time occupancy */}
          <div className="bg-white rounded-lg shadow-md p-4">
            <h2 className="text-xl font-bold text-center mb-4">
              Afluencia del Gimnasio en tiempo real
            </h2>
            <div className="flex flex-col md:flex-row items-center justify-center gap-6">
              <div className="w-48 h-48">
                <CircleGraph data={liveStatus} />
              </div>
              {liveStatus && (
                <div className="flex flex-col gap-4">
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full bg-[#F43F5E]"></div>
                    <p className="text-lg">Lleno: {liveStatus[0].value}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full bg-[#60A5FA]"></div>
                    <p className="text-lg">Disponible: {liveStatus[1].value}</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Hourly occupancy */}
          <div className="bg-white rounded-lg shadow-md p-4">
            <h2 className="text-xl font-bold text-center mb-4">
              Afluencia del Gimnasio por hora
            </h2>
            <div className="h-60">
              {hourStatus && <ProyectionGraph data={hourStatus} />}
            </div>
          </div>
        </div>

        {/* Spaces Management Section */}
        <div className="bg-white rounded-lg shadow-md p-4 mb-8">
          <div className="flex flex-col md:flex-row justify-between items-center mb-4">
            <h2 className="text-xl font-bold text-blue-800 text-center md:text-left mb-3 md:mb-0">
              Administración de Áreas Deportivas
            </h2>
            <div className="relative w-full md:w-64">
              <input
                type="text"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Buscar por nombre..."
              />
              {search && (
                <button
                  onClick={() => setSearch("")}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                >
                  <i className="fa-solid fa-times"></i>
                </button>
              )}
            </div>
          </div>

          {/* Table Headers */}
          <div className="hidden md:grid md:grid-cols-12 gap-2 py-2 px-4 bg-gray-100 rounded-md font-semibold text-gray-700 mb-2">
            <div className="col-span-2">Imagen</div>
            <div className="col-span-3">Área</div>
            <div className="col-span-2">Estatus</div>
            <div className="col-span-5 text-right">Acciones</div>
          </div>

          {/* Table Content */}
          <div>
            {currentItems.length > 0 ? (
              currentItems.map((space) => (
                <Space key={space.id} {...space} />
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                No se encontraron resultados con la búsqueda "{search}"
              </div>
            )}
          </div>

          {/* Pagination */}
          {filteredData.length > 0 && (
            <div className="flex flex-wrap justify-center mt-4 gap-1">
              <button
                onClick={() => setCurrentPage(0)}
                disabled={currentPage === 0}
                className="p-2 rounded-md hover:bg-blue-100 disabled:opacity-50"
              >
                <i className="fa-solid fa-angles-left"></i>
              </button>
              <button
                onClick={handleClickPrev}
                disabled={currentPage === 0}
                className="p-2 rounded-md hover:bg-blue-100 disabled:opacity-50"
              >
                <i className="fa-solid fa-angle-left"></i>
              </button>

              {/* Mobile pagination - just show current page */}
              <div className="md:hidden flex items-center">
                <span className="mx-2 text-sm">
                  Página {currentPage + 1} de {pageCount}
                </span>
              </div>

              {/* Desktop pagination - show page numbers */}
              <div className="hidden md:flex">
                {Array.from({ length: Math.min(5, pageCount) }, (_, i) => {
                  // Show pages around current page
                  let pageToShow = currentPage - 2 + i;
                  if (currentPage < 2) pageToShow = i;
                  if (currentPage > pageCount - 3) pageToShow = pageCount - 5 + i;
                  if (pageToShow >= 0 && pageToShow < pageCount) {
                    return (
                      <button
                        key={pageToShow}
                        onClick={() => setCurrentPage(pageToShow)}
                        className={`w-10 h-10 rounded-md ${
                          currentPage === pageToShow
                            ? "bg-blue-500 text-white font-bold"
                            : "hover:bg-blue-100"
                        }`}
                      >
                        {pageToShow + 1}
                      </button>
                    );
                  }
                  return null;
                })}
              </div>

              <button
                onClick={handleClickNext}
                disabled={currentPage >= pageCount - 1}
                className="p-2 rounded-md hover:bg-blue-100 disabled:opacity-50"
              >
                <i className="fa-solid fa-angle-right"></i>
              </button>
              <button
                onClick={() => setCurrentPage(pageCount - 1)}
                disabled={currentPage >= pageCount - 1}
                className="p-2 rounded-md hover:bg-blue-100 disabled:opacity-50"
              >
                <i className="fa-solid fa-angles-right"></i>
              </button>
            </div>
          )}

          {/* Add Space Button */}
          <div className="flex justify-center mt-6">
            <button
              onClick={() => dispatch(toggleModalEspacio())}
              className="bg-blue-500 hover:bg-blue-600 text-white rounded-full w-12 h-12 flex items-center justify-center shadow-lg"
            >
              <i className="fa-solid fa-plus text-xl"></i>
            </button>
          </div>
        </div>

        {/* Export and Survey Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8 px-4">
          <button
            className="bg-gray-700 hover:bg-gray-800 text-white py-2 px-4 rounded-md font-medium flex items-center justify-center gap-2 flex-1"
          >
            <i className="fa-solid fa-download"></i>
            Exportar datos de aforo
          </button>
          <button
            className="bg-gray-700 hover:bg-gray-800 text-white py-2 px-4 rounded-md font-medium flex items-center justify-center gap-2 flex-1"
          >
            <i className="fa-solid fa-chart-simple"></i>
            Encuesta de satisfacción
          </button>
        </div>
      </div>

      {/* Modals */}
      <ModalCerrar />
      <ModalEditar />
      <ModalCUSpace />
    </div>
  );
};

export default Inicio;