"use client"

import Spinner from "@/components/shared/Spinner";
import { axiosConfig } from "@/config/axiosConfig";
import clienteAxios from "@/config/clienteAxios";
import { ReservationStatus } from "@/types/ModelTypes";
import { handleError } from "@/utils/errorHandler";
import { useSearchParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import { QrReader } from "react-qr-reader";
import Swal from "sweetalert2";
import { mutate } from "swr";

export default function page() {
  const searchParams = useSearchParams();
  const id = searchParams?.get("id") ?? "0";

  const [loading, setLoading] = useState(false);

  const handleScan = async (reservationURL: string) => {
    // Extraemos el query param id de la url
    const url = new URL(reservationURL);
    const reservationId = url.searchParams.get("id");

    if (!reservationId) {
      Swal.fire({
        title: "Error",
        text: "El código QR no es válido",
        icon: "error",
        confirmButtonText: "Ok",
      });

      return;
    }

    await handleAttendance(parseInt(reservationId));

  };

  const handleAttendance = async (reservationId: number) => {
    try {
      const config = axiosConfig();
      if (!config) return;

      setLoading(true);

      const { data } = await clienteAxios.put(
        `/reservation/updateReservation`,
        { id: reservationId, status: ReservationStatus.APPROVED },
        config
      );

      Swal.fire({
        title: "Éxito",
        text: data.msg,
        icon: "success",
        confirmButtonText: "Ok",
      });
    } catch (error: any) {
      handleError(error);
    } finally {
      setLoading(false);
    }
  };

  // Cada vez que cambie el id y no sea 0 llamamos el handleAttendance
  useEffect(() => {
    if (id !== "0") {
      handleAttendance(parseInt(id));
    }
  }, [id]);

  return (
    <div>
      <h2 className="text-xl my-2 text-center">Leer Código QR</h2>

    <div className="w-full md:w-1/2 mx-auto">

      {loading ? (
        <div className="p-5 rounded-md bg-blue-200 flex flex-col items-center gap-4">
          <p className="text-blue-500 font-bold text-xl">
            QR leído, validando información y marcando asistencia
          </p>
          <Spinner />
        </div>
      ) : (
        <QrReader
          onResult={(result, error) => {
            
            if (!!result) {
              handleScan(result?.getText());
            }
          }}
          containerStyle={{ width: "100%" }}
          constraints={{ facingMode: "environment" }}
          scanDelay={1000}
        />
      )}
      </div>
    </div>
  );
}
