"use client";

import { useState } from "react";
import MaterialList from "@/components/screens/prestamos/MaterialList";
import LoanList from "@/components/screens/prestamos/LoanList";

export default function Prestamos() {
  const [activeTab, setActiveTab] = useState<"materiales" | "prestamos">("materiales");

  return (
    <div className="w-full">
      <div className="w-full grid place-items-center">
        <h2 className="text-3xl text-blue-600 font-bold text-center py-5">
          Préstamo de Materiales
        </h2>
        <div className="h-1 w-full bg-gray-600" />
      </div>

      {/* Tabs */}
      <div className="flex flex-wrap justify-center my-4 gap-2 sm:gap-4">
        <button
          onClick={() => setActiveTab("materiales")}
          className={`px-3 py-2 sm:px-4 rounded-lg shadow-md font-semibold ${
            activeTab === "materiales" ? "bg-blue-600 text-white" : "bg-gray-200"
          }`}
        >
          Materiales
        </button>
        <button
          onClick={() => setActiveTab("prestamos")}
          className={`px-3 py-2 sm:px-4 rounded-lg shadow-md font-semibold ${
            activeTab === "prestamos" ? "bg-blue-600 text-white" : "bg-gray-200"
          }`}
        >
          Préstamos Activos
        </button>
      </div>

      {/* Contenido según la pestaña activa */}
      <div className="w-full px-2 sm:px-4 md:px-6">
        {activeTab === "materiales" ? <MaterialList /> : <LoanList />}
      </div>
    </div>
  );
}
