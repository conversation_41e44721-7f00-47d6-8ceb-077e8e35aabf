import { Modal, ModalProps } from "@/components/UI/Modal";
import { SugestionSearchBar } from "@/components/UI/SugestionsSearchBar";
import { axiosConfig } from "@/config/axiosConfig";
import clienteAxios from "@/config/clienteAxios";
import { fetcher } from "@/config/fetcher";
import useDebounce from "@/hooks/useDebounce";
import { UserInt } from "@/types/ModelTypes";
import { handleError } from "@/utils/errorHandler";
import React, { useState } from "react";
import Swal from "sweetalert2";
import useSWR, { mutate } from "swr";

interface ModalRegisterProps extends Omit<ModalProps, "children"> {
    reservableId: number;
}

export function ModalRegisterUser({ active, setActive,reservableId }: ModalRegisterProps) {
  const [search, setSearch] = useState("");
  const debouncedSearch = useDebounce(search, 1000);

  const { data: usuarios } = useSWR<UserInt[]>(
    `/user/search/${debouncedSearch}`,
    fetcher
  );


  const [selectedUser, setSelectedUser] = useState<UserInt | null>(null);


  const handleReserveUser = async ()=>{
        // Verificamos que haya un usuario seleccionado
        if(!selectedUser) return

        try {
            const config = axiosConfig();
            if(!config){
                throw new Error("Se ha perdido tu sesión, vuelve a iniciar sesión")
            }

            // Hacemos la peticion para reservar
            const {data} = await clienteAxios.post("/reservation/reserve/admin",{
                reservableId,
                userId:selectedUser.id
            },config)


            // Si todo sale bien cerramos el modal
            mutate(`/reservation/all/${reservableId}`)
            Swal.fire("Reservado","El usuario ha sido reservado con exito","success")
            setSelectedUser(null);
            setSearch("");



            
        } catch (error:any) {
            return handleError(error);
        }
  }

  return (
    <Modal active={active} setActive={setActive}>
      <div className="w-full">
        {selectedUser ? (
          <div className="grid grid-cols-2 place-items-center gap-3">
           
                <h2 className="col-span-2 capitalize font-bold text-2xl">{selectedUser.name}</h2>

                <div className="flex flex-col items-center">
                    <p className="text-gray-600">Matricula</p>
                    <p className="text-lg capitalize">{selectedUser.registration}</p>
                </div>
                <div className="flex flex-col items-center">
                    <p className="text-gray-600">Carrera</p>
                    <p className="text-lg capitalize">{selectedUser.program}</p>
                </div>
                <div>
                    <p className="text-gray-600">Genero</p>
                    <p className="text-lg capitalize">{selectedUser.gender}</p>
                </div>
                <div>
                    <p className="text-gray-600">Clave Programa</p>
                    <p className="text-lg capitalize">{selectedUser.program_key}</p>
                </div>

                <button
                    className="text-gray-600 border border-gray-600 px-4 py-2 rounded-md text-lg"
                    onClick={() => {
                        setSelectedUser(null);
                        setSearch("");
                    }}
                >
                    Regresar
                </button>
                <button
                    className="bg-blue-500 text-white font-bold px-4 py-2 rounded-md text-lg"
                    onClick={() => {
                        handleReserveUser()
                    }}
                >
                    Agregar
                </button>
          </div>
        ) : (
          <>
            <h2 className="text-center font-bold text-lg">
              Agregar Alumno a la clase
            </h2>

            <SugestionSearchBar<UserInt>
              sugestions={usuarios || []}
              onSelect={(user) => {
                setSearch("");
                setSelectedUser(user);
              }}
              labelResolver={(user) => user.name}
              search={search}
              setSearch={setSearch}
              placeholder="Buscar Alumnos"
            />
          </>
        )}
      </div>
    </Modal>
  );
}
