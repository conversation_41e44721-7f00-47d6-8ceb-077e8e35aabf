"use client";
import { useModal } from "@/components/UI/Modal";
import { axiosConfig } from "@/config/axiosConfig";
import clienteAxios from "@/config/clienteAxios";
import { fetcher } from "@/config/fetcher";
import { ReservableInt, ReservationInt, ReservationStatus, UserInt } from "@/types/ModelTypes";
import { handleError } from "@/utils/errorHandler";
import { formatearHora } from "@/utils/helpers";

import { GetServerSidePropsContext } from "next";
import Image from "next/image";
import { useParams } from "next/navigation";
import React, { useState } from "react";
import useSWR, { mutate } from "swr";
import { ModalRegisterUser } from "./ModalRegisterUser";
import ModalQRScan from "../ModalQRScan";


interface ReservableQuota extends ReservableInt{
  presentialQuotaString: string;
  onlineQuotaString: string;
}

export default function reservable() {

  // Obtenemos el id de los parametros de la url
  const {id} = useParams();

 
  // Cargamos el reservable y las reservas
  const {data:reservable} = useSWR<ReservableQuota>(`/reservable/getReservable/get/${id}`,fetcher,{
    refreshInterval:3000
  })

  // // Cargamos las reservas de los usuarios
  const {data:userReservations} = useSWR<ReservationInt[]>(reservable ? `/reservation/all/${reservable.id}` : null,fetcher,{
    refreshInterval:3000
  })

  const {active:modalRegister,setActive:setModalRegister} = useModal();

  // // Calculamos las reservaciones completadas
  const completedReservations = userReservations?.filter(reservation => reservation.status === ReservationStatus.APPROVED)

  
  const [qrModal, setQrModal] = useState(false)


  if (id === null) {
    return (
      <div className="min-h-screen grid place-items-center">
        <div>
          <Image
            src="/logo.svg"
            width={300}
            height={300}
            alt="Logo de la aplicacion"
          />
          <h2 className="text-xl text-center">
            Se necesita pasar un id de reservable en la ruta por ejemplo
            /reservable/1
          </h2>
          <p className="text-lg text-center">Reintenta la accion</p>
        </div>
      </div>
    );
  }

  if(!userReservations || !completedReservations || !reservable){
    return (
        <p>Cargando...</p>
    )
  }

  return (
    <>
      <div className="w-full relative h-40 lg:h-48">
        <Image
          src="/images/fondo_reservas.png"
          alt="Fondo de reservas"
          width={1920}
          height={1080}
          className="object-cover object-center w-full h-40 lg:h-48 z-0 absolute top-0 left-0"
        />

        <div className="w-full bg-gray-700 bg-opacity-80 z-10 h-40 lg:h-48 absolute top-0 left-0 grid place-items-center justify-center">
          <h1 className="text-3xl text-white">{reservable?.space?.name}</h1>

          <div className="mt-3 lg:mt-0 flex flex-col lg:flex-row lg:space-x-3 items-center">
            <p className="bg-blue-200 text-blue-500 p-2 rounded-md text-center font-bold text-lg">
              {formatearHora(reservable.init_date)} - {formatearHora(reservable.end_date)}
            </p>
            <p className="font-bold text-lg text-left text-white">
              Cuota Presencial: {reservable.presentialQuotaString}
            </p>
            <p className="font-bold text-lg text-left text-white">
              Cuota Online: {reservable.onlineQuotaString}
            </p>
          </div>
        </div>
      </div>

      <div className="flex items-center ">
        <button
        className="bg-blue-500 text-white p-2 rounded-md ml-4 mt-4 font-bold text-xl"
        onClick={() => setModalRegister(true)}
          type="button"
        >
          Registrar Alumno
        </button>
        <button
        className="bg-blue-500 text-white p-2 rounded-md ml-4 mt-4 font-bold text-xl"
        onClick={() => {
          window.open(`/readqr?id=${id}`, "_blank")
        }}
          type="button"
        >
          Escanear QR
        </button>
      </div>

      <div className="ml-5 mt-6">
        <div className="flex items-center gap-2 mb-4">
          <p className="text-xl text-gray-600 md:text-2xl">
            {completedReservations.length}/{userReservations.length}{" "}
            Participantes
          </p>

          {completedReservations.length === userReservations.length && (
            <p
              className={`p-2 md:p-3 md:text-xl bg-blue-100 text-blue-500 rounded-md`}
            >
              Completo
            </p>
          )}
        </div>
        <div className="flex flex-col gap-3 flex-wrap pb-3 md:h-[70vh] overflow-x-auto">
          
          {[...userReservations]
                .sort((a,b)=>(a.user?.name ?? "").localeCompare(b.user?.name ?? ""))
                .map(reservation => (
                     reservation.user && reservation.user.id ? (
                        
                        <User 
                          key={reservation.id}
                          {...reservation.user}
                          
                          reservation={reservation}
                        />
                       
                        
                       
                      ) : null
                ))}
        </div>
      </div>
      <ModalRegisterUser active={modalRegister} setActive={setModalRegister} reservableId={+id} />
      {/* <ModalQRScan active={qrModal} setActive={setQrModal} reservable={reservable} /> */}
    </>
  );
}

function User({
  name,
  reservation,
}: UserInt & {  reservation: ReservationInt }) {
  console.log("name", reservation)
  async function updateUserReservation(status: ReservationStatus) {
    try {
        const config = axiosConfig();
        if(!config) return;

        await clienteAxios.put(`/reservation/updateReservation`,{id:reservation.id,status},config)

        mutate(`/reservation/all/${reservation.reservableId}`)
        
    } catch (error:any) {
      return handleError(error);
    }
  }

  const traduction = {
    "PRESENTIAL":"Presencial",
    "ONLINE":"Online"
  }

  return (
    <div className={`flex items-center gap-1`}>
      <button
        type="button"
        className={
          `w-7 h-7 text-xs bg-${reservation.status === ReservationStatus.APPROVED ? "green" : "gray"}-500 text-gray-50 rounded-md font-bold fa-solid fa-check`
        }
        onClick={() => {
          updateUserReservation(reservation.status === ReservationStatus.APPROVED ? ReservationStatus.PENDING : ReservationStatus.APPROVED)
        }}
      ></button>
      <button
        type="button"
        className={
          "  text-red-500 rounded-md font-bold fa-solid fa-xmark text-lg"
        }
        onClick={() => {
          updateUserReservation(reservation.status === ReservationStatus.CANCELED ? ReservationStatus.PENDING : ReservationStatus.CANCELED)
        }}
      ></button>

      <p className={`capitalize text-lg ${reservation.status === ReservationStatus.CANCELED ? "line-through":""}`}>{name}</p>
      <p>- {reservation.user?.registration}</p>
      <p className=""> - {traduction[reservation.type]}</p>
    </div>
  );
}
