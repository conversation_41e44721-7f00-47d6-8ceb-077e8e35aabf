import Spinner from "@/components/shared/Spinner";
import { Modal, ModalProps } from "@/components/UI/Modal";
import { axiosConfig } from "@/config/axiosConfig";
import clienteAxios from "@/config/clienteAxios";
import { ReservableInt } from "@/types/ModelTypes";
import { handleError } from "@/utils/errorHandler";
import React, { useState } from "react";
import { QrReader } from "react-qr-reader";
import Swal from "sweetalert2";
import { mutate } from "swr";

interface ModalQRScanProps extends Omit<ModalProps, "children"> {
  reservable: ReservableInt;
}

export default function ModalQRScan({
  reservable,
  ...props
}: ModalQRScanProps) {
  const [loading, setLoading] = useState(false);

  const handleScan = async (userStr: string) => {
    try {
      const config = axiosConfig();
      if (!config) return;

      setLoading(true);

      const { data } = await clienteAxios.post(
        `/user/qrAttendanceScan`,
        {
          qrText: userStr,
        },
        config
      );

      mutate(`/reservation/all/${reservable.id}`);
      mutate(`/reservable/getReservable/get/${reservable.id}`);

      Swal.fire({
        title: "Éxito",
        text: data.msg,
        icon: "success",
        confirmButtonText: "Ok",
      });
    } catch (error: any) {
      handleError(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal {...props}>
      <div>
        <h2 className="text-xl my-2">Leer Código QR</h2>

        {loading ? (
          <div className="p-5 rounded-md bg-blue-200 flex flex-col items-center gap-4">
            <p className="text-blue-500 font-bold text-xl">
              QR leído, validando información y marcando asistencia
            </p>
            <Spinner />
          </div>
        ) : (
          <QrReader
            onResult={(result, error) => {
              console.log(result);
              if (!!result) {
                handleScan(result?.getText());
              }
            }}
            containerStyle={{ width: "100%" }}
            constraints={{ facingMode: "environment" }}
            scanDelay={1000}
          />
        )}
      </div>
    </Modal>
  );
}
