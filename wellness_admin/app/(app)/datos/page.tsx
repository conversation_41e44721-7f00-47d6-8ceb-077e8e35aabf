"use client"

import <PERSON>Header from "@/components/shared/PageHeader";
import Link from "next/link";
import { useState } from "react";
import { DateTime } from "luxon";
import { DateRange } from "react-day-picker";
import clienteAxios from "@/config/clienteAxios";
import { getToken } from "@/config/axiosConfig";
import { handleError } from "@/utils/errorHandler";
import { DatePicker } from "@/components/UI/DatePicker";
import { Button } from "@/components/UI/Button";

export default function page() {
  const today = DateTime.now().startOf("day").toJSDate();

  const [date, setDate] = useState<DateRange | undefined>({
    from: today,
    to: today,
  });

  const [exportProgress, setExportProgress] = useState(false);

  const handleExportReservations = async () => {
    setExportProgress(true);
    try {
      const { data } = await clienteAxios.post(
        "/reservation/downloadReservations",
        {
          startDate: date?.from?.toISOString(),
          endDate: date?.to?.toISOString(),
        },
        {
          responseType: "blob",
          headers: { Authorization: `Bearer ${getToken()}` },
        }
      );

      const url = window.URL.createObjectURL(new Blob([data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "reservaciones.csv");
      document.body.appendChild(link);
      link.click();

      if (link.parentNode) {
        link.parentNode.removeChild(link);
      }
    } catch (error: any) {
      return handleError(error);
    } finally {
      setExportProgress(false); // Ocultar el CircularProgress
    }
  };

  return (
    <div>
      <PageHeader title="Descarga de Datos" image={"/images/borregos.jpg"} />

      <div className="ml-2 mt-2">
      <p className="text-lg">Descargar datos de reservaciones</p>
      <div className="flex items-center gap-2">
        <DatePicker
          date={date}
          setDate={(date) => {
            setDate(date);
          }}
        />

        <Button
          variant="ghost"
          type="button"
          onClick={() => {
            setDate({ from: today, to: today });
          }}
        >
          Hoy
        </Button>
        <Button
          variant="default"
          className="bg-blue-100 text-blue-500 hover:bg-blue-200"
          type="button"
          onClick={() => {
            if(exportProgress || !date?.from || !date?.to) return;
            handleExportReservations()
          }}
          
        >
          {exportProgress ? "Descargando..." : "Descargar"}
        </Button>


      </div>
      </div>
    </div>
  );
}
