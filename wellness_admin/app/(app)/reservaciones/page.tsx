"use client";

import { Skeleton } from "@/components/UI/Skeleton";
import ModalReservables from "@/components/screens/reservaciones/ModalReservables";
import ReservablesCalendar from "@/components/screens/reservaciones/ReservablesCalendar";
import PageLoader from "@/components/shared/PageLoader";
import { fetcher } from "@/config/fetcher";
import { Admins } from "@/types/AppTypes";
import { ReservableInt, SpaceInt } from "@/types/ModelTypes";
import { Switch } from "@headlessui/react";
import React, { useEffect, useState} from "react";
import useSWR, { mutate } from "swr";

export default function Reservaciones() {
  const { data: spaces } = useSWR<SpaceInt[]>("/space/spaces", fetcher);
  const { data: admins } = useSWR<Admins[]>("/admin", fetcher);
  
  const [teacher, setTeacher] = useState("")
  const [space, setSpace] = useState("")
  const [isBooking, setIsBooking] = useState(false)
  const [loading, setLoading] = useState(false);

  // /reservable/getAllReservables?isBooking=true&adminId=1&spaceId=1
  // /reservable/getAllReservables?isBooking=true&spaceId=1
  // /reservable/getAllReservables?isBooking=true
  const url = `/reservable/getAllReservables?isBooking=${isBooking}${teacher?`&adminId=${teacher}`:""}${space?`&spaceId=${space}`:""}`

  // Obtener reservables desde SWR
  const { data: reservables, isValidating } = useSWR<ReservableInt[]>(url, fetcher, {
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
    refreshInterval:2000
  });

  // Simulación retraso tras un cambio 
  useEffect(() => {
    if (isValidating) {
      setLoading(true);
      const timer = setTimeout(() => setLoading(false), 2000);

      return () => clearTimeout(timer);
    }
  }, [isValidating]);   


  return (
    <div>
      {/* componente que indica que la pagina no es posible verla en celular */}
      <NoPhoneScreen />

      {/* Pantalla que ya se muestra si el dispositivo no es tan grande */}

      <div className="hidden md:block">
        {/* Header de la Pagina */}
        <div className="w-full grid place-items-center">
          <h2 className="text-3xl text-blue-600 font-bold text-center py-5">
            Reservaciones
          </h2>
          <div className="h-1 w-full bg-gray-600"></div>
        </div>

        {/* Contenedor principal que contendrá en el lado izquierdo el boton de crear y del lado derecho el calendario */}
          <div className="w-full h-70vh mt-6">
            <div className="flex items-center justify-center gap-4 w-full mb-3 ">
              <div className="flex flex-col items-center gap-2 ">
                <label className=" " htmlFor="profesor">
                  Area
                </label>
                <select 
                value={space}
                onChange={(e)=>setSpace(e.target.value)}
                className="ring-1 ring-gray-500  rounded-md bg-gray-100 shadow-md lg:p-1">
                  <option value={""}>--- Seleccionar ---</option>
                  {spaces?.map((space) => (
                    <option key={space.id} value={space.id}>
                      {space.name}
                    </option>
                  
                  ))}
                  <option value={"natacion"}>Natacion</option>
                </select>
              </div>
              <div className="flex flex-col items-center gap-2 ">
                <label className=" " htmlFor="profesor">
                  Profesor
                </label>
                <select 
                value={teacher}
                onChange={(e)=>setTeacher(e.target.value)}
                className="ring-1 ring-gray-500  rounded-md bg-gray-100 shadow-md lg:p-1">
                  <option value={""}>--- Seleccionar ---</option>
                  {admins?.map((admin) => (
                    <option key={admin.id} value={admin.id}>
                      {admin.name}
                    </option>
                  
                  ))}
                </select>
              </div>

              <div className="flex flex-col items-center gap-2 ">
                <label className=" " htmlFor="repeticion">
                  ¿Es Booking?{" "}
                </label>

                <Switch
                  checked={isBooking}
                  onChange={(e)=>setIsBooking(e)}
                  className={`switch ${
                    isBooking ? "bg-blue-600" : "bg-gray-100"
                  } relative inline-flex h-6 lg:h-8 w-11 lg:w-16 items-center rounded-full`}
                >
                  <span className="sr-only">Es Booking</span>
                  <span
                    className={`${
                      isBooking ? "translate-x-6 lg:translate-x-10" : "translate-x-1"
                    } inline-block h-4 lg:h-5 w-4 lg:w-5 transform rounded-full bg-white transition`}
                  />
                </Switch>
              </div>
            </div>
            <ReservablesCalendar reservables={reservables ?? []} />
          </div>
        
      </div>
      <ModalReservables />
    </div>
  );
}

function NoPhoneScreen() {
  return (
    <div className="min-h-screen grid place-items-center md:hidden">
      <div className="grid place-items-center bg-gray-100 shadow p-4 rounded-lg mx-4">
        <i className="fa-solid fa-triangle-exclamation text-2xl text-yellow-500"></i>
        <h2 className="text-3xl font-bold text-yellow-700 text-center">
          Error
        </h2>
        <p className="text-center">
          Esta pagina solo puede ser visualizada en tablet o dispositivos más
          grandes
        </p>
      </div>
    </div>
  );
}
