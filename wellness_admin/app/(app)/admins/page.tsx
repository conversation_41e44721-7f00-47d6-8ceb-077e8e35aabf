"use client"

import Admin from '@/components/screens/admins/Admin'
import CUAdmin from '@/components/screens/admins/CUAdmin'
import Announce from '@/components/screens/anuncios/Announce'
import CUAnnounce from '@/components/screens/anuncios/CUAnnounce'
import PageHeader from '@/components/shared/PageHeader'
import PageLoader from '@/components/shared/PageLoader'
import { fetcher } from '@/config/fetcher'
import { AdminInt, AnnounceInt } from '@/types/ModelTypes'
import React, { useState } from 'react'
import useSWR from 'swr'

export default function page() {

 const {data:admins} = useSWR<AdminInt[]>("/admin",fetcher)

  const [create, setCreate] = useState(false)


  if(!admins){
    return <PageLoader />
  }



  return (
    <div>

        <PageHeader title='Administradores' image="/images/borregos.jpg" />

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 2xl:max-w-5xl mx-auto place-items-center mt-4 gap-4'>


        {admins.map(admin =>(

            <Admin
                key={admin.id}
                {...admin}
            />
        ))}

            {create && (
                <CUAdmin 
                    setCreate={setCreate}
                    edition={false}                                       
                    id={0}
                    email=''
                    name=''
                    password=''
                    role={""}


                />
            )}

            <button
                onClick={() => setCreate(true)}
                type='button'
                className='grid place-items-center p-9 md:p-12 rounded-lg bg-gray-200 '
            >
                <i className="fa-solid fa-plus text-gray-900 text-4xl md:text-6xl"></i>
            </button>

        </div>

    </div>
  )
}
