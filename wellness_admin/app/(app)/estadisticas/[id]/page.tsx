"use client";

import React from "react";
import PageHeader from "@/components/shared/PageHeader";
import { useState } from "react";
import AreaGraph from "@/components/Graphs/AreaGraph";
import useSWR from "swr";
import { useEffect } from "react";
import { fetcher } from "@/config/fetcher";
import { SpaceInt } from "@/types/ModelTypes";
import { useParams } from "next/navigation";
import { DatePicker } from "@/components/UI/DatePicker";
import { DateTime } from "luxon";
import { DateRange } from "react-day-picker";
import { Button } from "@/components/UI/Button";
import BarraGraph from "@/components/Graphs/BarraGraph";

const Estadisticas: React.FC = () => {
  const { id } = useParams();

  const today = DateTime.now().startOf("day").toJSDate();
  const weekAgo = DateTime.now().minus({ days: 6 }).startOf("day").toJSDate(); // 6 en lugar de 7 para que muestre la grafica por dia y no por semana

  const [date, setDate] = useState<DateRange | undefined>({
    from: today,
    to: today,
  });

  const [groupBy, setGroupBy] = useState<"day" | "week">("day");

  const { data } = useSWR(
    date && id ? `/graphs/reservations?start=${date.from?.toISOString()}&end=${date.to?.toISOString()}&spaceId=${id}&groupBy=${groupBy}` : null,
    fetcher
  );

  return (
    <div className="flex flex-col bg-[#F1F1F1] overflow-x-hidden">
      {/* Parte de arriba */}
      <PageHeader
        title="Estadisticas gimnasio"
        image={"/images/borregos.jpg"}
      />

      <h1 className="text-lg lg:text-3xl xl:text-3xl m-8">
        {" "}
        <strong> Reservaciones hechas por espacio </strong>
      </h1>

      <div className="w-full  mx-8 lg:mx-8 xl:mx-8 flex flex-col md:flex-row">
        <div className="flex items-center gap-2">
        <DatePicker date={date} setDate={setDate} />

          <select
            value={groupBy}
            onChange={(e)=>{
              console.log("Nuevo valor de groupBy:", e.target.value); 
              setGroupBy(e.target.value as "day" | "week");

            }}
            className="border rounded-md p-2"
          >
            <option value="day">Por Día</option>
            <option value="week">Por Semana</option>
          </select>
        </div>
      </div>

      <div className=" mt-10 h-52 mx-5 lg:mx-16 lg:h-96 xl:mx-16 xl:h-96">
        <BarraGraph data={data} />
      </div>
    </div>
  );
};

export default Estadisticas;
