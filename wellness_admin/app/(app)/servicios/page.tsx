import PageHeader from '@/components/shared/PageHeader'
import Link from 'next/link'
import React from 'react'

export default function page() {
  return (
    <div>
        <PageHeader
        title="Servicios Asociados"
        image={"/images/borregos.jpg"}
      />
      <div className='w-full grid place-items-center md:grid-cols-2 gap-4 p-3 max-w-3xl mx-auto'>
            <ServiceCard title='Control de Acceso' link='https://control-de-acceso-pink.vercel.app'/>
            <ServiceCard title='Aplicación de Usuario' link='https://wellness-reservaciones.vercel.app'/>
      </div>
    </div>
  )
}


interface ServiceCardProps {
    title: string;
    link: string;
}

function ServiceCard({title, link}:ServiceCardProps) {
    return (
        <Link 
            target='_blank'
            href={link}
            className='w-full h-40 grid place-items-center bg-blue-200 rounded-lg'>
                <p className='text-blue-500 font-bold text-2xl text-center'>{title}</p>
            </Link>
    )
}