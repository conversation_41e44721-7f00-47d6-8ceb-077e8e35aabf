"use client";

import PageLoader from "@/components/shared/PageLoader";
import { fetcher } from "@/config/fetcher";
import { RootState } from "@/redux/store";
import { loadPerfil } from "@/redux/thunks/authThunk";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { AiOutlineMenu, AiOutlineClose } from "react-icons/ai";
import { useDispatch, useSelector } from "react-redux";
import useSWR from "swr";

interface Props {
  children: React.ReactNode;
}

export default function layout({ children }: Props) {
  const { auth, cargando } = useSelector((state: RootState) => state.auth);
  const [menuIcon, setIcon] = useState(false);

  const dispatch = useDispatch<any>();
  const router = useRouter();

  const { data: nextClass } = useSWR<{ reservableId: number }>(
    "/admin/nextClass",
    fetcher
  );

  const handleSmallerScreensNavigation = () => {
    setIcon(!menuIcon);
  };

  useEffect(() => {
    dispatch(loadPerfil());
  }, []);

  useEffect(() => {
    if (!auth?.id && !cargando) {
      router.push("/login");
    }
  }, [cargando, auth, router]);

  if (cargando) {
    return <PageLoader />;
  }

  return (
    <>
      <div
        className="sticky top-0 border-black-200 h-20 dark:bg-black-900 dark:border-black-700 bg-gray-50"
        style={{ zIndex: 1000 }}
      >
        <div className="relative flex flex-wrap items-center justify-between mx-auto p-4">
          <Link href="/#" className="flex items-center">
            <Image
              src={"/images/borrego-blue.svg"}
              className="h-12 mr-3"
              width={40}
              height={40}
              alt="Flowbite Logo"
            />
            <span className="self-center text-2xl font-semibold whitespace-nowrap dark:text-black">
              Athletics
            </span>
          </Link>
          <div
            className="hidden w-full md:block md:w-auto"
            id="navbar-dropdown"
          >
            <ul className="flex flex-col font-medium p-4 md:p-0 mt-4 border border-white-100 rounded-lg bg-white-50 md:flex-row md:space-x-8 md:mt-0 md:border-0 bg-gray-50 dark:bg-white-800 md:dark:bg-white-900 dark:border-white-700">
              {nextClass && (
                <li>
                  <Link
                    href={`/reservable/${nextClass.reservableId}`}
                    className="block py-2 pl-3 pr-4 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-dark md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent"
                  >
                    Proxima Clase
                  </Link>
                </li>
              )}
              <li>
                <Link
                  href="/"
                  className="block py-2 pl-3 pr-4 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-dark md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent"
                >
                  Inicio
                </Link>
              </li>

              <li>
                <Link
                  href="/anuncios"
                  className="block py-2 pl-3 pr-4 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-dark md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent"
                >
                  Anuncios
                </Link>
              </li>
              <li>
                <Link
                  href="/admins"
                  className="block py-2 pl-3 pr-4 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-dark md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent"
                >
                  Administradores
                </Link>
              </li>
              <li>
                <Link
                  href="/reservaciones"
                  className="block py-2 pl-3 pr-4 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-dark md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent"
                >
                  Reservaciones
                </Link>
              </li>
              <li>
                <Link
                  href="/servicios"
                  className="block py-2 pl-3 pr-4 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-dark md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent"
                >
                  Servicios
                </Link>
              </li>
              <li>
                <Link
                  target="_blank"
                  href="/eventos"
                  className="block py-2 pl-3 pr-4 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-dark md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent"
                >
                  Clases
                </Link>
              </li>
              <li>
                <Link
                  target="_blank"
                  href="/datos"
                  className="block py-2 pl-3 pr-4 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-dark md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent"
                >
                  Datos
                </Link>
              </li>
              <li>
                <Link
                  target="_blank"
                  href="/prestamos"
                  className="block py-2 pl-3 pr-4 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-dark md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent"
                >
                  Prestamos
                </Link>
              </li>
              {auth?.role === "SUPERADMIN" && (


              <li>
                <Link
                  href="/camara"
                  className="block py-2 pl-3 pr-4 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-dark md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent"
                >
                  Cámara
                </Link>
              </li>
              )}
                
            </ul>
          </div>

          {/* Smaller screens - navigation skills */}
          {/* onClick change the icon*/}
          <div
            onClick={handleSmallerScreensNavigation}
            className="flex md:hidden"
          >
            {menuIcon ? (
              <AiOutlineClose size={25} className="text-[#FFFFF]" />
            ) : (
              <AiOutlineMenu size={25} className="text-[#FFFFF]" />
            )}
          </div>
        </div>

        {/* Smaller screens - Navbar*/}
      </div>
      <MobileNavigation
        menuIcon={menuIcon}
        handleSmallerScreensNavigation={handleSmallerScreensNavigation}
        nextClass={nextClass}
      />

      {children}
    </>
  );
}

interface MobileNavigationProps {
  menuIcon: boolean;
  handleSmallerScreensNavigation: () => void;
  nextClass?: { reservableId: number };
}

function MobileNavigation({
  menuIcon,
  handleSmallerScreensNavigation,
  nextClass,
}: MobileNavigationProps) {
  const { auth, cargando } = useSelector((state: RootState) => state.auth);

  return (
    <div
      className={`md:hidden fixed top-20 flex justify-center items-center w-full bg-slate-800 text-white ease-in duration-300 z-50 left-0 ${
        menuIcon ? "" : "translate-x-[100%]"
      }`}
    >
      {/* Smaller screens - Links*/}

      <div className="w-full">
        <ul className="font-bold text-2xl">
          {nextClass && (
            <li
              onClick={handleSmallerScreensNavigation}
              className="pt-5 pb-10 px-4 hover:text-[#CEFF00] cursor-pointer"
            >
              <Link
                href={`/reservable/${nextClass.reservableId}`}
                className="text-white"
              >
                Proxima Clase
              </Link>
            </li>
          )}

          <li
            onClick={handleSmallerScreensNavigation}
            className="pt-5 pb-10 px-4 hover:text-[#CEFF00] cursor-pointer"
          >
            <Link href="/#" className="text-white">
              Inicio
            </Link>
          </li>
          <li
            onClick={handleSmallerScreensNavigation}
            className="py-10 px-4 hover:text-[#CEFF00] cursor-pointer"
          >
            <Link href="/anuncios" className="text-white">
              Anuncios
            </Link>
          </li>
          <li
            onClick={handleSmallerScreensNavigation}
            className="py-10 px-4 hover:text-[#CEFF00] cursor-pointer"
          >
            <Link href="/admins" className="text-white">
              Administradores
            </Link>
          </li>
          <li
            onClick={handleSmallerScreensNavigation}
            className="py-10 px-4 hover:text-[#CEFF00] cursor-pointer"
          >
            <Link href="/#" className="text-white">
              Reservaciones
            </Link>
          </li>
          <li
            onClick={handleSmallerScreensNavigation}
            className="py-10 px-4 hover:text-[#CEFF00] cursor-pointer"
          >
            <Link href="/servicios" className="text-white">
              Servicios
            </Link>
          </li>
          <li
            onClick={handleSmallerScreensNavigation}
            className="py-10 px-4 hover:text-[#CEFF00] cursor-pointer"
          >
            <Link href="/eventos" className="text-white">
              Clases
            </Link>
          </li>
          <li>
            <Link
              target="_blank"
              href="/prestamos"
              className="block py-2 pl-3 pr-4 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-dark md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent"
            >
              Prestamos
            </Link>
          </li>
          {auth?.role === "SUPERADMIN" && (
            <li
              onClick={handleSmallerScreensNavigation}
              className="py-10 px-4 hover:text-[#CEFF00] cursor-pointer"
            >
              <Link href="/camara" className="text-white">
                Cámara
              </Link>
            </li>
          )}
        </ul>
      </div>
    </div>
  );
}
