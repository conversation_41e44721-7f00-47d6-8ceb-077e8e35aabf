"use client";

import Announce from "@/components/screens/anuncios/Announce";
import CUAnnounce from "@/components/screens/anuncios/CUAnnounce";
import PageHeader from "@/components/shared/PageHeader";
import PageLoader from "@/components/shared/PageLoader";
import { fetcher } from "@/config/fetcher";
import { CarouselImageInt } from "@/types/ModelTypes";
import React, { useState } from "react";
import useSWR from "swr";
import Link from "next/link";
import Swal from "sweetalert2";
import clienteAxios from "@/config/clienteAxios";
import ImageUploader from "@/components/forms/ImageUploader";
import Image from "next/image";
import { axiosConfig } from "@/config/axiosConfig";
import { handleError } from "@/utils/errorHandler";

export default function page() {
  const { data: announces, mutate } = useSWR<CarouselImageInt[]>(
    "/admin/carouselImages",
    fetcher
  );

  const [selected, setSelected] = useState("imagen");
  const [image, setImage] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);

  if (!announces) {
    return <PageLoader />;
  }


  async function handleUpdate(id: number,order:number) {
    console.log(id,order)
    try {
      const config = axiosConfig();

      if (!config) {
        return Swal.fire(
          "Error",
          "Porfavor recarga la pagina, se ha perdido tu sesion",
          "error"
        );
      }


      await clienteAxios.put(`/admin/updateCarouselImage`,{
        id,
        order
      }, config);

      await Swal.fire("Exito", "Imagen actualizada correctamente", "success");

      mutate();

  
    } catch (error: any) {
      return handleError(error);
    }
  }

  async function handleDelete(id: number) {
    try {
      const config = axiosConfig();

      if (!config) {
        return Swal.fire(
          "Error",
          "Porfavor recarga la pagina, se ha perdido tu sesion",
          "error"
        );
      }

      const { isConfirmed } = await Swal.fire({
        title: "¿Estás seguro?",
        text: "Esta acción no se puede deshacer",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#d33",
        cancelButtonColor: "#3085d6",
        confirmButtonText: "Eliminar",
        cancelButtonText: "Cancelar",
      });

      if (!isConfirmed) return;

      await clienteAxios.delete(`/admin/deleteCarouselImage/${id}`, config);

      mutate();

      await Swal.fire("Exito", "Imagen eliminada correctamente", "success");
    } catch (error: any) {
      return handleError(error);
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const config = axiosConfig(false);

      if (!config) {
        return Swal.fire(
          "Error",
          "Porfavor recarga la pagina, se ha perdido tu sesion",
          "error"
        );
      }

      if (!image)
        return Swal.fire("Error", "Debes seleccionar una imagen", "error");

      const formData = new FormData();
      formData.append("image", image);

      await clienteAxios.post("/admin/uploadCarouselImage", formData, config);
      Swal.fire("Exito", "Imagen cargada correctamente", "success");
      setImage(null);
      setPreview(null);
      mutate();
    } catch (error: any) {
      return handleError(error);
    }
  };

  return (
    <div>
      <PageHeader title="Agregar Imagen" image="/images/borregos.jpg" />

      <div className="flex justify-center gap-8 mt-4">
        <Link
          href="/anuncios/"
          className={`py-2 px-4 text-gray-900 rounded-md hover:bg-gray-100 ${
            selected === "anuncio"
              ? "border-b-2 border-blue-500"
              : "md:hover:bg-transparent"
          }`}
          onClick={() => setSelected("anuncio")}
        >
          Anuncio
        </Link>
        <Link
          href="/anuncios/imagen"
          className={`py-2 px-4 text-gray-900 rounded-md hover:bg-gray-100 ${
            selected === "imagen"
              ? "border-b-2 border-blue-500"
              : "md:hover:bg-transparent"
          }`}
          onClick={() => setSelected("imagen")}
        >
          Agregar Imagen
        </Link>
      </div>

      <div className="flex items-center gap-3 mx-auto w-2/3">
        {announces.map((announce) => (
          <div className="relative w-1/4" key={announce.id}>
            <Image
              
              src={announce.url}
              alt={""}
              width={300}
              height={200}
              objectFit="cover"
              className="rounded-lg "
            />
            <div className="flex items-center justify-between absolute bottom-0 left-0 w-full">
              <button
                type="button"
                className="fas fa-trash text-red-500 absolute top-0 right-0"
                onClick={() => handleDelete(announce.id)}
              ></button>
              <select value={announce.order} className="" onChange={(e)=>{
                handleUpdate(announce.id,parseInt(e.target.value))
              }}>
                {announces.map((announce) => (
                  <option key={announce.id} value={announce.order}>
                    {announce.order}
                  </option>
                ))}
              </select>
            </div>
          </div>
        ))}
        <div className="flex flex-col items-center">
          <ImageUploader
            onUpload={(dataUrl, file) => {
              setImage(file);
              setPreview(dataUrl);
            }}
            previewImage={
              preview ? { file: image as File, preview: preview } : null
            }
            setPreviewImage={(preview) => {
              setImage(preview?.file || null);
              setPreview(preview?.preview || null);
            }}
          />

          <button
            onClick={handleSubmit}
            className="bg-blue-500 text-white px-4 py-2 rounded-md mt-4"
          >
            Subir Imagen
          </button>
        </div>
      </div>
    </div>
  );
}
