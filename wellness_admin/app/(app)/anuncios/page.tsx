"use client";

import Announce from "@/components/screens/anuncios/Announce";
import CUAnnounce from "@/components/screens/anuncios/CUAnnounce";
import PageHeader from "@/components/shared/PageHeader";
import PageLoader from "@/components/shared/PageLoader";
import { fetcher } from "@/config/fetcher";
import { AnnounceInt } from "@/types/ModelTypes";
import React, { useState } from "react";
import useSWR from "swr";

import Link from "next/link";


export default function page() {
  const { data: announces } = useSWR<AnnounceInt[]>("/announce/all", fetcher);

  const [create, setCreate] = useState(false);
  const currenteISODate = new Date().toISOString();
  const [selected, setSelected] = useState("anuncio");

  if (!announces) {
    return <PageLoader />;
  }

  return (
    <div>
      <PageHeader title="Anuncios" image="/images/borregos.jpg" />

      <div className="flex justify-center gap-8 mt-4">
        <Link
          href="/anuncios/"
          className={`py-2 px-4 text-gray-900 rounded-md hover:bg-gray-100 ${
            selected === "anuncio"
              ? "border-b-2 border-blue-500"
              : "md:hover:bg-transparent"
          }`}
          onClick={() => setSelected("anuncio")}
        >
          Anuncio
        </Link>
        <Link
          href="/anuncios/imagen"
          className={`py-2 px-4 text-gray-900 rounded-md hover:bg-gray-100 ${
            selected === "imagen"
              ? "border-b-2 border-blue-500"
              : "md:hover:bg-transparent"
          }`}
          onClick={() => setSelected("imagen")}
        >
          Agregar Imagen
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 2xl:max-w-5xl mx-auto place-items-center mt-4 gap-4">
        {announces.map((announce) => (
          <Announce key={announce.id} {...announce} />
        ))}
        

        {create && (
          <CUAnnounce
            setCreate={setCreate}
            edition={false}
            end_date={currenteISODate}
            event_date={currenteISODate}
            init_date={currenteISODate}
            title=""
            id={0}
          />
        )}

        <button
          onClick={() => setCreate(true)}
          type="button"
          className="grid place-items-center p-9 md:p-12 rounded-lg bg-gray-200 "
        >
          <i className="fa-solid fa-plus text-gray-900 text-4xl md:text-6xl"></i>
        </button>
      </div>
    </div>
  );
}
