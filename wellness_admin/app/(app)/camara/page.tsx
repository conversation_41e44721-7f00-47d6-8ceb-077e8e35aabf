"use client";

import React, { useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";

import PageHeader from "@/components/shared/PageHeader";
import CameraWebSocketService, { ConnectionStatus, ConfigUpdate, PerformanceMetrics } from "@/services/cameraWebSocketService";
import { RootState } from "@/redux/store";

const CameraMonitoring: React.FC = () => {
  const { auth } = useSelector((state: RootState) => state.auth);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    server: false,
    camera: false,
  });
  const [currentFrame, setCurrentFrame] = useState<string | null>(null);
  const [isProcessedFrame, setIsProcessedFrame] = useState<boolean>(false);
  const [showDebugInfo, setShowDebugInfo] = useState<boolean>(false);
  const [counterData, setCounterData] = useState<{
    totalExits: number;
    currentCount: number;
    lastUpdate: Date | null;
  }>({
    totalExits: 0,
    currentCount: 0,
    lastUpdate: null,
  });
  const [lastFrameTime, setLastFrameTime] = useState<Date | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showConfig, setShowConfig] = useState<boolean>(false);
  const [showPerformance, setShowPerformance] = useState<boolean>(false);
  const [isUpdating, setIsUpdating] = useState<boolean>(false);
  const [queueSize, setQueueSize] = useState<number>(0);
  const [config, setConfig] = useState<ConfigUpdate["config"] | null>(null);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics | null>(null);
  const [configForm, setConfigForm] = useState<{
    camera: {
      frame_rate: string;
      resize_width: string;
      resize_height: string;
      frame_skip: string;
    };
    processor: {
      confidence: string;
      iou: string;
      frame_rate: string;
      line_position_percent: string;
    };
  }>({
    camera: {
      frame_rate: "3",
      resize_width: "600",
      resize_height: "400",
      frame_skip: "2"
    },
    processor: {
      confidence: "0.4",
      iou: "0.45",
      frame_rate: "15",
      line_position_percent: "50"
    }
  });

  const wsServiceRef = useRef<CameraWebSocketService | null>(null);

  // Function to connect WebSocket
  const connectWebSocket = () => {
    // Create WebSocket service
    const wsService = new CameraWebSocketService(`admin-${auth?.id || "unknown"}`, {
      onConnect: () => {
        console.log("WebSocket connected");
        setError(null);
        setSuccess(null);
      },
      onDisconnect: () => {
        console.log("WebSocket disconnected");
        setSuccess(null);
      },
      onFrame: (frame, processed) => {
        console.log("Received frame in admin client, processed:", processed ? "yes" : "no");
        setCurrentFrame(frame);
        setIsProcessedFrame(!!processed);
        setLastFrameTime(new Date());
      },
      onStatus: (status) => {
        setConnectionStatus(status);
      },
      onCounterData: (data) => {
        setCounterData(data);
      },
      onConfigUpdate: (newConfig) => {
        console.log("Received configuration update:", newConfig);
        setConfig(newConfig);

        // Reset updating state
        setIsUpdating(false);

        // Show success message
        setSuccess("Configuración actualizada correctamente");
        // Clear success message after 3 seconds
        setTimeout(() => {
          setSuccess(null);
        }, 3000);

        // Update form values
        setConfigForm({
          camera: {
            frame_rate: newConfig.camera.frame_rate.toString(),
            resize_width: newConfig.camera.resize_width.toString(),
            resize_height: newConfig.camera.resize_height.toString(),
            frame_skip: newConfig.camera.frame_skip.toString()
          },
          processor: {
            confidence: newConfig.processor.confidence.toString(),
            iou: newConfig.processor.iou.toString(),
            frame_rate: newConfig.processor.frame_rate.toString(),
            line_position_percent: newConfig.processor.line_position_percent.toString()
          }
        });
      },
      onQueueUpdate: (size) => {
        setQueueSize(size);
      },
      onPerformanceUpdate: (metrics) => {
        console.log("Received performance metrics update:", metrics);
        setPerformanceMetrics(metrics);
      },
      onError: (errorMsg) => {
        console.error("WebSocket error:", errorMsg);
        setError(errorMsg);
        setIsUpdating(false);
      }
    });

    // Connect to WebSocket
    wsService.connect();
    wsServiceRef.current = wsService;
  };

  // Connect to WebSocket
  useEffect(() => {
    connectWebSocket();

    // Cleanup on unmount
    return () => {
      if (wsServiceRef.current) {
        wsServiceRef.current.disconnect();
      }
    };
  }, [auth?.id]);

  return (
    <div className="container mx-auto px-4 py-8">
      <PageHeader
      image="/images/borregos.jpg"
      title="Monitoreo de Cámara" />

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          <p>{success}</p>
        </div>
      )}



      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Video Feed */}
        <div className="lg:col-span-2 bg-white rounded-lg shadow-md p-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">Video en Tiempo Real</h2>
            <div className="flex space-x-2">
              <button
                onClick={() => setShowConfig(!showConfig)}
                className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm"
              >
                {showConfig ? "Ocultar Configuración" : "Configuración"}
              </button>
              <button
                onClick={() => setShowPerformance(!showPerformance)}
                className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm"
              >
                {showPerformance ? "Ocultar Rendimiento" : "Rendimiento"}
              </button>
              <button
                onClick={() => setShowDebugInfo(!showDebugInfo)}
                className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-3 py-1 rounded text-sm"
              >
                {showDebugInfo ? "Ocultar Debug" : "Mostrar Debug"}
              </button>
            </div>
          </div>
          <div className="relative aspect-video bg-gray-200 rounded overflow-hidden">
            {currentFrame ? (
              <>
                {/* Usar un elemento img en lugar de Image de Next.js para depuración */}
                <img
                  src={`data:image/jpeg;base64,${currentFrame}`}
                  alt="Video feed"
                  className="absolute inset-0 w-full h-full object-contain"
                  onError={(e) => {
                    console.error("Error loading image:", e);
                    (e.target as HTMLImageElement).style.display = 'none';
                  }}
                  onLoad={() => {
                    console.log("Image loaded successfully");
                  }}
                />
                <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 text-xs rounded flex items-center">
                  <span className="mr-2">
                    Frame {isProcessedFrame ? 'procesado' : 'sin procesar'}: {lastFrameTime?.toLocaleTimeString() || 'N/A'}
                  </span>

                  {/* Indicador de retraso */}
                  {queueSize > 0 && (
                    <span className={`mr-2 px-1.5 py-0.5 rounded ${
                      queueSize > 5 ? 'bg-red-500' :
                      queueSize > 2 ? 'bg-yellow-500' : 'bg-green-500'
                    }`} title="Frames pendientes en cola">
                      {queueSize} {queueSize > 5 ? '⚠️' : ''}
                    </span>
                  )}

                  <button
                    onClick={() => {
                      if (wsServiceRef.current) {
                        wsServiceRef.current.syncStream();
                        setSuccess("Transmisión sincronizada");
                        setTimeout(() => setSuccess(null), 2000);
                      }
                    }}
                    className={`bg-blue-500 hover:bg-blue-600 text-white text-xs px-2 py-1 rounded ${
                      queueSize > 2 ? 'animate-pulse' : ''
                    }`}
                    title="Sincronizar transmisión (descartar frames antiguos)"
                  >
                    {queueSize > 5 ? 'Sincronizar Ahora' : 'Sincronizar'}
                  </button>
                </div>
              </>
            ) : (
              <div className="absolute inset-0 flex flex-col items-center justify-center">
                <p className="text-gray-500">Esperando transmisión de video...</p>
                <p className="text-gray-400 text-sm mt-2">
                  {connectionStatus.server && connectionStatus.camera ?
                    "Conexiones activas, esperando frames..." :
                    "Verificando conexiones..."}
                </p>
              </div>
            )}
          </div>

          {/* Performance Metrics Panel */}
          {showPerformance && performanceMetrics && (
            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded">
              <h3 className="text-lg font-semibold mb-3">Métricas de Rendimiento</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Camera Metrics */}
                <div>
                  <h4 className="text-md font-semibold mb-2 text-green-700">Cliente de Cámara</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">FPS Actual:</span>
                      <span className="text-sm font-mono">{performanceMetrics.camera.fps.toFixed(2)} fps</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Ancho de Banda:</span>
                      <span className="text-sm font-mono">{(performanceMetrics.camera.bandwidth / 1024).toFixed(2)} KB/s</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Tamaño de Frame:</span>
                      <span className="text-sm font-mono">{performanceMetrics.camera.frame_size.width}x{performanceMetrics.camera.frame_size.height}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Tiempo de Captura:</span>
                      <span className="text-sm font-mono">{(performanceMetrics.camera.avg_capture_time * 1000).toFixed(2)} ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Tiempo de Envío:</span>
                      <span className="text-sm font-mono">{(performanceMetrics.camera.avg_send_time * 1000).toFixed(2)} ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Última Actualización:</span>
                      <span className="text-sm font-mono">{new Date(performanceMetrics.camera.last_update).toLocaleTimeString()}</span>
                    </div>
                  </div>
                </div>

                {/* Processor Metrics */}
                <div>
                  <h4 className="text-md font-semibold mb-2 text-green-700">Cliente de Procesamiento</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">FPS Actual:</span>
                      <span className="text-sm font-mono">{performanceMetrics.processor.fps.toFixed(2)} fps</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Capacidad Máxima:</span>
                      <span className="text-sm font-mono">{performanceMetrics.processor.max_fps_capacity.toFixed(2)} fps</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Tiempo de Procesamiento:</span>
                      <span className="text-sm font-mono">{(performanceMetrics.processor.avg_processing_time * 1000).toFixed(2)} ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">Última Actualización:</span>
                      <span className="text-sm font-mono">{new Date(performanceMetrics.processor.last_update).toLocaleTimeString()}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-4">
                <h4 className="text-md font-semibold mb-2 text-green-700">Recomendaciones</h4>
                <div className="bg-white p-3 rounded border border-green-200 text-sm">
                  {performanceMetrics.camera.fps > performanceMetrics.processor.max_fps_capacity ? (
                    <p className="text-red-600">
                      ⚠️ La cámara está enviando más frames ({performanceMetrics.camera.fps.toFixed(2)} fps) de los que el procesador puede manejar ({performanceMetrics.processor.max_fps_capacity.toFixed(2)} fps).
                      Considera reducir el frame rate de la cámara o aumentar el frame skip.
                    </p>
                  ) : (
                    <p className="text-green-600">
                      ✅ El sistema está funcionando de manera óptima. La cámara envía {performanceMetrics.camera.fps.toFixed(2)} fps y el procesador puede manejar hasta {performanceMetrics.processor.max_fps_capacity.toFixed(2)} fps.
                    </p>
                  )}

                  {performanceMetrics.camera.bandwidth > 500 * 1024 && (
                    <p className="text-yellow-600 mt-2">
                      ⚠️ El ancho de banda utilizado es alto ({(performanceMetrics.camera.bandwidth / 1024).toFixed(2)} KB/s).
                      Considera reducir la resolución o el frame rate si experimentas retrasos.
                    </p>
                  )}

                  <p className="mt-2">
                    Frame rate recomendado para la cámara: <strong>{Math.min(performanceMetrics.processor.max_fps_capacity * 0.8, 15).toFixed(1)} fps</strong>
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Configuration Panel */}
          {showConfig && (
            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded">
              <h3 className="text-lg font-semibold mb-3">Configuración del Sistema</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Camera Client Configuration */}
                <div>
                  <h4 className="text-md font-semibold mb-2 text-blue-700">Cliente de Cámara</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Velocidad de Captura (FPS)</label>
                      <div className="mt-1 flex items-center">
                        <input
                          type="number"
                          min="1"
                          max="30"
                          value={configForm.camera.frame_rate}
                          onChange={(e) => {
                            const newFrameRate = e.target.value;
                            setConfigForm({
                              ...configForm,
                              camera: {
                                ...configForm.camera,
                                frame_rate: newFrameRate
                              },
                              // Actualizar automáticamente el frame rate del procesador si es menor
                              processor: {
                                ...configForm.processor,
                                frame_rate: parseFloat(newFrameRate) > parseFloat(configForm.processor.frame_rate)
                                  ? newFrameRate
                                  : configForm.processor.frame_rate
                              }
                            });
                          }}
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        />
                        <div className="ml-2 text-xs text-gray-500">
                          <span className="block">Controla cuántos frames por segundo captura la cámara.</span>
                          <span className="block">Valores más altos consumen más ancho de banda.</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Ancho de Imagen</label>
                      <input
                        type="number"
                        min="320"
                        max="1920"
                        step="80"
                        value={configForm.camera.resize_width}
                        onChange={(e) => setConfigForm({
                          ...configForm,
                          camera: {
                            ...configForm.camera,
                            resize_width: e.target.value
                          }
                        })}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Alto de Imagen</label>
                      <input
                        type="number"
                        min="240"
                        max="1080"
                        step="80"
                        value={configForm.camera.resize_height}
                        onChange={(e) => setConfigForm({
                          ...configForm,
                          camera: {
                            ...configForm.camera,
                            resize_height: e.target.value
                          }
                        })}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Salto de Frames</label>
                      <input
                        type="number"
                        min="1"
                        max="10"
                        value={configForm.camera.frame_skip}
                        onChange={(e) => setConfigForm({
                          ...configForm,
                          camera: {
                            ...configForm.camera,
                            frame_skip: e.target.value
                          }
                        })}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>
                  </div>
                </div>

                {/* Processing Client Configuration */}
                <div>
                  <h4 className="text-md font-semibold mb-2 text-blue-700">Cliente de Procesamiento</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Umbral de Confianza</label>
                      <input
                        type="number"
                        min="0.1"
                        max="0.9"
                        step="0.05"
                        value={configForm.processor.confidence}
                        onChange={(e) => setConfigForm({
                          ...configForm,
                          processor: {
                            ...configForm.processor,
                            confidence: e.target.value
                          }
                        })}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Umbral IoU</label>
                      <input
                        type="number"
                        min="0.1"
                        max="0.9"
                        step="0.05"
                        value={configForm.processor.iou}
                        onChange={(e) => setConfigForm({
                          ...configForm,
                          processor: {
                            ...configForm.processor,
                            iou: e.target.value
                          }
                        })}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Velocidad de Procesamiento (FPS)</label>
                      <div className="mt-1 flex items-center">
                        <input
                          type="number"
                          min="1"
                          max="30"
                          value={configForm.processor.frame_rate}
                          onChange={(e) => setConfigForm({
                            ...configForm,
                            processor: {
                              ...configForm.processor,
                              frame_rate: e.target.value
                            }
                          })}
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        />
                        <div className="ml-2 text-xs text-gray-500">
                          <span className="block">Nota: Este valor debe ser mayor o igual al de la cámara</span>
                          <span className="block">para procesar todos los frames recibidos.</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Posición de Línea (%)</label>
                      <div className="mt-1 flex items-center">
                        <input
                          type="number"
                          min="10"
                          max="90"
                          value={configForm.processor.line_position_percent}
                          onChange={(e) => setConfigForm({
                            ...configForm,
                            processor: {
                              ...configForm.processor,
                              line_position_percent: e.target.value
                            }
                          })}
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        />
                        <div className="ml-2 text-xs text-gray-500">
                          <span className="block">Posición vertical de la línea de conteo (% de la altura).</span>
                          <span className="block">50% = centro, valores menores = más arriba.</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-4 flex justify-end space-x-3">
                <button
                  onClick={() => {
                    if (config) {
                      setConfigForm({
                        camera: {
                          frame_rate: config.camera.frame_rate.toString(),
                          resize_width: config.camera.resize_width.toString(),
                          resize_height: config.camera.resize_height.toString(),
                          frame_skip: config.camera.frame_skip.toString()
                        },
                        processor: {
                          confidence: config.processor.confidence.toString(),
                          iou: config.processor.iou.toString(),
                          frame_rate: config.processor.frame_rate.toString(),
                          line_position_percent: config.processor.line_position_percent.toString()
                        }
                      });
                    }
                  }}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
                >
                  Cancelar
                </button>
                <button
                  onClick={() => {
                    if (wsServiceRef.current) {
                      // Set updating state
                      setIsUpdating(true);
                      setError(null);

                      const newConfig = {
                        camera: {
                          frame_rate: parseFloat(configForm.camera.frame_rate),
                          resize_width: parseInt(configForm.camera.resize_width),
                          resize_height: parseInt(configForm.camera.resize_height),
                          frame_skip: parseInt(configForm.camera.frame_skip)
                        },
                        processor: {
                          confidence: parseFloat(configForm.processor.confidence),
                          iou: parseFloat(configForm.processor.iou),
                          frame_rate: parseInt(configForm.processor.frame_rate),
                          line_position_percent: parseFloat(configForm.processor.line_position_percent),
                          // Keep existing tracker settings if available
                          tracker: config?.processor.tracker || {
                            track_thresh: 0.4,
                            track_buffer: 50,
                            match_thresh: 0.65,
                            aspect_ratio_thresh: 1.6,
                            min_box_area: 10
                          }
                        }
                      };

                      wsServiceRef.current.updateConfig(newConfig);

                      // Reset updating state after 5 seconds if no response
                      setTimeout(() => {
                        setIsUpdating(false);
                      }, 5000);
                    }
                  }}
                  disabled={isUpdating}
                  className={`px-4 py-2 rounded ${isUpdating
                    ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                    : 'bg-blue-500 text-white hover:bg-blue-600'}`}
                >
                  {isUpdating ? 'Actualizando...' : 'Guardar Cambios'}
                </button>
              </div>
            </div>
          )}

          {/* Debug Information */}
          {showDebugInfo && currentFrame && (
            <div className="mt-4 p-4 bg-gray-100 rounded overflow-auto max-h-40">
              <h3 className="text-md font-semibold mb-2">Información de Depuración</h3>
              <div className="text-xs font-mono">
                <p>Longitud del frame: {currentFrame.length} caracteres</p>
                <p>Primeros 50 caracteres: {currentFrame.substring(0, 50)}...</p>
                <p>Último frame recibido: {lastFrameTime?.toISOString() || 'N/A'}</p>
                <p>Tipo de frame: {isProcessedFrame ? 'Procesado (con anotaciones)' : 'Sin procesar (raw)'}</p>
                <p>Estado de conexión: Servidor {connectionStatus.server ? '✅' : '❌'}, Cámara {connectionStatus.camera ? '✅' : '❌'}</p>
                <button
                  onClick={() => {
                    try {
                      // Intentar cargar la imagen manualmente
                      const img = new Image();
                      img.onload = () => alert("La imagen se cargó correctamente");
                      img.onerror = (e) => alert("Error al cargar la imagen: " + e);
                      img.src = `data:image/jpeg;base64,${currentFrame}`;
                    } catch (e) {
                      alert("Error al crear la imagen: " + e);
                    }
                  }}
                  className="mt-2 bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded text-xs"
                >
                  Probar carga de imagen
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Status and Statistics */}
        <div className="bg-white rounded-lg shadow-md p-4">
          <h2 className="text-xl font-bold mb-4">Estado del Sistema</h2>

          {/* Connection Status */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Estado de Conexión</h3>
            <div className="space-y-2">
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-2 ${connectionStatus.server ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span>Servidor: {connectionStatus.server ? 'Conectado' : 'Desconectado'}</span>
              </div>
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-2 ${connectionStatus.camera ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span>Cámara: {connectionStatus.camera ? 'Conectada' : 'Desconectada'}</span>
              </div>
              <div className="space-y-2 mt-2">
                <button
                  onClick={() => {
                    if (wsServiceRef.current) {
                      wsServiceRef.current.disconnect();
                    }
                    setTimeout(() => {
                      connectWebSocket();
                    }, 1000);
                  }}
                  className="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm"
                >
                  Reconectar
                </button>
                <button
                  onClick={() => {
                    // Forzar limpieza del frame actual
                    setCurrentFrame(null);
                    setLastFrameTime(null);

                    // Reconectar después de un breve retraso
                    if (wsServiceRef.current) {
                      wsServiceRef.current.disconnect();
                    }
                    setTimeout(() => {
                      connectWebSocket();
                    }, 1000);
                  }}
                  className="w-full bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded text-sm"
                >
                  Reiniciar Video
                </button>

                <button
                  onClick={() => {
                    if (wsServiceRef.current) {
                      wsServiceRef.current.syncStream();
                      setSuccess("Transmisión sincronizada");
                      setTimeout(() => setSuccess(null), 2000);
                    }
                  }}
                  className={`w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm mt-2 ${
                    queueSize > 2 ? 'animate-pulse' : ''
                  }`}
                >
                  {queueSize > 0 ? (
                    <span>
                      Sincronizar ({queueSize} frames en cola)
                      {queueSize > 5 && <span className="ml-1">⚠️</span>}
                    </span>
                  ) : (
                    "Sincronizar (Volver a En Vivo)"
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Counter Statistics */}
          <div>
            <h3 className="text-lg font-semibold mb-2">Estadísticas</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Total de salidas:</span>
                <span className="font-bold">{counterData.totalExits}</span>
              </div>
              <div className="flex justify-between">
                <span>Conteo actual:</span>
                <span className="font-bold">{counterData.currentCount}</span>
              </div>
              <div className="flex justify-between">
                <span>Última actualización:</span>
                <span className="font-bold">
                  {counterData.lastUpdate
                    ? counterData.lastUpdate.toLocaleTimeString()
                    : 'N/A'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Último frame recibido:</span>
                <span className="font-bold">
                  {lastFrameTime
                    ? lastFrameTime.toLocaleTimeString()
                    : 'N/A'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CameraMonitoring;
