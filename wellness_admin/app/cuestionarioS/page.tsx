"use client";

import PageHeader from '@/components/shared/PageHeader'
import <PERSON>Loader from '@/components/shared/PageLoader'
import useSWR from "swr";
import React, { useEffect, useState } from "react";
import { fetcher } from "@/config/fetcher";
import { AiOutlineMenu, AiOutlineClose } from "react-icons/ai";
import { SpaceInt } from "@/types/ModelTypes";

const CuestionarioSPage = () => {
    const { data } = useSWR<SpaceInt[]>(`/space/spaces`, fetcher); //Cambiar al de esta pagina UTILIZANDO EL DE INICIO
    const [filteredData, setFilteredData] = useState<SpaceInt[]>([]);
    const [search, setSearch] = useState<string>("");

    useEffect(() => {
        if(!data) return;
        if (search === "") setFilteredData(data);
        else
          setFilteredData(
            data.filter((space) =>
              space.name.toLowerCase().trim().includes(search.toLowerCase().trim())
            )
          );
    
      }, [data, search]);

  const [isDropdownOpen, setDropdownOpen] = useState(false);
  const [alumnos, setAlumnos] = useState([
    { matricula: "12345", nombre: "Juan Pérez", cuestionarioLlenado: true },
  ]);

  const agregarAlumno = () => {
    const nuevoAlumno = {
      matricula: "67890",
      nombre: "Ana López",
      cuestionarioLlenado: false,
    };
    setAlumnos((prevAlumnos) => [...prevAlumnos, nuevoAlumno]);
  };

  const toggleDropdown = () => {
    setDropdownOpen(!isDropdownOpen);
  };

  return (
    <div>
      <PageHeader title="Cuestionario Salud" image="/images/borregos.jpg" />

      <div className="mt-4 gap-4 px-6">
        <div className="mt-4 flex justify-start items-center gap-5">
          <input
            type="text"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="focus-within:outline-blue-500 w-full max-w-sm border-2 rounded-md pe-4 ps-2 py-2 caret-blue-500"
            placeholder="Buscar..."
          />

          <div className="relative">
            <button
              onClick={toggleDropdown}
              className="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition duration-300"
            >
              Select
            </button>
            {isDropdownOpen && (
              <ul className="absolute left-0 mt-2 w-48 bg-white shadow-lg rounded-md">
                <li className="px-4 py-2 hover:bg-gray-100 cursor-pointer">Opción 1</li>
                <li className="px-4 py-2 hover:bg-gray-100 cursor-pointer">Opción 2</li>
                <li className="px-4 py-2 hover:bg-gray-100 cursor-pointer">Opción 3</li>
              </ul>
            )}
          </div>
        </div>

            
          

          {/* Tabla Alumnos*/}
          <div className="mt-6 flex justify-center">
            <div className="w-full max-w-7xl">
              <table className="w-full border-collapse bg-white shadow-md rounded-lg justify-center">
                <thead>
                  <tr>
                    <th className="border px-4 py-2 text-center">Matrícula</th>
                    <th className="border px-4 py-2 text-center">Nombre</th>
                    <th className="border px-4 py-2 text-center">Cuestionario Llenado</th>
                    <th className="border px-4 py-2 text-center">Ver</th>
                  </tr>
                </thead>
                <tbody>
                  {alumnos.map((alumno, index) => (
                    <tr key={index} className="even:bg-gray-100">
                      <td className="border px-4 py-2 text-center">{alumno.matricula}</td>
                      <td className="border px-4 py-2 text-center">{alumno.nombre}</td>
                      <td className="border px-4 py-2 text-center">
                        {alumno.cuestionarioLlenado ? "Sí" : "No"}
                      </td>
                      <td className="border px-4 py-2 text-center">
                        <button className="px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600">
                          Ver
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Solo es para prueba */}
          <div className="mt-4 flex justify-center">
            <button
              onClick={agregarAlumno}
              className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
            >
              Agregar Alumno
            </button>
          </div>
        </div>
      
    </div>
  );
};

export default CuestionarioSPage;
