import '@/styles/globals.css'
import { Metadata } from "next";
import ReduxProvider from "@/redux/ReduxProvider";


interface RootProps {
  children: React.ReactNode;
}

export const metadata: Metadata = {
  title:"Wellness Admin",
  description:"Wellness Admin",
  icons:[
    {rel:"shortcut icon",url:"/icono.svg"},
    {rel:"icon",url:"/icono.svg"},
    {rel:"favicon",url:"/icono.svg"},
    
  ]
};

export default function RootLayout({ children }: RootProps) {
  return (
    <html lang="es-MX">
      <head>
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.3.0/css/all.min.css"
          integrity="sha512-SzlrxWUlpfuzQ+pcUCosxcglQRNAq/DZjVsC0lE40xsADsfeQoEypE+enwcOiGjk/bSuGGKHEyjSoQ1zVisanQ=="
          crossOrigin="anonymous"
          referrerPolicy="no-referrer"
        />
  
        <link rel="stylesheet" 
        href="https://rsms.me/inter/inter.css"
        crossOrigin="anonymous"
        referrerPolicy="no-referrer"
        />

      </head>
      <ReduxProvider>
        <body>{children}</body>
      </ReduxProvider>

      
    </html>
  );
}
