import { useState } from "react";

import { generarIdUnico } from "@/utils/helpers";
import { twMerge } from "tailwind-merge";

interface SugestionSearchBarProps<T> {
  search: string;
  setSearch: (search: string) => void;
  sugestions: T[];
  onSelect: (selected: T) => void;
  labelResolver: (item: T) => string;
  placeholder?: string;
  inputClassName?: string;
  selectableClassName?: string;
  className?: string;
}

export function SugestionSearchBar<T>({
  search,
  setSearch,
  sugestions,
  onSelect,
  labelResolver,
  placeholder,
  inputClassName,
  selectableClassName,
  className,
}: SugestionSearchBarProps<T>) {
  const [focused, setFocused] = useState(false);

  return (
    <div className={twMerge("mt-6 mx-4 xl:mx-0 xl:col-span-6", className)}>
      <div className="w-full">
        <div className="flex items-center bg-white shadow rounded-lg">
          <i className="fa-solid fa-magnifying-glass mx-4 rounded-lg text-slate-500"></i>
          <input
            type="text"
            className={twMerge(
              "w-full py-3 md:py-5 px-4 rounded-lg text-base md:text-xl focus:outline-none buscadorAlumnos",
              inputClassName
            )}
            onChange={(event) => setSearch(event.target.value)}
            value={search}
            placeholder={placeholder}
            onFocus={() => setFocused(true)}
          />
        </div>
        {focused && (
          <div className={"bg-white p-3 rounded-lg shadow"}>
            {sugestions.map((sugestion) => (
              <button
                className={twMerge(
                  ` my-1 py-2 shadow px-4 w-full`,
                  selectableClassName
                )}
                key={generarIdUnico()}
                onClick={() => {
                  onSelect(sugestion);
                  setSearch(labelResolver(sugestion));
                  setFocused(false);
                }}
              >
                <p className="text-left  capitalize">
                  {labelResolver(sugestion)}
                </p>
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
