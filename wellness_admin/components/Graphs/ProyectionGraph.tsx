"use client"



import { ProyectionGraphData } from '@/types/AppTypes';
import React from 'react'
import { CartesianGrid, ResponsiveContainer, XAxis,Bar, Rectangle, BarProps, ReferenceLine, ComposedChart } from 'recharts'



// Iterface real pero no se puede colocar por errores de tipado de recharts
interface CustomBarProps extends BarProps {
    payload: ProyectionGraphData;
}

const CustomBar = (props:any ) => {
    const { fill, x, y, width, height, payload } = props;
  
    // Cambiar el color a gris si projection es true
    const actualFill = payload.projection ? '#A9A9A9' : fill;
  
    return <Rectangle x={x ? +x : 0} y={y ? +y : 0} width={width} height={height} fill={actualFill} />;
  };
  

  interface Props{
    data:ProyectionGraphData[]
  }

export default function ProyectionGraph({data}:Props) {

  const maxValue = Math.max(...data.map((item)=>item.value))

  return (
    <div className="w-full h-52 px-5">
        <ResponsiveContainer width="100%" height="100%">
        <ComposedChart
          width={500}
          height={300}
          data={data}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <ReferenceLine y={maxValue}  label={{ value: maxValue, position: 'insideTopLeft' }} stroke="red" strokeDasharray="3 3" /> 
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="label" />
          {/* <YAxis /> */}
          {/* <Tooltip /> */}
          
          <Bar dataKey="value" fill="#93C5FD" shape={<CustomBar />}  />
          {/* <Line dataKey="value" fill="#CFFAFE" stroke='#7DD3FC' dot={false} /> */}
          
        </ComposedChart>
        </ResponsiveContainer>
    </div>
  )
}
