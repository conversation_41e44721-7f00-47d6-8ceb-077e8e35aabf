import React from "react";
import { GraficaData } from "@/types/AppTypes";

import {
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  BarChart,
  Bar,
  ResponsiveContainer,
} from "recharts";

interface Props {
    data: GraficaData[];
    fillColor ?:string;
  }


  export default function BarraGraph({ data,fillColor= "#003399"}: Props) {
    return (

    <ResponsiveContainer width="100%" height="100%">
        <BarChart width={600} height={350} data={data}>
          <XAxis dataKey="label" strokeWidth={1} />
          <YAxis />
          <Tooltip />
          <CartesianGrid stroke="#ccc" strokeDasharray="5 5" />
          <Bar dataKey="value" fill= {fillColor} barSize={70} label={{ position: 'top' }} />
        </BarChart>
    </ResponsiveContainer>
       
     
    );
  }
  

