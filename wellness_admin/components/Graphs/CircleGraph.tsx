"use client";

import { fetcher } from "@/config/fetcher";
import React from "react";
import useS<PERSON> from "swr";

import { Respons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from "recharts";
import { GraphData } from "@/types/AppTypes";
import { twMerge } from "tailwind-merge";


const RED = "#F43F5E"
const BLUE = "#60A5FA"

const RADIAN = Math.PI / 180;
const renderCustomizedLabel = ({
  cx,
  cy,
  midAngle,
  innerRadius,
  outerRadius,
  percent,
  index,
  total
}: any) => {

 
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text
      x={x}
      y={y}
      fill="white"
      textAnchor={x > cx ? "start" : "end"}
      dominantBaseline="central"
    >
      {Math.round(total*percent)}
    
    </text>
  );
};

interface CircleGraphProps {
  data?: GraphData[];
  className ?: string;

}

export default function CircleGraph({ data,className }: CircleGraphProps) {


  if (!data) {
    return <div>Cargando...</div>;
  }

  return (
    <div className={twMerge("w-full h-52",className)}>
      <ResponsiveContainer width="100%" height="100%">
        <PieChart width={800} height={800}>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={(props) => renderCustomizedLabel({...props,total:data.reduce((a,b)=>a+b.value,0)})}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {data.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={entry.label === "Llenado" ? RED : BLUE}
              />
            ))}
          </Pie>
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
}
