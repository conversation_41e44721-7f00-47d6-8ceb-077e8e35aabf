import TextInput from "@/components/forms/TextInput";
import { axiosConfig } from "@/config/axiosConfig";
import clienteAxios from "@/config/clienteAxios";
import { AdminInt } from "@/types/ModelTypes";
import { adminRoles } from "@/utils/constants";
import { handleError } from "@/utils/errorHandler";
import React, { useState } from "react";
import Swal from "sweetalert2";
import { mutate } from "swr";

interface Props extends AdminInt {
  setEdit?: React.Dispatch<React.SetStateAction<boolean>>;
  setCreate?: React.Dispatch<React.SetStateAction<boolean>>;
  edition: boolean;
}

export default function CUAdmin({
  id,
  name,
  password,
  role,
  email,
  setEdit,
  setCreate,
  edition,
}: Props) {
  const [nameState, setNameState] = useState(name);
  const [emailState, setEmailState] = useState(email);
  const [passwordState, setPasswordState] = useState("");
  const [roleState, setRoleState] = useState(role as string);


  const payload = {
    name: nameState,
    email: emailState,
    password: passwordState,
    role: roleState,
  }

  async function handleEdit() {
    try {
      // Obtenemos la configuracion
      const config = axiosConfig();
      if (!config) {
        throw new Error("No hay token");
      }

      // Hacemos la peticion
      const { data } = await clienteAxios.put("/admin", {
          id,
          ...payload
      }, config);

      // Mostramos la respuesta
      Swal.fire({
        icon: "success",
        text: data.message,
      });

      // Quitamos el modo de edicion
      setEdit && setEdit(false);

      // Sincronizamos el estado
      mutate("/admin");
    } catch (error: any) {
      return handleError(error);
    }
  }

  async function handleCreate() {
    try {
      // Obtenemos la configuracion
      const config = axiosConfig();
      if (!config) {
        throw new Error("No hay token");
      }

      // Hacemos la peticion
      const { data } = await clienteAxios.post("/admin", payload, config);

      // Mostramos la respuesta
      Swal.fire({
        icon: "success",
        text: data.message,
      });

      // Quitamos el modo de creacion
      setCreate && setCreate(false)

      // Sincronizamos el estado
      mutate("/admin");
    } catch (error: any) {
      return handleError(error);
    }
  }

  return (
    <div className="w-full bg-gray-100 rounded-md p-4 grid grid-cols-2 place-items-center gap-3">
      <TextInput
        label="Nombre"
        onChange={(e) => setNameState(e.target.value)}
        placeholder="Titulo"
        value={nameState}
        type="text"
      />

      <TextInput
        label="Correo"
        onChange={(e) => setEmailState(e.target.value)}
        placeholder="Correo"
        value={emailState}
        type="email"
      />
      <TextInput
        label=""
        onChange={(e) => setPasswordState(e.target.value)}
        placeholder="Contraseña"
        value={passwordState}
        type="password"
      />

      <select
        value={roleState}
        onChange={(e) => setRoleState(e.target.value)}
        className="text-sm w-full px-1 py-1 rounded-md bg-gray-50 border-gray-300 placeholder-gray-500"
      >
        <option value={""}>-- Seleccionar --</option>
        {adminRoles.map((role) => (
          <option key={role.value} value={role.value}>
            {role.label}
          </option>
        ))}
      </select>

      <div className="col-span-2 grid grid-cols-2 w-full">
        <button
          onClick={() => {
            setEdit && setEdit(false);
            setCreate && setCreate(false);
          }}
          className="text-gray-800 rounded-md "
        >
          Cancelar
        </button>
        <button
          onClick={edition ? handleEdit : handleCreate}
          className="text-blue-500 ring-1 ring-blue-500  font-bold rounded-md p-2"
        >
          {edition ? "Editar" : "Crear"}
        </button>
      </div>
    </div>
  );
}
