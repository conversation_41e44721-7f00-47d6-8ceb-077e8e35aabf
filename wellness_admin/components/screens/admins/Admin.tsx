import { AdminInt } from "@/types/ModelTypes";
import React, { useState } from "react";
import CUAdmin from "./CUAdmin";
import { formatearFecha } from "@/utils/helpers";
import Swal from "sweetalert2";
import { axiosConfig } from "@/config/axiosConfig";
import clienteAxios from "@/config/clienteAxios";
import { mutate } from "swr";
import { handleError } from "@/utils/errorHandler";

export default function Admin(props: AdminInt) {
  const [edit, setEdit] = useState(false);


  // Si esta en modo de edicion
  if (edit) {
    return <CUAdmin {...props} setEdit={setEdit} edition />;
  }

  // Handler de eliminacion
  async function handleDelete() {

    // Primero verificamos si de verdad quiere borrar
    const { isConfirmed } = await Swal.fire({
      icon: "warning",
      text: "¿Estas seguro de borrar este administrador?",
      showCancelButton: true,
      cancelButtonText: "Cancelar",
      confirmButtonText: "Borrar",
      
    });
    if (!isConfirmed) {
        return;
    }

    try {
      // Obtenemos la configuracion
      const config = axiosConfig();
      if (!config) {
        throw new Error("No hay token");
      }

      // Hacemos la peticion
      const { data } = await clienteAxios.delete(`/admin/${props.id}`, config);

      // Mostramos la respuesta
      Swal.fire({
        icon: "success",
        text: data.message,
      });

      // Sincronizamos el estado
      mutate("/admin");
    } catch (error: any) {
      return handleError(error);
    }
  }

  return (
    <div className="w-full bg-gray-100 rounded-md p-4 grid grid-cols-1 place-items-center gap-3">
      <p className="text-lg font-bold text-blue-500 text-center">
        {props.name}
      </p>
      <p className="capitalize font-bold">{props.email}</p>
      <p className="capitalize font-bold">{props.role}</p>

      <div className="grid grid-cols-2 w-full">
        <button onClick={handleDelete} className="text-red-500 rounded-md ">
          Eliminar
        </button>
        <button
          onClick={() => setEdit(true)}
          className="text-blue-500 ring-1 ring-blue-500  font-bold rounded-md p-2"
        >
          Editar
        </button>
      </div>
    </div>
  );
}
