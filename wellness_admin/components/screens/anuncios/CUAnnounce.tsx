import TextInput from '@/components/forms/TextInput';
import { axiosConfig } from '@/config/axiosConfig';
import clienteAxios from '@/config/clienteAxios';
import { AnnounceInt } from '@/types/ModelTypes';
import { handleError } from '@/utils/errorHandler';
import { formatInputDate } from '@/utils/helpers';
import React, { useState } from 'react'
import Swal from 'sweetalert2';
import { mutate } from 'swr';

interface Props extends AnnounceInt {
    setEdit?: React.Dispatch<React.SetStateAction<boolean>>;
    setCreate?: React.Dispatch<React.SetStateAction<boolean>>;
    edition:boolean
  }

export default function CUAnnounce({
    id,
    title,
    end_date,
    event_date,
    init_date,
    setEdit,
    setCreate,
    edition
  }: Props) {
    // Estados de la edicion o creacion
    const [titleState, setTitleState] = useState(title);
    const [eventDateState, setEventDateState] = useState(
      formatInputDate(event_date)
    );
    const [initDateState, setInitDateState] = useState(
      formatInputDate(init_date)
    );
    const [endDateState, setEndDateState] = useState(formatInputDate(end_date));

    // Definimos un payload común entre los dos

    const payload = {
      title: titleState,
      event_date: eventDateState,
      init_date: initDateState,
      end_date: endDateState,
    };
  
    // Handler de edicion
    async function handleEdit() {
      try {
        // Obtenemos la configuracion
        const config = axiosConfig();
        if (!config) {
          throw new Error("No hay token");
        }
  
        // Hacemos la peticion
        const { data } = await clienteAxios.put("/announce", {
            id,
            ...payload
        }, config);
  
        // Mostramos la respuesta
        Swal.fire({
          icon: "success",
          text: data.message,
        });
  
        // Quitamos el modo de edicion
        setEdit && setEdit(false);
  
        // Sincronizamos el estado
        mutate("/announce/all");
      } catch (error: any) {
        return handleError(error);
      }
    }

    // Handler de creacion
    async function handleCreate() {
        try {
          // Obtenemos la configuracion
          const config = axiosConfig();
          if (!config) {
            throw new Error("No hay token");
          }
    
          // Hacemos la peticion
          const { data } = await clienteAxios.post("/announce", payload, config);
    
          // Mostramos la respuesta
          Swal.fire({
            icon: "success",
            text: data.message,
          });
    
          // Quitamos el modo de creacion
          setCreate && setCreate(false)
    
          // Sincronizamos el estado
          mutate("/announce/all");
        } catch (error: any) {
          return handleError(error);
        }
      }
  
    return (
      <div className="w-full bg-gray-100 rounded-md p-4 grid grid-cols-2 place-items-center gap-3">
        <TextInput
          label=""
          onChange={(e) => setTitleState(e.target.value)}
          placeholder="Titulo"
          value={titleState}
          type="text"
          showLabel={false}
        />
  
        <TextInput
          label=""
          onChange={(e) => setEventDateState(e.target.value)}
          placeholder="Fecha del evento"
          value={eventDateState}
          type="date"
          showLabel={false}
        />
  
        <div className="grid place-items-center">
          <TextInput
            label="Inicio"
            onChange={(e) => setInitDateState(e.target.value)}
            placeholder="Inicio"
            value={initDateState}
            type="date"
          />
        </div>
        <div className="grid place-items-center">
          <TextInput
            label="Final"
            onChange={(e) => setEndDateState(e.target.value)}
            placeholder="Final"
            value={endDateState}
            type="date"
          />
        </div>
  
        <div className="col-span-2 grid grid-cols-2 w-full">
          <button
            onClick={() => {
                setEdit && setEdit(false);
                setCreate && setCreate(false);
            }}
            className="text-gray-800 rounded-md "
          >
            Cancelar
          </button>
          <button
            onClick={edition ?handleEdit :handleCreate}
            className="text-blue-500 ring-1 ring-blue-500  font-bold rounded-md p-2"
          >
            {edition ? "Editar" : "Crear"}
          </button>
        </div>
      </div>
    );
  }
