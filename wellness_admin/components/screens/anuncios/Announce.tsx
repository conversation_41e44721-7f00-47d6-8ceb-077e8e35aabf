import { AnnounceInt } from "@/types/ModelTypes";
import React, { useState } from "react";

import { formatInputDate, formatearFecha } from "@/utils/helpers";
import TextInput from "@/components/forms/TextInput";
import { axiosConfig } from "@/config/axiosConfig";
import { handleError } from "@/utils/errorHandler";
import clienteAxios from "@/config/clienteAxios";
import Swal from "sweetalert2";
import { mutate } from "swr";
import CUAnnounce from "./CUAnnounce";

interface Props extends AnnounceInt {
  setEdit: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function Announce(props: AnnounceInt) {
  const [edit, setEdit] = useState(false);

  // Si esta en modo ver
  if (!edit) {
    return <ViewAnnounce {...props} setEdit={setEdit} />;
  }

  // Si esta en modo editar
  return <CUAnnounce {...props} setEdit={setEdit} edition={true} />;
}

function ViewAnnounce({
  id,
  title,
  end_date,
  event_date,
  init_date,
  setEdit,
}: Props) {
  // Handler de eliminación
  async function handleDelete() {

    // Primero verificamos si de verdad quiere borrar
    const { isConfirmed } = await Swal.fire({
      icon: "warning",
      text: "¿Estas seguro de borrar este anuncio?",
      showCancelButton: true,
      cancelButtonText: "Cancelar",
      confirmButtonText: "Borrar",
      
    });
    if (!isConfirmed) {
        return;
    }

    
    try {
      // Obtenemos la configuracion
      const config = axiosConfig();
      if (!config) {
        throw new Error("No hay token");
      }

      // Hacemos la peticion
      const { data } = await clienteAxios.delete(`/announce/${id}`, config);

      // Mostramos la respuesta
      Swal.fire({
        icon: "success",
        text: data.message,
      });

      // Sincronizamos el estado
      mutate("/announce");
    } catch (error: any) {
      return handleError(error);
    }
  }
  return (
    <div className="w-full bg-gray-100 rounded-md p-4 grid grid-cols-2 place-items-center gap-3">
      <p className="text-lg font-bold text-blue-500 text-center">{title}</p>
      <p className="capitalize font-bold">{formatearFecha(event_date)}</p>

      <div className="grid place-items-center">
        <p>Inicio</p>
        <p className="capitalize">{formatearFecha(init_date)}</p>
      </div>
      <div className="grid place-items-center">
        <p>Final</p>
        <p className="capitalize">{formatearFecha(end_date)}</p>
      </div>

      <div className="col-span-2 grid grid-cols-2 w-full">
        <button onClick={handleDelete} className="text-red-500 rounded-md ">
          Eliminar
        </button>
        <button
          onClick={() => setEdit(true)}
          className="text-blue-500 ring-1 ring-blue-500  font-bold rounded-md p-2"
        >
          Editar
        </button>
      </div>
    </div>
  );
}

