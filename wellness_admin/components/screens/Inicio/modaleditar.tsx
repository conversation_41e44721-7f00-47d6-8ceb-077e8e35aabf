import { Fragment, useState } from "react";
import { useDispatch,useSelector } from "react-redux";

import {mutate} from "swr"


import { Dialog, Transition } from "@headlessui/react";
import { RootState } from "@/redux/store";
import { toggleModalCerrar, toggleModalEditar } from "@/redux/slices/modalSlice";

import { handleError } from "@/utils/errorHandler";
import { axiosConfig } from "@/config/axiosConfig";
import clienteAxios from "@/config/clienteAxios";
import Swal from "sweetalert2";

export default function ModalEditar() {

  const {modalEditar} = useSelector((state:RootState)=>state.modal)
  const { spaceId } = useSelector((state: RootState) => state.index);
  const {aforoactual} = useSelector((state: RootState) => state.index);
  
  const [aforonuevo, setAforoNuevo] = useState("")
  /* const [start_date, setStart_date] = useState("")
  const [end_date, setEnd_date] = useState("")
 */

  const handleSubmit = async (e:any)=>{
    e.preventDefault()

    try {

      const config = axiosConfig()
      if(!config){
        throw new Error("No hay autenticacion")
      }
      if(!spaceId){
        throw new Error("No hay espacio seleccionado")
      }
    /*   if(start_date === ""){
        throw new Error("No hay fecha de inicio")
      }

      if(end_date === ""){
        throw new Error("No hay fecha de fin")
      } */
      if(!aforoactual){
        throw new Error("No hay ningun aforo actual")
      }
      if(aforonuevo === ""){
        throw new Error("No hay ningun aforo nuevo")
      }

      const {data} = await clienteAxios.post("/space/changeQuota",{
        id:spaceId,
        quota:Number(aforonuevo)
      },config)


      mutate("/space/spaces")

      Swal.fire({
        icon:"success",
        title:data.msg
      })


/*       setStart_date("")
      setEnd_date("") */
      setAforoNuevo("")

      dispatch(toggleModalEditar())
      
    } catch (error:any) {
      return handleError(error)
    }
  }


  const dispatch = useDispatch();

  return (
    <Transition.Root show={modalEditar} as={Fragment}>
    <Dialog
      as="div"
      className="fixed z-50 inset-0 overflow-y-auto "
      onClose={()=>{
          dispatch(toggleModalEditar())
      }}
    >
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0 ">
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <Dialog.Overlay className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        {/* This element is to trick the browser into centering the modal contents. */}
        <span
          className="hidden sm:inline-block sm:align-middle sm:h-screen"
          aria-hidden="true"
        >
          &#8203;
        </span>

        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          enterTo="opacity-100 translate-y-0 sm:scale-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100 translate-y-0 sm:scale-100"
          leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        >
          <div className="inline-block align-bottom bg-white rounded-lg px-4 pt-3 pb-6 overflow-hidden shadow-xl transform transition-all sm:align-middle w-full md:w-2/3 lg:w-1/3 sm:p-6">
            <div className="">
              <Dialog.Title
                as="h3"
                className="text-3xl leading-6 text-gray-900 font-bold"
              >
              Editar aforo
              </Dialog.Title>
              <div className="my-3 text-center">

             {/* Formulario */}
             
             <form className="bg-white rounded px-8 pt-6 pb-8 mb-4">

        

                 {/* Fechas de inicio y fin del evento*/}
                {/*  <div className=" grid grid-cols-2 gap-4 mb-4">

                     <div>
                         <label className="block text-blue-700 text-2xl font-bold mb-2">
                             Inicio
                         </label>
                         <input className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" value={start_date} onChange={ (e)=>{setStart_date(e.target.value)}} id="username" type="date" placeholder="Titulo del anuncio"/>
                     </div>

                     <div>
                         <label className="block text-blue-700 text-2xl font-bold mb-2">
                             Fin
                         </label>

                         <input className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="username" value={end_date} onChange={ (e)=>{setEnd_date(e.target.value)}} type="date" placeholder="Titulo del anuncio"/>
                     </div>

                 </div> */}

                 {/* Aforo actual y ubicación */}
                 <div className=" grid grid-cols-2 gap-4 mb-4">
                     <div>
                         <label className="block text-blue-700 text-2xl font-bold mb-2">
                            Aforo actual
                         </label>
                         <input className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" value={aforoactual} id="username" type="text"  readOnly/>
                     </div>
                     
                     <div>
                         <label className="block text-blue-700 text-2xl font-bold mb-2">
                             Nuevo aforo
                         </label>
                         <input min={0} className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" value={aforonuevo} onChange={(e) => {setAforoNuevo(e.target.value)}}  id="aforo" type="number" placeholder="Nuevo aforo"/>
                     </div>
                 </div>

             
                 {/* Botones */}
                 <button
                     type="button"
                     className="bg-slate-700 text-slate-100 font-bold rounded-md px-3 py-2 mt-3 hover:bg-slate-800"
                     onClick={() => {
                     dispatch(toggleModalEditar())
                         }}
                         >
                     Cancelar
                 </button>
                 
                 <button
                 type="button"
                 className="bg-blue-700 text-slate-100 font-bold rounded-md px-3 py-2 mt-3  ml-2 hover:bg-slate-800"
                 onClick={handleSubmit}
                 >
                     Editar
                 </button>
             </form>


              </div>
            </div>
          </div>
        </Transition.Child>
      </div>
    </Dialog>
  </Transition.Root>
);
}