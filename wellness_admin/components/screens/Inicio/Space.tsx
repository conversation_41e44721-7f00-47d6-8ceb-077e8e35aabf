import React, { useState } from "react";
import Image from "next/image";
import {
  toggleModalCerrar,
  toggleModalEditar,
  toggleModalEspacio,
} from "@/redux/slices/modalSlice";
import { useDispatch } from "react-redux";

import Link from "next/link";
import { SpaceInt } from "@/types/ModelTypes";
import { setAforoo, setSpace, setSpaceIdo } from "@/redux/slices/indexSlice";

import { axiosConfig } from "@/config/axiosConfig";
import clienteAxios from "@/config/clienteAxios";
import Swal from "sweetalert2";
import { mutate } from "swr";
import { handleError } from "@/utils/errorHandler";
import Spinner from "@/components/shared/Spinner";

const Space: React.FC<SpaceInt> = (props) => {
  const {id, name, open, quota, image} = props

  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false)

  // función encargada de abrir un espacio cerrado
  const handleOpenSpace = async() => {
    try {
      setLoading(true)
      const config = axiosConfig()
      if(!config){
        throw new Error("No hay autenticacion")
      }

      const {data} = await clienteAxios.put(`/space/openSpace/${id}`,{},config)

      Swal.fire({
        icon:"success",
        title:data.msg
      })

      mutate("/space/spaces")

    } catch (error:any) {
      return handleError(error)
    }finally{
      setLoading(false)
    }
  }

  return (
    <div className="p-4 mt-2 bg-white drop-shadow-xl w-full rounded-lg">
      {/* Mobile view */}
      <div className="md:hidden">
        <div className="flex items-center mb-3">
          {/* Image and name in one row */}
          <div className="relative w-16 h-16 overflow-hidden rounded-lg mr-3">
            <Image
              width={64}
              height={64}
              alt="Imagen Espacio"
              src={image ? image : "../../../public/images/borrego-blue.png"}
              className="object-cover"
            />
          </div>

          <div className="flex-1">
            <h2 className="text-lg font-bold text-slate-900 truncate">{name}</h2>
            <div className="flex items-center gap-2 mt-1">
              <i className={`fa-solid fa-circle text-sm ${open ? "text-green-500" : "text-red-500"}`}></i>
              <span className={`text-sm font-medium ${open ? "text-green-600" : "text-red-600"}`}>
                {open ? "Abierto" : "Cerrado"}
              </span>
            </div>
          </div>
        </div>

        {/* Actions in a grid for mobile */}
        <div className="grid grid-cols-2 gap-2 mt-3">
          <Link
            href={`/estadisticas/${id}`}
            className="p-2 bg-blue-500 hover:bg-blue-600 shadow-sm rounded-md flex items-center justify-center"
          >
            <i className="fa-solid fa-chart-column text-white text-sm mr-1"></i>
            <span className="text-white text-sm">Estadísticas</span>
          </Link>

          <button
            className="p-2 ring-1 ring-gray-300 hover:bg-gray-50 shadow-sm rounded-md flex items-center justify-center"
            onClick={() => {
              dispatch(setSpace(props))
              dispatch(toggleModalEspacio());
            }}
          >
            <i className="fa-solid fa-pen text-gray-600 text-sm mr-1"></i>
            <span className="text-gray-600 text-sm">Editar</span>
          </button>

          <button
            className="p-2 ring-1 ring-gray-300 hover:bg-gray-50 shadow-sm rounded-md flex items-center justify-center"
            onClick={() => {
              dispatch(setAforoo(quota))
              dispatch(setSpaceIdo(id))
              dispatch(toggleModalEditar());
            }}
          >
            <i className="fa-solid fa-user-pen text-gray-600 text-sm mr-1"></i>
            <span className="text-gray-600 text-sm">Aforo</span>
          </button>

          {loading ? (
            <div className="p-2 flex items-center justify-center">
              <Spinner />
            </div>
          ) : (
            <button
              className={`p-2 rounded-md flex items-center justify-center ${
                open ? "bg-red-100 text-red-600 hover:bg-red-200" : "bg-green-100 text-green-600 hover:bg-green-200"
              }`}
              onClick={() => {
                if(!open) {
                  handleOpenSpace()
                  return
                }
                dispatch(setAforoo(quota))
                dispatch(setSpaceIdo(id))
                dispatch(toggleModalCerrar());
              }}
            >
              <i className={`fa-solid ${open ? "fa-lock" : "fa-lock-open"} text-sm mr-1`}></i>
              <span className="text-sm">{!open ? "Abrir" : "Cerrar"}</span>
            </button>
          )}
        </div>
      </div>

      {/* Desktop view */}
      <div className="hidden md:grid md:grid-cols-12 md:gap-2 md:items-center">
        {/* Image column */}
        <div className="col-span-2 flex justify-center">
          <div className="relative w-20 h-20 overflow-hidden rounded-lg">
            <Image
              width={80}
              height={80}
              alt="Imagen Espacio"
              src={image ? image : "../../../public/images/borrego-blue.png"}
              className="object-cover"
            />
          </div>
        </div>

        {/* Area name column */}
        <h2 className="col-span-3 text-lg font-bold text-slate-900 truncate">{name}</h2>

        {/* Status column */}
        <div className="col-span-2 flex items-center gap-2 justify-center">
          <i className={`fa-solid fa-circle text-lg ${open ? "text-green-500" : "text-red-500"}`}></i>
          <span className={`text-sm font-medium ${open ? "text-green-600" : "text-red-600"}`}>
            {open ? "Abierto" : "Cerrado"}
          </span>
        </div>

        {/* Actions column */}
        <div className="col-span-5 flex flex-row items-center justify-end gap-2">
          <Link
            href={`/estadisticas/${id}`}
            className="p-2 bg-blue-500 hover:bg-blue-600 shadow-sm rounded-md inline-flex items-center justify-center"
          >
            <i className="fa-solid fa-chart-column text-white text-sm mr-1"></i>
            <span className="text-white text-sm">Estadísticas</span>
          </Link>

          <button
            className="p-2 ring-1 ring-gray-300 hover:bg-gray-50 shadow-sm rounded-md inline-flex items-center justify-center"
            onClick={() => {
              dispatch(setSpace(props))
              dispatch(toggleModalEspacio());
            }}
          >
            <i className="fa-solid fa-pen text-gray-600 text-sm mr-1"></i>
            <span className="text-gray-600 text-sm">Editar espacio</span>
          </button>

          <button
            className="p-2 ring-1 ring-gray-300 hover:bg-gray-50 shadow-sm rounded-md inline-flex items-center justify-center"
            onClick={() => {
              dispatch(setAforoo(quota))
              dispatch(setSpaceIdo(id))
              dispatch(toggleModalEditar());
            }}
          >
            <i className="fa-solid fa-user-pen text-gray-600 text-sm mr-1"></i>
            <span className="text-gray-600 text-sm">Editar aforo</span>
          </button>

          {loading ? (
            <div className="p-2 flex items-center justify-center">
              <Spinner />
            </div>
          ) : (
            <button
              className={`p-2 rounded-md inline-flex items-center justify-center ${
                open ? "bg-red-100 text-red-600 hover:bg-red-200" : "bg-green-100 text-green-600 hover:bg-green-200"
              }`}
              onClick={() => {
                if(!open) {
                  handleOpenSpace()
                  return
                }
                dispatch(setAforoo(quota))
                dispatch(setSpaceIdo(id))
                dispatch(toggleModalCerrar());
              }}
            >
              <i className={`fa-solid ${open ? "fa-lock" : "fa-lock-open"} text-sm mr-1`}></i>
              <span className="text-sm">{!open ? "Abrir" : "Cerrar"}</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default Space;