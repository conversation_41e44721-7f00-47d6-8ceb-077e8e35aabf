import React from "react";
import Image from "next/image";
import {
  toggleModalCerrar,
  toggleModalEditar,
} from "@/redux/slices/modalSlice";
import { useDispatch } from "react-redux";
import ModalCerrar from "./modalcerrar";
import Link from "next/link";

interface ContenedorProps {
  name: string;
}

const Contenedor: React.FC<ContenedorProps> = ({ name }) => {
  const dispatch = useDispatch();

  return (
    <div className="p-4 mt-2 bg-white drop-shadow-xl flex flex-row items-center justify-around">
      <div className="w-5/12 h-full justify-between flex items-center">
        <h2 className="text-lg font-bold text-[#645757] truncate max-w-[80%]">{name}</h2>
        <div className="h-6 w-6 md:h-8 md:w-8 bg-[#0D7D1F] shadow-lg rounded-full flex-shrink-0" />
      </div>

      <div className="w-1/12 flex-shrink-0" />

      <div className="ml-auto md:px-4 w-6/12 flex items-stretch justify-evenly">
        <button className="p-2 bg-[#194DD3] shadow-lg md:w-3/12 md:h-12 lg:h-full hover:bg-blue-600 rounded-lg mr-2 flex-shrink-0">
          <Link href="/estadisticas">
            <Image
              className="w-full h-full lg:hidden"
              src={"/images/grafica.svg"}
              width={10}
              height={10}
              alt="Grafica"
            />
          </Link>

          <Link
            href="/estadisticas"
            className="hidden lg:block truncate py-2 pl-3 pr-4 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-dark md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent"
          >
            <h2 className="text-white font-bold">Estadisticas</h2>
          </Link>
        </button>

        <button
          className="p-2 md:px-4 bg-gray-500 hover:bg-gray-600 md:h-12 lg:h-full md:w-3/12 shadow-lg rounded-md mr-2 flex-shrink-0"
          onClick={() => {
            dispatch(toggleModalEditar());
          }}
        >
          <Image
            className="w-full h-full lg:hidden"
            src={"/images/cambioperf.svg"}
            width={10}
            height={10}
            alt="perfil"
          />
          <h2 className="text-white font-bold hidden lg:inline truncate">
            Editar aforo
          </h2>
        </button>

        <button
          className="p-2 md:px-4 bg-[#9A2E2E] shadow-lg lg:h-full md:h-12 md:w-3/12 hover:bg-red-600 rounded-md flex-shrink-0"
          onClick={() => {
            dispatch(toggleModalCerrar());
          }}
        >
          <Image
            className="w-full h-full lg:hidden"
            src={"/images/tacha.svg"}
            width={10}
            height={10}
            alt="cancelar"
          />
          <h2 className="text-white font-bold hidden lg:inline truncate">Cerrar</h2>
        </button>
      </div>
    </div>
  );
};

export default Contenedor;