import { Fragment, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import jsCookie from "js-cookie";
import { mutate } from "swr";

import { Dialog, Transition } from "@headlessui/react";
import { RootState } from "@/redux/store";
import { toggleModalEspacio } from "@/redux/slices/modalSlice";
import { handleError } from "@/utils/errorHandler";
import { axiosConfig } from "@/config/axiosConfig";
import clienteAxios from "@/config/clienteAxios";
import CreatableSelect from "react-select/creatable";

import { MultiValue, ActionMeta } from "react-select/dist/declarations/src";

import Swal from "sweetalert2";
import ImageUploader from "@/components/forms/ImageUploader";
import TextInput from "@/components/forms/TextInput";
import { setSpace } from "@/redux/slices/indexSlice";

const components = {
  IndicatorsContainer: () => null,
  Menu: () => null,
  Option: () => null,
};

// Tipado para la funcion de handleMaterialesChange
type ActionMetaType = MultiValue<{
  label: string;
  value: string;
}>;

type NewValueType = ActionMeta<{
  label: string;
  value: string;
}>;

interface PreviewImage {
  file: File;
  preview: string;
}

export function ModalCUSpace() {
  const { modalEspacio } = useSelector((state: RootState) => state.modal);
  const { auth } = useSelector((state: RootState) => state.auth);
  const {space} = useSelector((state: RootState) => state.index);

  const [name, setName] = useState("");
  const [quota, setQuota] = useState("");
  const [onlineQuota, setOnlineQuota] = useState("");
  const [start_time, setStart_time] = useState("");
  const [end_time, setEnd_Time] = useState("");
  const [location, setLocation] = useState("");
  const [materials, setMaterials] = useState<string[]>([]);
  const [picture, setPicture] = useState<File | null>(null);

  const [previewImage, setPreviewImage] = useState<PreviewImage | null>(null);

  const dispatch = useDispatch();

  const [materialsRequired, setMaterialsRequired] = useState(false);

  useEffect(()=>{
    setName(space?.name ?? "")
    setQuota(space?.quota.toString() ?? "")
    setOnlineQuota(space?.onlineQuota.toString() ?? "")
    setStart_time(space?.open_time ?? "")
    setEnd_Time(space?.close_time ?? "")
    setLocation(space?.location ?? "")
    setMaterials(space?.materials ?? [])
    setMaterialsRequired(space?.materials_required ?? false)
    
  },[space])

  const handleUpload = (dataUrl: string, file: File) => {
    setPicture(file);
  };

  const handleMaterialsChange = (
    newValue: ActionMetaType,
    actionMeta: NewValueType
  ) => {
    // Check if newValue is not null or undefined
    if (!newValue) return;

    const newOption =
      newValue[actionMeta.action === "create-option" ? newValue.length - 1 : 0]
        ?.label;

    // Check if newOption is not null or undefined
    if (!newOption) return;

    const exists = materials.includes(newOption);

    if (!exists) {
      setMaterials((prev) => [...prev, newOption]);
    } else if (
      actionMeta.action === "remove-value" ||
      actionMeta.action === "pop-value"
    ) {
      const removed = actionMeta.removedValue?.label;

      // Check if removed is not null or undefined
      if (!removed) return;

      setMaterials((prev) => prev.filter((material) => material !== removed));
    }
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault();

    try {
      if (!auth) {
        throw new Error("No hay ningun administrador");
      }

      if (end_time === "") {
        throw new Error("No hay fecha de fin");
      }

      if (start_time === "") {
        throw new Error("No hay fecha de comienzo");
      }
      if (name === "") {
        throw new Error("Ingrese un nombre");
      }
      if (quota === "") {
        throw new Error("No hay quota");
      }
      if (location === "") {
        throw new Error("No hay ubicacion");
      }
    

      const formData = new FormData();

      formData.append("name", name);
      formData.append("adminId", auth.id.toString());
      formData.append("quota", quota);
      formData.append("onlineQuota", quota);
      formData.append("open_time", start_time);
      formData.append("close_time", end_time);
      formData.append("materials_required", materialsRequired.toString());
      formData.append("materials", materials.toString());
      formData.append("location", location);

      if (picture) {
        formData.append("image", picture);
      }

      const config = axiosConfig(false);
      if (!config) {
        throw new Error("No hay autenticacion");
      }

      let data

      // Si hay un espacio, entonces se esta editando
      if(space){
        // Agregamos el id del espacio
        formData.append("id", space.id.toString());

        const { data:response } = await clienteAxios.put(
          "/space/updateSpace",
          formData,
          config
        );
        data = response

      }else{
        // Sino se esta creando
        const { data:response } = await clienteAxios.post(
          "/space/createSpace",
          formData,
          config
        );
        data = response

      }


      mutate("/space/spaces");

      Swal.fire({
        icon: "success",
        title: data?.msg,
      });

      dispatch(toggleModalEspacio());
      dispatch(setSpace(null))

      setStart_time("");
      setEnd_Time("");
      setQuota("")
      setOnlineQuota("");
      setName("");
      setLocation("");
      setMaterials([]);
      setPicture(null);
      setPreviewImage(null);
      setMaterialsRequired(false);
    } catch (error: any) {
      return handleError(error);
    }
  };

  return (
    <Transition.Root show={modalEspacio} as={Fragment}>
      <Dialog
        as="div"
        className="fixed z-30 inset-0 overflow-y-auto "
        onClose={() => {
          dispatch(setSpace(null))
          dispatch(toggleModalEspacio());

        }}
      >
        <div className=" min-h-screen pt-4 px-4 pb-20 text-center block p-0 ">
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <Dialog.Overlay className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </Transition.Child>

          {/* This element is to trick the browser into centering the modal contents. */}
          <span
            className=" inline-block align-middle h-screen"
            aria-hidden="true"
          >
            &#8203;
          </span>

          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0 translate-y-0 scale-95"
            enterTo="opacity-100 translate-y-0 scale-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100 translate-y-0 scale-100"
            leaveTo="opacity-0 translate-y-0 scale-95"
          >
            <div className="inline-block  bg-white rounded-lg px-4 pt-3 pb-6 overflow-hidden shadow-xl transform transition-all align-middle w-5/6 md:w-2/3 lg:w-1/2 max-w-3xl p-6">
              <div className="w-full h-full ">
                <Dialog.Title
                  as="h3"
                  className="text-xl font-semibold leading-6 text-black"
                >
                  {space ? "Editar":"Crear"} Area
                </Dialog.Title>

                <div
                  id="textos"
                  className="mx-auto  w-5/6 grid grid-cols-1 md:grid-cols-2 md:gap-4 place-items-center"
                >
                  <TextInput
                    label="Nombre"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    type="text"
                    placeholder="Nombre del area"
                    className="my-3"
                  />
                  <TextInput
                    label="Cuota Presencial"
                    value={quota}
                    onChange={(e) => setQuota(e.target.value)}
                    type="number"
                    placeholder="Cantidad de personas"
                    className="my-3"
                  />
                  <TextInput
                    label="Cuota en linea"
                    value={onlineQuota}
                    onChange={(e) => setOnlineQuota(e.target.value)}
                    type="number"
                    placeholder="Cantidad de personas"
                    className="my-3"
                  />
                  <TextInput
                    label="Ubicación"
                    value={location}
                    onChange={(e) => setLocation(e.target.value)}
                    type="text"
                    placeholder="Ubicacion"
                    className="my-3"
                  />
                  <TextInput
                    label="Hora de Apertura"
                    value={start_time}
                    onChange={(e) => setStart_time(e.target.value)}
                    type="time"
                    className="my-3"
                  />
                  <TextInput
                    label="Hora de Cierre"
                    value={end_time}
                    onChange={(e) => setEnd_Time(e.target.value)}
                    type="time"
                    className="my-3"
                  />

                  <div className="flex flex-col items-center">
                    <label className="mt-3">Se requieren materiales</label>
                    <label className="mt-1 relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        value=""
                        checked={materialsRequired}
                        className="sr-only peer"
                        onChange={() => {
                          setMaterialsRequired(!materialsRequired);
                        }}
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600" />
                    </label>
                  </div>

                  {materialsRequired && (
                    <>
                      <label className="mt-3">Materiales a usar</label>
                      <CreatableSelect
                        className="w-full "
                        onChange={handleMaterialsChange}
                        components={components}
                        isMulti
                      />
                    </>
                  )}
                  <div className="my-5 md:col-span-2">
                    <ImageUploader
                      onUpload={handleUpload}
                      previewImage={previewImage}
                      setPreviewImage={setPreviewImage}
                    />
                  </div>
                </div>

                <div className="my-3 text-center">
                  <button
                    type="button"
                    className="bg-blue-700 text-lg shadow-lg text-white font-bold rounded-lg px-3 py-1 mt-3"
                    onClick={handleSubmit}
                  >
                    {space ? "Editar":"Crear"}
                  </button>
                </div>
              </div>
            </div>
          </Transition.Child>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
