import { Fragment, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

import {mutate} from "swr"

import { Dialog, Transition } from "@headlessui/react";
import { RootState } from "@/redux/store";
import { toggleModalCerrar } from "@/redux/slices/modalSlice";
import { handleError } from "@/utils/errorHandler";
import { axiosConfig } from "@/config/axiosConfig";
import clienteAxios from "@/config/clienteAxios";
import Swal from "sweetalert2";

export default function ModalCerrar() {
  const { modalCerrar } = useSelector((state: RootState) => state.modal);
  const { spaceId } = useSelector((state: RootState) => state.index);


  const [start_date, setStart_date] = useState(new Date().toISOString().split("T")[0])
  const [end_date, setEnd_date] = useState("")
  const [description, setDescription] = useState("")

  const dispatch = useDispatch();

  const handleSubmit = async (e:any)=>{
    e.preventDefault()

    

    try {

      const config = axiosConfig()
      if(!config){
        throw new Error("No hay autenticacion")
      }
      if(!spaceId){
        throw new Error("No hay espacio seleccionado")
      }

      if(end_date === ""){
        throw new Error("No hay fecha de fin")
      }
      if(description === ""){
        throw new Error("Debes proporcionar una descripcion")
      }

      const {data} = await clienteAxios.put("/space/closeSpace",{
        id:spaceId,
        start_date,
        end_date,
        description
      },config)


      mutate("/space/spaces")

      Swal.fire({
        icon:"success",
        title:data.msg
      })

      dispatch(toggleModalCerrar())

      setStart_date(new Date().toISOString().split("T")[0])
      setEnd_date("")
      setDescription("")
      
    } catch (error:any) {
      return handleError(error)
    }
  }

  return (
    <Transition.Root show={modalCerrar} as={Fragment}>
      <Dialog
        as="div"
        className="fixed z-30 inset-0 overflow-y-auto "
        onClose={() => {
          dispatch(toggleModalCerrar());
        }}
      >
        <div className=" min-h-screen pt-4 px-4 pb-20 text-center block p-0 ">
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <Dialog.Overlay className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </Transition.Child>

          {/* This element is to trick the browser into centering the modal contents. */}
          <span
            className=" inline-block align-middle h-screen"
            aria-hidden="true"
          >
            &#8203;
          </span>

          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0 translate-y-0 scale-95"
            enterTo="opacity-100 translate-y-0 scale-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100 translate-y-0 scale-100"
            leaveTo="opacity-0 translate-y-0 scale-95"
          >
            <div className="inline-block  bg-white rounded-lg px-4 pt-3 pb-6 overflow-hidden shadow-xl transform transition-all align-middle w-5/6 md:w-2/3 lg:w-1/3 p-6">
              <form 
              onSubmit={handleSubmit}
              className="">
                <Dialog.Title
                  as="h3"
                  className="text-xl font-semibold leading-6 text-black"
                >
                  Cerrar espacios
                </Dialog.Title>

                <h2 className="mt-3 mb-1">Lapso de tiempo</h2>

                <div className="w-full flex flex-row justify-between">
                  <input
                    className="text-sm w-5/12 px-1 py-1 rounded-md bg-[#F0F0F0] border-[#888888] placeholder-[#858585]"
                    type="date"
                    placeholder="Fecha de inicio"
                    value={start_date}
                    onChange={(e) => setStart_date(e.target.value)}
                  />
                  <input
                    className="text-sm w-5/12 px-1 py-1 rounded-md bg-[#F0F0F0] border-[#888888] placeholder-[#858585]"
                    type="date"
                    placeholder="Fecha de fin"
                    value={end_date}
                    onChange={(e) => setEnd_date(e.target.value)}
                  />
                </div>

                <div className="w-full mt-4 h-3/6 flex flex-col items-start">
                  <h3>Descripción </h3>
                  <textarea
                    className="mt-1 p-1 text-sm w-full h-20 px-1 py-1 rounded-md bg-[#F0F0F0] border-[#888888] placeholder-[#858585]"
                    placeholder="Esta area esta cerrada por..."
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                  ></textarea>
                </div>

                <div className="my-3 text-center">
                  <button
                    type="submit"
                    className="bg-[#9A2E2E] text-sm shadow-lg text-white font-bold rounded-lg px-3 py-1 mt-3 hover:bg-red-800"
                    
                  >
                    Cerrar
                  </button>
                </div>
              </form>
            </div>
          </Transition.Child>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
