import { useState, useRef, ChangeEvent } from "react";
import useSWR, { mutate } from "swr";
import { fetcher } from "@/config/fetcher";
import { MaterialInt } from "@/types/ModelTypes";
import { axiosConfig } from "@/config/axiosConfig";
import clienteAxios from "@/config/clienteAxios";
import Swal from "sweetalert2";

import ModalMaterial from "./MaterialModal";
import MaterialEditModal from "./MaterialEditModal";
import PageLoader from "@/components/shared/PageLoader";

export default function MaterialList() {
  const { data: materiales, isLoading, error } = useSWR<MaterialInt[]>(
    "/material/getAll",
    fetcher
  );

  const [selectedMaterial, setSelectedMaterial] = useState<MaterialInt | null>(null);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Pagination configuration
  const itemsPerPage = 10;
  const offset = currentPage * itemsPerPage;
  const pageCount = Math.max(1, Math.ceil((materiales?.length || 0) / itemsPerPage));
  const currentItems = materiales?.slice(offset, offset + itemsPerPage);

  // Pagination handlers
  const handleClickNext = () => {
    if (currentPage < pageCount - 1) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handleClickPrev = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleEdit = (material: MaterialInt) => {
    setSelectedMaterial(material);
    setEditModalOpen(true);
  };

  const handleDelete = async (id: number) => {
    try {
      const result = await Swal.fire({
        title: "¿Estás seguro?",
        text: "Esta acción no se puede revertir. Si hay préstamos activos de este material, no se podrá eliminar.",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#d33",
        cancelButtonColor: "#3085d6",
        confirmButtonText: "Sí, eliminar",
        cancelButtonText: "Cancelar"
      });

      if (result.isConfirmed) {
        const config = axiosConfig();
        if (!config) throw new Error("Sesión expirada, por favor inicia sesión.");

        await clienteAxios.delete(`/material/delete/${id}`, config);

        Swal.fire({
          icon: "success",
          title: "Material eliminado",
          text: "El material ha sido eliminado correctamente",
          timer: 1500,
          showConfirmButton: false
        });

        mutate("/material/getAll");
      }
    } catch (error: any) {
      console.error("Error al eliminar material:", error);
      Swal.fire({
        icon: "error",
        title: "Error",
        text: error.response?.data?.msg || "No se pudo eliminar el material"
      });
    }
  };

  // CSV upload handler
  const handleFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check if file is CSV
    if (file.type !== "text/csv" && !file.name.endsWith('.csv')) {
      Swal.fire({
        icon: "error",
        title: "Error",
        text: "Por favor, selecciona un archivo CSV"
      });
      return;
    }

    try {
      setIsUploading(true);

      const formData = new FormData();
      formData.append('file', file);

      const config = axiosConfig();
      if (!config) throw new Error("Sesión expirada, por favor inicia sesión.");

      // Add the file to the config
      const uploadConfig = {
        ...config,
        headers: {
          ...config.headers,
          'Content-Type': 'multipart/form-data'
        }
      };

      const response = await clienteAxios.post('/material/uploadCsv', formData, uploadConfig);

      Swal.fire({
        icon: "success",
        title: "Materiales importados",
        text: response.data.msg || "Los materiales han sido importados correctamente",
        timer: 2000,
        showConfirmButton: false
      });

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      // Refresh materials list
      mutate("/material/getAll");
    } catch (error: any) {
      console.error("Error al importar materiales:", error);
      Swal.fire({
        icon: "error",
        title: "Error",
        text: error.response?.data?.msg || "No se pudieron importar los materiales"
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  if (isLoading) return <PageLoader />;

  if (error) {
    return (
      <div className="w-full text-center py-8">
        <p className="text-red-600 font-semibold">Error al cargar los materiales</p>
        <button
          onClick={() => mutate("/material/getAll")}
          className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Reintentar
        </button>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* CSV Upload and Add Material buttons */}
      <div className="flex flex-wrap justify-between items-center mb-4">
        <div className="flex gap-2 mb-2">
          <input
            type="file"
            accept=".csv"
            ref={fileInputRef}
            onChange={handleFileChange}
            className="hidden"
          />
          <button
            onClick={handleUploadClick}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center gap-2"
            disabled={isUploading}
          >
            {isUploading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Importando...
              </>
            ) : (
              <>
                <i className="fa-solid fa-file-csv"></i>
                <span className="hidden sm:inline">Importar materiales desde CSV</span>
                <span className="sm:hidden">Importar CSV</span>
              </>
            )}
          </button>
        </div>
        <div className="flex justify-center">
          <ModalMaterial />
        </div>
      </div>

      {/* Desktop view */}
      <div className="hidden md:block overflow-x-auto">
        <table className="min-w-full border shadow-md rounded-lg overflow-hidden">
          <thead className="bg-gray-200 text-gray-700">
            <tr>
              <th className="p-3 text-left">Imagen</th>
              <th className="p-3 text-left">Nombre</th>
              <th className="p-3 text-left">Cantidad</th>
              <th className="p-3 text-left">Matrículas mínimas</th>
              <th className="p-3 text-left">Días de préstamo</th>
              <th className="p-3 text-left">Costo de reposición</th>
              <th className="p-3 text-left">Acciones</th>
            </tr>
          </thead>
          <tbody>
            {currentItems?.map((mat) => (
              <tr key={mat.id} className="border-b hover:bg-gray-50">
                <td className="p-2">
                  <img
                    src={mat.image}
                    alt={mat.name}
                    className="w-16 h-16 object-cover rounded-md"
                  />
                </td>
                <td className="p-2">{mat.name}</td>
                <td className="p-2">{mat.quantity}</td>
                <td className="p-2">{mat.minMatriculas}</td>
                <td className="p-2">{mat.leadTimeDays}</td>
                <td className="p-2">${mat.replacementCost.toFixed(2)}</td>
                <td className="p-2 flex gap-2 items-center">
                  <button
                    onClick={() => handleEdit(mat)}
                    className="text-sm px-2 py-1 rounded bg-blue-600 text-white hover:bg-blue-700"
                  >
                    Editar
                  </button>
                  <button
                    onClick={() => handleDelete(mat.id)}
                    className="text-sm px-2 py-1 rounded bg-red-600 text-white hover:bg-red-700"
                  >
                    Eliminar
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile view */}
      <div className="md:hidden space-y-4">
        {currentItems?.map((mat) => (
          <div key={mat.id} className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
            <div className="flex items-center gap-3 mb-3">
              <img
                src={mat.image}
                alt={mat.name}
                className="w-16 h-16 object-cover rounded-md"
              />
              <h3 className="font-semibold text-lg">{mat.name}</h3>
            </div>

            <div className="grid grid-cols-2 gap-2 mb-3">
              <div>
                <p className="text-sm text-gray-600">Cantidad:</p>
                <p className="font-medium">{mat.quantity}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Matrículas mínimas:</p>
                <p className="font-medium">{mat.minMatriculas}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Días de préstamo:</p>
                <p className="font-medium">{mat.leadTimeDays}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Costo de reposición:</p>
                <p className="font-medium">${mat.replacementCost.toFixed(2)}</p>
              </div>
            </div>

            <div className="flex gap-2 mt-3">
              <button
                onClick={() => handleEdit(mat)}
                className="flex-1 py-2 rounded bg-blue-600 text-white hover:bg-blue-700"
              >
                Editar
              </button>
              <button
                onClick={() => handleDelete(mat.id)}
                className="flex-1 py-2 rounded bg-red-600 text-white hover:bg-red-700"
              >
                Eliminar
              </button>
            </div>
          </div>
        ))}
      </div>

      {materiales?.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">No hay materiales registrados</p>
        </div>
      )}

      {/* Pagination */}
      {materiales && materiales.length > 0 && (
        <div className="flex flex-wrap justify-center mt-6 mb-6 gap-2">
          <button
            onClick={() => setCurrentPage(0)}
            disabled={currentPage === 0}
            className="w-12 h-12 flex items-center justify-center rounded-md hover:bg-blue-50 disabled:opacity-50 text-gray-600"
            aria-label="Primera página"
          >
            <i className="fa-solid fa-angles-left"></i>
          </button>
          <button
            onClick={handleClickPrev}
            disabled={currentPage === 0}
            className="w-12 h-12 flex items-center justify-center rounded-md hover:bg-blue-50 disabled:opacity-50 text-gray-600"
            aria-label="Página anterior"
          >
            <i className="fa-solid fa-angle-left"></i>
          </button>

          {/* Mobile pagination - just show current page */}
          <div className="md:hidden flex items-center">
            <span className="mx-2 text-sm">
              Página {currentPage + 1} de {pageCount}
            </span>
          </div>

          {/* Desktop pagination - show page numbers */}
          <div className="hidden md:flex gap-2">
            {Array.from({ length: Math.min(5, pageCount) }, (_, i) => {
              // Show pages around current page
              let pageToShow = currentPage - 2 + i;
              if (currentPage < 2) pageToShow = i;
              if (currentPage > pageCount - 3) pageToShow = pageCount - 5 + i;
              if (pageToShow >= 0 && pageToShow < pageCount) {
                return (
                  <button
                    key={pageToShow}
                    onClick={() => setCurrentPage(pageToShow)}
                    className={`w-12 h-12 flex items-center justify-center rounded-md text-lg font-medium ${
                      currentPage === pageToShow
                        ? "bg-blue-500 text-white"
                        : "hover:bg-blue-50 text-gray-800"
                    }`}
                    aria-label={`Página ${pageToShow + 1}`}
                    aria-current={
                      currentPage === pageToShow ? "page" : undefined
                    }
                  >
                    {pageToShow + 1}
                  </button>
                );
              }
              return null;
            })}
          </div>

          <button
            onClick={handleClickNext}
            disabled={currentPage >= pageCount - 1}
            className="w-12 h-12 flex items-center justify-center rounded-md hover:bg-blue-50 disabled:opacity-50 text-gray-600"
            aria-label="Página siguiente"
          >
            <i className="fa-solid fa-angle-right"></i>
          </button>
          <button
            onClick={() => setCurrentPage(pageCount - 1)}
            disabled={currentPage >= pageCount - 1}
            className="w-12 h-12 flex items-center justify-center rounded-md hover:bg-blue-50 disabled:opacity-50 text-gray-600"
            aria-label="Última página"
          >
            <i className="fa-solid fa-angles-right"></i>
          </button>
        </div>
      )}

      {selectedMaterial && (
        <MaterialEditModal
          material={selectedMaterial}
          isOpen={editModalOpen}
          onClose={() => setEditModalOpen(false)}
        />
      )}
    </div>
  );
}
