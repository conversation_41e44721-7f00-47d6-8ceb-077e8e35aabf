"use client";

import { useState, useEffect } from "react";
import { Modal } from "@/components/UI/Modal";
import { axiosConfig } from "@/config/axiosConfig";
import clienteAxios from "@/config/clienteAxios";
import ImageUploader from "@/components/forms/ImageUploader";
import Swal from "sweetalert2";
import { mutate } from "swr";
import { MaterialInt } from "@/types/ModelTypes";

interface MaterialEditModalProps {
  material: MaterialInt;
  isOpen: boolean;
  onClose: () => void;
}

export default function MaterialEditModal({ material, isOpen, onClose }: MaterialEditModalProps) {
  const [previewImage, setPreviewImage] = useState<{
    file: File;
    preview: string;
  } | null>(null);

  const [formData, setFormData] = useState({
    name: "",
    quantity: 0,
    minMatriculas: 0,
    leadTimeDays: 0,
    rules: "",
    replacementCost: 0,
    image: "",
    imageId: ""
  });

  // Cargar datos del material cuando cambia
  useEffect(() => {
    if (material) {
      setFormData({
        name: material.name,
        quantity: material.quantity,
        minMatriculas: material.minMatriculas,
        leadTimeDays: material.leadTimeDays,
        rules: material.rules,
        replacementCost: material.replacementCost,
        image: material.image || "",
        imageId: material.image_id || ""
      });
    }
  }, [material]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    let parsedValue = value;

    if (["quantity", "minMatriculas", "leadTimeDays"].includes(id)) {
      parsedValue = value === "" ? "0" : value;
    } else if (id === "replacementCost") {
      parsedValue = value === "" ? "0" : value;
    }

    setFormData(prev => ({ ...prev, [id]: parsedValue }));
  };

  const handleUpload = (dataUrl: string, file: File) => {
    setPreviewImage({
      file,
      preview: dataUrl
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const config = axiosConfig(false);
      if (!config) throw new Error("Sesión expirada, por favor inicia sesión.");

      // Crear un objeto FormData para enviar la imagen y los demás campos
      const formDataToSend = new FormData();
      formDataToSend.append("name", formData.name.toString());
      formDataToSend.append("quantity", formData.quantity.toString());
      formDataToSend.append("minMatriculas", formData.minMatriculas.toString());
      formDataToSend.append("leadTimeDays", formData.leadTimeDays.toString());
      formDataToSend.append("rules", formData.rules.toString());
      formDataToSend.append("replacementCost", formData.replacementCost.toString());
      
      // Adjuntar la imagen si hay una nueva
      if (previewImage?.file) {
        formDataToSend.append("image", previewImage.file);
      }

      // Actualizar el material
      const { data } = await clienteAxios.put(
        `/material/update/${material.id}`, 
        formDataToSend, 
        config
      );

      Swal.fire({
        icon: "success",
        title: data.msg || "Material actualizado con éxito",
        showConfirmButton: false,
        timer: 1500
      });

      mutate("/material/getAll");
      onClose();
    } catch (error: any) {
      console.error(error);
      Swal.fire({
        icon: "error",
        title: "Error",
        text: error.response?.data?.msg || "No se pudo actualizar el material",
      });
    }
  };

  return (
    <Modal active={isOpen} setActive={onClose}>
      <div className="p-2">
        <h2 className="text-xl font-bold mb-4 border-b pb-2">Editar material</h2>

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Nombre del material *
                </label>
                <input
                  type="text"
                  id="name"
                  placeholder="Ej: Balón de fútbol"
                  value={formData.name}
                  onChange={handleChange}
                  className="w-full border rounded px-3 py-2 focus:ring focus:ring-blue-300 focus:border-blue-500"
                  required
                />
              </div>

              <div>
                <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">
                  Cantidad disponible *
                </label>
                <input
                  type="number"
                  id="quantity"
                  min="0"
                  placeholder="0"
                  value={formData.quantity}
                  onChange={handleChange}
                  className="w-full border rounded px-3 py-2 focus:ring focus:ring-blue-300 focus:border-blue-500"
                  required
                />
              </div>

              <div>
                <label htmlFor="minMatriculas" className="block text-sm font-medium text-gray-700 mb-1">
                  Matrículas mínimas *
                </label>
                <input
                  type="number"
                  id="minMatriculas"
                  min="0"
                  placeholder="0"
                  value={formData.minMatriculas}
                  onChange={handleChange}
                  className="w-full border rounded px-3 py-2 focus:ring focus:ring-blue-300 focus:border-blue-500"
                  required
                />
              </div>

              <div>
                <label htmlFor="leadTimeDays" className="block text-sm font-medium text-gray-700 mb-1">
                  Días de préstamo *
                </label>
                <input
                  type="number"
                  id="leadTimeDays"
                  min="0"
                  placeholder="0"
                  value={formData.leadTimeDays}
                  onChange={handleChange}
                  className="w-full border rounded px-3 py-2 focus:ring focus:ring-blue-300 focus:border-blue-500"
                  required
                />
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label htmlFor="replacementCost" className="block text-sm font-medium text-gray-700 mb-1">
                  Costo de reposición *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500">$</span>
                  </div>
                  <input
                    type="number"
                    id="replacementCost"
                    step="0.01"
                    min="0"
                    placeholder="0.00"
                    value={formData.replacementCost}
                    onChange={handleChange}
                    className="w-full border rounded px-3 py-2 pl-8 focus:ring focus:ring-blue-300 focus:border-blue-500"
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="mt-4">
            <label htmlFor="rules" className="block text-sm font-medium text-gray-700 mb-1">
              Reglamento o reglas de uso
            </label>
            <textarea
              id="rules"
              placeholder="Describa las reglas de uso para este material..."
              value={formData.rules}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:ring focus:ring-blue-300 focus:border-blue-500"
              rows={4}
            />
          </div>

          {/* Imagen actual */}
          <div className="mt-4">
            <p className="block text-sm font-medium text-gray-700 mb-1">Imagen actual</p>
            <img 
              src={formData.image} 
              alt={formData.name} 
              className="w-32 h-32 object-cover rounded-md border"
            />
          </div>

          {/* ImageUploader para nueva imagen */}
          <div className="mt-4">
            <p className="block text-sm font-medium text-gray-700 mb-1">Cambiar imagen (opcional)</p>
            <ImageUploader
              onUpload={handleUpload}
              previewImage={previewImage}
              setPreviewImage={setPreviewImage}
            />
          </div>

          <div className="flex justify-between mt-6 border-t pt-4">
            <button
              type="button"
              onClick={onClose}
              className="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition duration-200"
            >
              Cancelar
            </button>
            <button
              type="submit"
              className="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700 transition duration-200 flex items-center gap-2"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M19 21H5a2 2 0 01-2-2V5a2 2 0 012-2h11l5 5v11a2 2 0 01-2 2z"/>
                <polyline points="17 21 17 13 7 13 7 21"/>
                <polyline points="7 3 7 8 15 8"/>
              </svg>
              Guardar cambios
            </button>
          </div>
        </form>
      </div>
    </Modal>
  );
}
