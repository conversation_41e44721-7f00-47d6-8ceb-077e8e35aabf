"use client";

import { useState, useEffect } from "react";
import { Modal, useModal } from "@/components/UI/Modal";
import { axiosConfig } from "@/config/axiosConfig";
import clienteAxios from "@/config/clienteAxios";
import Swal from "sweetalert2";
import { mutate } from "swr";
import useSWR from "swr";
import { fetcher } from "@/config/fetcher";
import { MaterialInt } from "@/types/ModelTypes";
import { getCurrentDateForInput, getFutureDateForInput } from "@/utils/dateUtils";

export default function LoanModal() {
  const { active, setActive } = useModal();
  const { data: materials, isLoading } = useSWR<MaterialInt[]>("/material/getAll", fetcher);

  // Estados para cada campo del formulario
  const [materialId, setMaterialId] = useState("");
  const [responsibleId, setResponsibleId] = useState("");
  const [studentIds, setStudentIds] = useState<string[]>([]);
  const [studentIdInput, setStudentIdInput] = useState("");
  const [quantity, setQuantity] = useState(1);
  const [pickupDate, setPickupDate] = useState(getCurrentDateForInput());
  const [returnDate, setReturnDate] = useState(getFutureDateForInput(7));
  const [pickupTime, setPickupTime] = useState("7:00–9:00");
  const [returnTime, setReturnTime] = useState("7:00–9:00");
  const [selectedMaterial, setSelectedMaterial] = useState<MaterialInt | null>(null);

  // Actualizar la fecha de devolución cuando cambia el material seleccionado
  useEffect(() => {
    if (selectedMaterial) {
      setReturnDate(getFutureDateForInput(selectedMaterial.leadTimeDays));
    }
  }, [selectedMaterial]);

  // Actualizar el material seleccionado cuando cambia el ID
  useEffect(() => {
    if (materialId && materials) {
      const material = materials.find(m => m.id.toString() === materialId);
      setSelectedMaterial(material || null);
    } else {
      setSelectedMaterial(null);
    }
  }, [materialId, materials]);

  const handleAddStudentId = () => {
    // Normalize the input: trim whitespace and convert to lowercase
    const normalizedInput = studentIdInput.trim().toLowerCase();

    if (normalizedInput && !studentIds.includes(normalizedInput)) {
      setStudentIds([...studentIds, normalizedInput]);
      setStudentIdInput("");
    } else if (normalizedInput && studentIds.includes(normalizedInput)) {
      // Show a small notification that the ID is already added
      Swal.fire({
        icon: "info",
        title: "Matrícula duplicada",
        text: "Esta matrícula ya ha sido agregada",
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 2000
      });
    }
  };

  const handleRemoveStudentId = (id: string) => {
    setStudentIds(studentIds.filter(studentId => studentId !== id));
  };

  const validateStudentIds = async () => {
    try {
      const config = axiosConfig();
      if (!config) throw new Error("Sesión expirada, por favor inicia sesión.");

      // Ensure all student IDs are normalized (lowercase)
      const normalizedStudentIds = studentIds.map(id => id.toLowerCase());

      const { data } = await clienteAxios.post("/loan/validateStudentIds", {
        studentIds: normalizedStudentIds
      }, config);

      if (!data.valid) {
        Swal.fire({
          icon: "warning",
          title: "Matrículas inválidas",
          html: `
            <p>Las siguientes matrículas no son válidas:</p>
            <ul class="mt-2 text-left">
              ${data.invalidStudents.map((id: string) => `<li>- ${id}</li>`).join("")}
            </ul>
            <p class="mt-3 text-sm">Verifica que las matrículas estén correctas y que los estudiantes estén registrados en el sistema.</p>
          `,
        });
        return false;
      }
      return true;
    } catch (error: any) {
      console.error("Error al validar matrículas:", error);
      Swal.fire({
        icon: "error",
        title: "Error",
        text: error.response?.data?.msg || "No se pudieron validar las matrículas",
      });
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validar que haya al menos una matrícula
    if (studentIds.length === 0) {
      Swal.fire({
        icon: "error",
        title: "Error",
        text: "Debe agregar al menos una matrícula",
      });
      return;
    }

    // Validar que la matrícula responsable esté en la lista
    if (!studentIds.includes(responsibleId)) {
      Swal.fire({
        icon: "error",
        title: "Error",
        text: "La matrícula responsable debe estar en la lista de matrículas",
      });
      return;
    }

    // Validar que la cantidad sea mayor a 0
    if (quantity <= 0) {
      Swal.fire({
        icon: "error",
        title: "Error",
        text: "La cantidad debe ser mayor a 0",
      });
      return;
    }

    // Validar que la fecha de devolución sea posterior a la de recolección
    if (new Date(returnDate) <= new Date(pickupDate)) {
      Swal.fire({
        icon: "error",
        title: "Error",
        text: "La fecha de devolución debe ser posterior a la fecha de recolección",
      });
      return;
    }

    // Validar que haya suficientes matrículas
    if (selectedMaterial && studentIds.length < selectedMaterial.minMatriculas) {
      Swal.fire({
        icon: "error",
        title: "Error",
        text: `Se requieren al menos ${selectedMaterial.minMatriculas} matrículas para este material`,
      });
      return;
    }

    // Validar que las matrículas existan
    const idsValid = await validateStudentIds();
    if (!idsValid) return;

    try {
      const config = axiosConfig();
      if (!config) throw new Error("Sesión expirada, por favor inicia sesión.");

      // Ensure all IDs are normalized (lowercase)
      const normalizedStudentIds = studentIds.map(id => id.toLowerCase());
      const normalizedResponsibleId = responsibleId.toLowerCase();

      const payload = {
        materialId,
        responsibleId: normalizedResponsibleId,
        studentIds: normalizedStudentIds,
        quantity,
        pickupDate,
        returnDate,
        pickupTime,
        returnTime,
      };

      const { data } = await clienteAxios.post("/loan/create", payload, config);

      Swal.fire({
        icon: "success",
        title: data.msg || "Préstamo creado con éxito",
        showConfirmButton: false,
        timer: 1500
      });

      mutate("/loan/getAll"); // Actualiza la lista de préstamos
      setActive(false); // Cierra el modal
      resetForm(); // Limpia los campos
    } catch (error: any) {
      console.error(error);
      Swal.fire({
        icon: "error",
        title: "Error",
        text: error.response?.data?.msg || "No se pudo crear el préstamo",
      });
    }
  };

  const resetForm = () => {
    setMaterialId("");
    setResponsibleId("");
    setStudentIds([]);
    setStudentIdInput("");
    setQuantity(1);
    setPickupDate(getCurrentDateForInput());
    setReturnDate(getFutureDateForInput(7));
    setPickupTime("7:00–9:00");
    setReturnTime("7:00–9:00");
    setSelectedMaterial(null);
  };

  return (
    <>
      <button
        onClick={() => setActive(true)}
        className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition duration-200 flex items-center gap-2"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M12 5v14M5 12h14"/>
        </svg>
        Crear Préstamo
      </button>

      <Modal active={active} setActive={setActive}>
        <div className="p-2">
          <h2 className="text-xl font-bold mb-4 border-b pb-2">Crear nuevo préstamo</h2>

          <form onSubmit={handleSubmit}>
            <div className="space-y-4">
              {/* Material */}
              <div>
                <label htmlFor="materialId" className="block text-sm font-medium text-gray-700 mb-1">
                  Material *
                </label>
                <select
                  id="materialId"
                  value={materialId}
                  onChange={(e) => setMaterialId(e.target.value)}
                  className="w-full border rounded px-3 py-2 focus:ring focus:ring-blue-300 focus:border-blue-500"
                  required
                >
                  <option value="">Selecciona un material</option>
                  {materials?.filter(m => !m.deleted && m.quantity > 0).map((material) => (
                    <option key={material.id} value={material.id}>
                      {material.name} - Disponibles: {material.quantity}
                    </option>
                  ))}
                </select>
              </div>

              {/* Cantidad */}
              <div>
                <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">
                  Cantidad *
                </label>
                <input
                  type="number"
                  id="quantity"
                  min="1"
                  max={selectedMaterial?.quantity || 1}
                  value={quantity}
                  onChange={(e) => setQuantity(parseInt(e.target.value))}
                  className="w-full border rounded px-3 py-2 focus:ring focus:ring-blue-300 focus:border-blue-500"
                  required
                />
                {selectedMaterial && (
                  <p className="text-xs text-gray-500 mt-1">
                    Disponibles: {selectedMaterial.quantity}
                  </p>
                )}
              </div>

              {/* Matrícula responsable */}
              <div>
                <label htmlFor="responsibleId" className="block text-sm font-medium text-gray-700 mb-1">
                  Matrícula responsable *
                </label>
                <select
                  id="responsibleId"
                  value={responsibleId}
                  onChange={(e) => setResponsibleId(e.target.value)}
                  className="w-full border rounded px-3 py-2 focus:ring focus:ring-blue-300 focus:border-blue-500"
                  required
                >
                  <option value="">Selecciona una matrícula</option>
                  {studentIds.map((id) => (
                    <option key={id} value={id}>{id}</option>
                  ))}
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  La matrícula responsable debe estar en la lista de matrículas
                </p>
              </div>

              {/* Matrículas */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Matrículas *
                </label>
                <div className="flex gap-2">
                  <div className="relative flex-1">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 text-sm">A</span>
                    </div>
                    <input
                      type="text"
                      placeholder="Agregar matrícula (ej: a01234567)"
                      value={studentIdInput}
                      onChange={(e) => setStudentIdInput(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          handleAddStudentId();
                        }
                      }}
                      className="w-full border rounded px-3 py-2 pl-8 focus:ring focus:ring-blue-300 focus:border-blue-500"
                    />
                  </div>
                  <button
                    type="button"
                    onClick={handleAddStudentId}
                    className="px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center gap-1"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                    Agregar
                  </button>
                </div>
                <div className="flex justify-between items-center mt-1">
                  {selectedMaterial && (
                    <p className="text-xs text-gray-500">
                      Se requieren al menos {selectedMaterial.minMatriculas} matrículas
                    </p>
                  )}
                  <p className="text-xs text-gray-500">
                    Presiona Enter para agregar
                  </p>
                </div>

                {studentIds.length > 0 ? (
                  <div className="mt-3 p-3 border border-gray-200 rounded-lg bg-gray-50">
                    <div className="flex justify-between items-center mb-2">
                      <p className="text-sm font-medium text-gray-700">Matrículas agregadas ({studentIds.length})</p>
                      {studentIds.length > 0 && (
                        <button
                          type="button"
                          onClick={() => setStudentIds([])}
                          className="text-xs text-red-500 hover:text-red-700"
                        >
                          Limpiar todo
                        </button>
                      )}
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {studentIds.map((id) => (
                        <div key={id} className="bg-white border border-gray-200 rounded-full px-3 py-1 flex items-center gap-1 shadow-sm">
                          <span className="text-sm">{id}</span>
                          <button
                            type="button"
                            onClick={() => handleRemoveStudentId(id)}
                            className="text-red-500 hover:text-red-700 ml-1"
                          >
                            &times;
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="mt-3 p-3 border border-gray-200 rounded-lg bg-gray-50 text-center text-gray-500 text-sm">
                    No hay matrículas agregadas
                  </div>
                )}
              </div>

              {/* Fechas y horarios */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="pickupDate" className="block text-sm font-medium text-gray-700 mb-1">
                    Fecha de recolección *
                  </label>
                  <input
                    type="date"
                    id="pickupDate"
                    value={pickupDate}
                    onChange={(e) => setPickupDate(e.target.value)}
                    className="w-full border rounded px-3 py-2 focus:ring focus:ring-blue-300 focus:border-blue-500"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="pickupTime" className="block text-sm font-medium text-gray-700 mb-1">
                    Horario de recolección *
                  </label>
                  <select
                    id="pickupTime"
                    value={pickupTime}
                    onChange={(e) => setPickupTime(e.target.value)}
                    className="w-full border rounded px-3 py-2 focus:ring focus:ring-blue-300 focus:border-blue-500"
                    required
                  >
                    <option value="7:00–9:00">Mañana (7:00–9:00)</option>
                    <option value="14:00–16:00">Tarde (14:00–16:00)</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="returnDate" className="block text-sm font-medium text-gray-700 mb-1">
                    Fecha de devolución *
                  </label>
                  <input
                    type="date"
                    id="returnDate"
                    value={returnDate}
                    onChange={(e) => setReturnDate(e.target.value)}
                    className="w-full border rounded px-3 py-2 focus:ring focus:ring-blue-300 focus:border-blue-500"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="returnTime" className="block text-sm font-medium text-gray-700 mb-1">
                    Horario de devolución *
                  </label>
                  <select
                    id="returnTime"
                    value={returnTime}
                    onChange={(e) => setReturnTime(e.target.value)}
                    className="w-full border rounded px-3 py-2 focus:ring focus:ring-blue-300 focus:border-blue-500"
                    required
                  >
                    <option value="7:00–9:00">Mañana (7:00–9:00)</option>
                    <option value="14:00–16:00">Tarde (14:00–16:00)</option>
                  </select>
                </div>
              </div>

              {/* Información del material seleccionado */}
              {selectedMaterial && (
                <div className="bg-gray-50 p-3 rounded-lg">
                  <h3 className="font-medium text-gray-700 mb-2">Información del material</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <div className="flex items-center gap-2">
                      <img
                        src={selectedMaterial.image}
                        alt={selectedMaterial.name}
                        className="w-16 h-16 object-cover rounded-md"
                      />
                      <div>
                        <p className="font-medium">{selectedMaterial.name}</p>
                        <p className="text-sm text-gray-500">Disponibles: {selectedMaterial.quantity}</p>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm"><span className="font-medium">Matrículas mínimas:</span> {selectedMaterial.minMatriculas}</p>
                      <p className="text-sm"><span className="font-medium">Días de préstamo:</span> {selectedMaterial.leadTimeDays}</p>
                      <p className="text-sm"><span className="font-medium">Costo de reposición:</span> ${selectedMaterial.replacementCost.toFixed(2)}</p>
                    </div>
                  </div>
                  {selectedMaterial.rules && (
                    <div className="mt-2">
                      <p className="text-sm font-medium">Reglas:</p>
                      <p className="text-sm text-gray-700">{selectedMaterial.rules}</p>
                    </div>
                  )}
                </div>
              )}
            </div>

            <div className="flex justify-between mt-6 border-t pt-4">
              <button
                type="button"
                onClick={() => {
                  setActive(false);
                  resetForm();
                }}
                className="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition duration-200"
              >
                Cancelar
              </button>
              <button
                type="submit"
                className="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700 transition duration-200 flex items-center gap-2"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M19 21H5a2 2 0 01-2-2V5a2 2 0 012-2h11l5 5v11a2 2 0 01-2 2z"/>
                  <polyline points="17 21 17 13 7 13 7 21"/>
                  <polyline points="7 3 7 8 15 8"/>
                </svg>
                Crear Préstamo
              </button>
            </div>
          </form>
        </div>
      </Modal>
    </>
  );
}