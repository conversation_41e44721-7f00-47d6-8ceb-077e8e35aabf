import { useState } from "react";
import useSWR, { mutate } from "swr";
import { fetcher } from "@/config/fetcher";
import { axiosConfig } from "@/config/axiosConfig";
import clienteAxios from "@/config/clienteAxios";
import Swal from "sweetalert2";
import LoanModal from "./LoanModal";
import { LoanInt, LoanStatus } from "@/types/ModelTypes";
import PageLoader from "@/components/shared/PageLoader";
import { formatDate } from "@/utils/dateUtils";

export default function LoanList() {
  const [statusFilter, setStatusFilter] = useState<string>("ALL");
  const [currentPage, setCurrentPage] = useState(0);
  const [activeTab, setActiveTab] = useState<"active" | "returned" | "lost">(
    "active"
  );

  const {
    data: prestamos,
    isLoading,
    error,
  } = useSWR<LoanInt[]>("/loan/getAll", fetcher, {
    // Add revalidation to ensure material data is loaded
    revalidateOnFocus: true,
    dedupingInterval: 5000,
    refreshInterval: 10000, // Refresh every 10 seconds to ensure material data is loaded
    suspense: false,
    onSuccess: (data) => {
      // Check if any loan is missing material data
      const missingMaterialData = data?.some(
        (loan) => loan.materialId && !loan.material
      );
      if (missingMaterialData) {
        // If material data is missing, revalidate after a short delay
        setTimeout(() => mutate("/loan/getAll"), 2000);
      }
    },
  });

  // Función para formatear la fecha
  const formatDateString = (dateString: Date | string | undefined) => {
    if (!dateString) return "Sin fecha";
    return formatDate(new Date(dateString));
  };

  // Función para obtener el color del badge según el estado
  const getStatusBadgeClass = (status: LoanStatus) => {
    switch (status) {
      case LoanStatus.PENDING:
        return "bg-yellow-100 text-yellow-800";
      case LoanStatus.AWAITING_PICKUP:
        return "bg-blue-100 text-blue-800";
      case LoanStatus.ON_LOAN:
        return "bg-green-100 text-green-800";
      case LoanStatus.LATE:
        return "bg-red-100 text-red-800";
      case LoanStatus.LOST:
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Función para obtener el texto del estado en español
  const getStatusText = (status: LoanStatus) => {
    switch (status) {
      case LoanStatus.PENDING:
        return "Pendiente";
      case LoanStatus.AWAITING_PICKUP:
        return "Por recoger";
      case LoanStatus.ON_LOAN:
        return "Prestado";
      case LoanStatus.LATE:
        return "Atrasado";
      case LoanStatus.LOST:
        return "Perdido";
      default:
        return status;
    }
  };

  // Función para actualizar el estado de un préstamo
  const updateLoanStatus = async (
    loanId: number,
    newStatus: LoanStatus,
    notes?: string
  ) => {
    try {
      const config = axiosConfig();
      if (!config) throw new Error("Sesión expirada, por favor inicia sesión.");

      await clienteAxios.put(
        `/loan/updateStatus/${loanId}`,
        {
          status: newStatus,
          notes,
        },
        config
      );

      Swal.fire({
        icon: "success",
        title: "Estado actualizado",
        text: "El estado del préstamo ha sido actualizado correctamente",
        timer: 1500,
        showConfirmButton: false,
      });

      // Actualizar la lista de préstamos y resetear a la primera página
      mutate("/loan/getAll");
      setCurrentPage(0);
    } catch (error: any) {
      console.error("Error al actualizar estado:", error);
      Swal.fire({
        icon: "error",
        title: "Error",
        text:
          error.response?.data?.msg ||
          "No se pudo actualizar el estado del préstamo",
      });
    }
  };

  // Función para aprobar un préstamo
  const approveLoan = (loan: LoanInt) => {
    Swal.fire({
      title: "Aprobar préstamo",
      text: "¿Estás seguro de aprobar este préstamo?",
      icon: "question",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Sí, aprobar",
      cancelButtonText: "Cancelar",
    }).then((result) => {
      if (result.isConfirmed) {
        updateLoanStatus(loan.id, LoanStatus.AWAITING_PICKUP);
      }
    });
  };

  // Función para marcar como recogido
  const markAsPickedUp = (loan: LoanInt) => {
    Swal.fire({
      title: "Marcar como recogido",
      text: "¿Confirmas que el material ha sido recogido?",
      icon: "question",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Sí, confirmar",
      cancelButtonText: "Cancelar",
    }).then((result) => {
      if (result.isConfirmed) {
        updateLoanStatus(loan.id, LoanStatus.ON_LOAN);
      }
    });
  };

  // Función para marcar como devuelto
  const markAsReturned = (loan: LoanInt) => {
    Swal.fire({
      title: "Marcar como devuelto",
      text: "¿Confirmas que el material ha sido devuelto?",
      icon: "question",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Sí, confirmar",
      cancelButtonText: "Cancelar",
    }).then((result) => {
      if (result.isConfirmed) {
        updateLoanStatus(loan.id, LoanStatus.RETURNED);
      }
    });
  };

  // Función para marcar como perdido
  const markAsLost = (loan: LoanInt) => {
    Swal.fire({
      title: "Marcar como perdido",
      text: "¿Estás seguro de marcar este material como perdido?",
      icon: "warning",
      input: "textarea",
      inputPlaceholder: "Ingresa notas sobre la pérdida (opcional)",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      confirmButtonText: "Sí, marcar como perdido",
      cancelButtonText: "Cancelar",
    }).then((result) => {
      if (result.isConfirmed) {
        updateLoanStatus(loan.id, LoanStatus.LOST, result.value);
      }
    });
  };

  // Función para verificar préstamos atrasados
  const checkLateLoans = async () => {
    try {
      const config = axiosConfig();
      if (!config) throw new Error("Sesión expirada, por favor inicia sesión.");

      const { data } = await clienteAxios.post("/loan/checkLate", {}, config);

      Swal.fire({
        icon: "info",
        title: "Verificación completada",
        text: data.msg,
      });

      // Actualizar la lista de préstamos y resetear a la primera página
      mutate("/loan/getAll");
      setCurrentPage(0);
    } catch (error: any) {
      console.error("Error al verificar préstamos atrasados:", error);
      Swal.fire({
        icon: "error",
        title: "Error",
        text:
          error.response?.data?.msg ||
          "No se pudieron verificar los préstamos atrasados",
      });
    }
  };

  // Función para convertir los préstamos a formato CSV y descargarlos
  const downloadLoansAsCSV = (onlyCurrentTab = false) => {
    // Determinar qué préstamos exportar (todos o solo los de la pestaña actual)
    const loansToExport = onlyCurrentTab ? filteredLoans : prestamos;

    if (!loansToExport || loansToExport.length === 0) {
      Swal.fire({
        icon: "info",
        title: "Sin datos",
        text: "No hay préstamos para exportar",
      });
      return;
    }

    // Definir las cabeceras del CSV
    const headers = [
      "ID",
      "Material",
      "Responsable",
      "Matrículas",
      "Cantidad",
      "Fecha Recolección",
      "Hora Recolección",
      "Fecha Entrega",
      "Hora Entrega",
      "Fecha Devolución Real",
      "Estado",
      "Penalización",
      "Notas",
      "Fecha Creación",
    ];

    // Convertir cada préstamo a una fila de CSV
    const csvRows = loansToExport.map((loan) => {
      // Formatear las fechas
      const pickupDate = loan.pickupDate
        ? formatDateString(loan.pickupDate)
        : "";
      const returnDate = loan.returnDate
        ? formatDateString(loan.returnDate)
        : "";
      const actualReturnDate = loan.actualReturnDate
        ? formatDateString(loan.actualReturnDate)
        : "";
      const createdAt = loan.createdAt ? formatDateString(loan.createdAt) : "";

      // Formatear las matrículas como una cadena separada por comas
      const studentIdsString = loan.studentIds
        ? loan.studentIds.join(", ")
        : "";

      // Obtener el nombre del material
      const materialName =
        loan.material?.name ||
        loan.material_name ||
        `Material ID: ${loan.materialId}`;

      // Obtener el texto del estado
      const statusText = getStatusText(loan.status);

      // Crear la fila
      return [
        loan.id,
        materialName,
        loan.responsibleId,
        studentIdsString,
        loan.quantity,
        pickupDate,
        loan.pickupTime,
        returnDate,
        loan.returnTime,
        actualReturnDate,
        statusText,
        loan.penaltyApplied ? "Sí" : "No",
        loan.penaltyNotes || "",
        createdAt,
      ];
    });

    // Unir cabeceras y filas
    const csvContent = [
      headers.join(","),
      ...csvRows.map((row) =>
        row
          .map((cell) =>
            // Escapar comas y comillas en los valores
            typeof cell === "string" &&
            (cell.includes(",") || cell.includes('"') || cell.includes("\n"))
              ? `"${cell.replace(/"/g, '""')}"`
              : cell
          )
          .join(",")
      ),
    ].join("\n");

    // Crear un blob con el contenido CSV
    const bom = "\uFEFF";
    const csvWithBom = bom + csvContent;

    const blob = new Blob([csvWithBom], { type: "text/csv;charset=utf-8;" });

    // Crear un enlace para descargar el archivo
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);

    // Configurar el enlace
    link.setAttribute("href", url);

    // Nombre del archivo según lo que se está exportando
    const fileName = onlyCurrentTab
      ? `prestamos_${activeTab}_${new Date().toISOString().split("T")[0]}.csv`
      : `prestamos_todos_${new Date().toISOString().split("T")[0]}.csv`;

    link.setAttribute("download", fileName);
    link.style.visibility = "hidden";

    // Añadir el enlace al DOM, hacer clic en él y luego eliminarlo
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Filtrar préstamos según la pestaña activa y el estado seleccionado
  const tabFilteredLoans = prestamos?.filter((loan) => {
    if (activeTab === "active") {
      return (
        loan.status === LoanStatus.PENDING ||
        loan.status === LoanStatus.AWAITING_PICKUP ||
        loan.status === LoanStatus.ON_LOAN ||
        loan.status === LoanStatus.LATE
      );
    } else if (activeTab === "returned") {
      return loan.status === LoanStatus.RETURNED;
    } else if (activeTab === "lost") {
      return loan.status === LoanStatus.LOST;
    }
    return true;
  });

  // Filtrar préstamos según el estado seleccionado (dentro de la pestaña activa)
  const filteredLoans =
    statusFilter === "ALL"
      ? tabFilteredLoans
      : tabFilteredLoans?.filter((loan) => loan.status === statusFilter);

  // Configuración de paginación
  const itemsPerPage = 10;
  const offset = currentPage * itemsPerPage;
  const currentItems = filteredLoans?.slice(offset, offset + itemsPerPage);
  const pageCount = Math.max(
    1,
    Math.ceil((filteredLoans?.length || 0) / itemsPerPage)
  );

  // Handlers para la paginación
  const handleClickNext = () => {
    if (currentPage < pageCount - 1) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handleClickPrev = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  };

  if (isLoading) return <PageLoader />;

  if (error) {
    return (
      <div className="w-full text-center py-8">
        <p className="text-red-600 font-semibold">
          Error al cargar los préstamos
        </p>
        <button
          onClick={() => mutate("/loan/getAll")}
          className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Reintentar
        </button>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="flex flex-col sm:flex-row justify-between gap-2 mb-4">
        {/* Botones para descargar CSV */}
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => downloadLoansAsCSV(true)}
            className="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center gap-1 text-sm sm:text-base"
          >
            <i className="fa-solid fa-download"></i>
            <span className="hidden sm:inline">
              Descargar CSV (Pestaña actual)
            </span>
            <span className="sm:hidden">CSV actual</span>
          </button>
          <button
            onClick={() => downloadLoansAsCSV(false)}
            className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center gap-1 text-sm sm:text-base"
          >
            <i className="fa-solid fa-download"></i>
            <span className="hidden sm:inline">
              Descargar todos los préstamos
            </span>
            <span className="sm:hidden">CSV todos</span>
          </button>
        </div>

        {/* Botón para crear préstamo */}
        <div className="flex justify-center">
          <LoanModal />
        </div>
      </div>
      {/* Tabs principales */}
      <div className="mb-4 flex overflow-x-auto border-b">
        <button
          onClick={() => {
            setActiveTab("active");
            setStatusFilter("ALL");
            setCurrentPage(0);
          }}
          className={`px-4 py-2 font-medium text-sm flex items-center whitespace-nowrap ${
            activeTab === "active"
              ? "text-blue-600 border-b-2 border-blue-600"
              : "text-gray-500 hover:text-gray-700"
          }`}
        >
          Préstamos Activos
        </button>

        <button
          onClick={() => {
            setActiveTab("returned");
            setStatusFilter("ALL");
            setCurrentPage(0);
          }}
          className={`px-4 py-2 font-medium text-sm flex items-center whitespace-nowrap ${
            activeTab === "returned"
              ? "text-blue-600 border-b-2 border-blue-600"
              : "text-gray-500 hover:text-gray-700"
          }`}
        >
          Préstamos Devueltos
        </button>

        <button
          onClick={() => {
            setActiveTab("lost");
            setStatusFilter("ALL");
            setCurrentPage(0);
          }}
          className={`px-4 py-2 font-medium text-sm flex items-center whitespace-nowrap ${
            activeTab === "lost"
              ? "text-blue-600 border-b-2 border-blue-600"
              : "text-gray-500 hover:text-gray-700"
          }`}
        >
          Préstamos Perdidos
        </button>
      </div>

      {/* Filtros y botones específicos para la pestaña activa */}
      <div className="mb-4 flex flex-wrap justify-between items-center">
        <div className="flex flex-wrap gap-2 mb-2 sm:mb-0">
          {activeTab === "active" && (
            <>
              <button
                onClick={() => {
                  setStatusFilter("ALL");
                  setCurrentPage(0);
                }}
                className={`px-3 py-1 rounded text-sm font-medium ${
                  statusFilter === "ALL"
                    ? "bg-blue-600 text-white"
                    : "bg-gray-200 text-gray-700"
                }`}
              >
                Todos
              </button>
              <button
                onClick={() => {
                  setStatusFilter(LoanStatus.PENDING);
                  setCurrentPage(0);
                }}
                className={`px-3 py-1 rounded text-sm font-medium ${
                  statusFilter === LoanStatus.PENDING
                    ? "bg-blue-600 text-white"
                    : "bg-yellow-100 text-yellow-800"
                }`}
              >
                Pendientes
              </button>
              <button
                onClick={() => {
                  setStatusFilter(LoanStatus.AWAITING_PICKUP);
                  setCurrentPage(0);
                }}
                className={`px-3 py-1 rounded text-sm font-medium ${
                  statusFilter === LoanStatus.AWAITING_PICKUP
                    ? "bg-blue-600 text-white"
                    : "bg-blue-100 text-blue-800"
                }`}
              >
                Por recoger
              </button>
              <button
                onClick={() => {
                  setStatusFilter(LoanStatus.ON_LOAN);
                  setCurrentPage(0);
                }}
                className={`px-3 py-1 rounded text-sm font-medium ${
                  statusFilter === LoanStatus.ON_LOAN
                    ? "bg-blue-600 text-white"
                    : "bg-green-100 text-green-800"
                }`}
              >
                Prestados
              </button>
              <button
                onClick={() => {
                  setStatusFilter(LoanStatus.LATE);
                  setCurrentPage(0);
                }}
                className={`px-3 py-1 rounded text-sm font-medium ${
                  statusFilter === LoanStatus.LATE
                    ? "bg-blue-600 text-white"
                    : "bg-red-100 text-red-800"
                }`}
              >
                Atrasados
              </button>
              <button
                onClick={checkLateLoans}
                className="px-3 py-1 rounded text-sm font-medium bg-orange-500 text-white hover:bg-orange-600"
              >
                Verificar préstamos atrasados
              </button>
            </>
          )}
        </div>
      </div>

      {/* Desktop view */}
      <div className="hidden md:block overflow-x-auto">
        <table className="min-w-full border shadow-md rounded-lg overflow-hidden">
          <thead className="bg-gray-200 text-gray-700">
            <tr>
              <th className="p-3 text-left">Material</th>
              <th className="p-3 text-left">Responsable</th>
              <th className="p-3 text-left">Matrículas</th>
              <th className="p-3 text-left">Cantidad</th>
              <th className="p-3 text-left">Fecha Recolección</th>
              <th className="p-3 text-left">Fecha Entrega</th>
              <th className="p-3 text-left">Estatus</th>
              <th className="p-3 text-left">Acciones</th>
            </tr>
          </thead>
          <tbody>
            {currentItems?.map((loan) => (
              <tr key={loan.id} className="border-b hover:bg-gray-50">
                <td className="p-2">
                  <div className="flex items-center gap-2">
                    {loan.material?.image || loan.material_image ? (
                      <img
                        src={loan.material?.image || loan.material_image}
                        alt={
                          loan.material?.name ||
                          loan.material_name ||
                          "Material"
                        }
                        className="w-12 h-12 object-cover rounded-md"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-gray-200 rounded-md flex items-center justify-center">
                        <span className="text-xs text-gray-500">
                          Sin imagen
                        </span>
                      </div>
                    )}
                    <span className="font-medium">
                      {loan.material?.name ||
                        loan.material_name ||
                        `Material ID: ${loan.materialId}`}
                    </span>
                  </div>
                </td>
                <td className="p-2">{loan.responsibleId}</td>
                <td className="p-2">
                  <div className="max-h-20 overflow-y-auto">
                    {loan.studentIds?.map((id, index) => (
                      <div
                        key={index}
                        className="text-xs bg-gray-100 rounded px-2 py-1 mb-1"
                      >
                        {id}
                      </div>
                    ))}
                  </div>
                </td>
                <td className="p-2">{loan.quantity}</td>
                <td className="p-2">
                  {formatDateString(loan.pickupDate)}
                  <div className="text-xs text-gray-500">{loan.pickupTime}</div>
                </td>
                <td className="p-2">
                  {formatDateString(loan.returnDate)}
                  <div className="text-xs text-gray-500">{loan.returnTime}</div>
                </td>
                <td className="p-2">
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(
                      loan.status
                    )}`}
                  >
                    {getStatusText(loan.status)}
                  </span>
                </td>
                <td className="p-2">
                  <div className="flex flex-col gap-1">
                    {loan.status === LoanStatus.PENDING && (
                      <button
                        onClick={() => approveLoan(loan)}
                        className="text-xs px-2 py-1 rounded bg-green-600 text-white hover:bg-green-700"
                      >
                        Aprobar
                      </button>
                    )}

                    {loan.status === LoanStatus.AWAITING_PICKUP && (
                      <button
                        onClick={() => markAsPickedUp(loan)}
                        className="text-xs px-2 py-1 rounded bg-blue-600 text-white hover:bg-blue-700"
                      >
                        Marcar recogido
                      </button>
                    )}

                    {(loan.status === LoanStatus.ON_LOAN ||
                      loan.status === LoanStatus.LATE) && (
                      <button
                        onClick={() => markAsReturned(loan)}
                        className="text-xs px-2 py-1 rounded bg-green-600 text-white hover:bg-green-700"
                      >
                        Marcar devuelto
                      </button>
                    )}

                    {(loan.status === LoanStatus.ON_LOAN ||
                      loan.status === LoanStatus.LATE) && (
                      <button
                        onClick={() => markAsLost(loan)}
                        className="text-xs px-2 py-1 rounded bg-red-600 text-white hover:bg-red-700"
                      >
                        Marcar perdido
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile view */}
      <div className="md:hidden space-y-4">
        {currentItems?.map((loan) => (
          <div
            key={loan.id}
            className="bg-white rounded-lg shadow-md p-4 border border-gray-200"
          >
            <div className="flex justify-between items-start mb-3">
              <div className="flex gap-3">
                {loan.material?.image || loan.material_image ? (
                  <img
                    src={loan.material?.image || loan.material_image}
                    alt={
                      loan.material?.name || loan.material_name || "Material"
                    }
                    className="w-16 h-16 object-cover rounded-md"
                  />
                ) : (
                  <div className="w-16 h-16 bg-gray-200 rounded-md flex items-center justify-center">
                    <span className="text-xs text-gray-500">Sin imagen</span>
                  </div>
                )}
                <div>
                  <h3 className="font-semibold text-lg">
                    {loan.material?.name ||
                      loan.material_name ||
                      `Material ID: ${loan.materialId}`}
                  </h3>
                  <p className="text-sm text-gray-600">
                    Responsable: {loan.responsibleId}
                  </p>
                  <p className="text-sm text-gray-600">
                    Cantidad: {loan.quantity}
                  </p>
                </div>
              </div>
              <span
                className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(
                  loan.status
                )}`}
              >
                {getStatusText(loan.status)}
              </span>
            </div>

            <div className="grid grid-cols-2 gap-2 mb-3">
              <div>
                <p className="text-sm text-gray-600">Recolección:</p>
                <p className="font-medium">
                  {formatDateString(loan.pickupDate)}
                </p>
                <p className="text-xs text-gray-500">{loan.pickupTime}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Entrega:</p>
                <p className="font-medium">
                  {formatDateString(loan.returnDate)}
                </p>
                <p className="text-xs text-gray-500">{loan.returnTime}</p>
              </div>
            </div>

            <div className="mb-3">
              <p className="text-sm text-gray-600 mb-1">Matrículas:</p>
              <div className="flex flex-wrap gap-1">
                {loan.studentIds?.map((id, index) => (
                  <div
                    key={index}
                    className="text-xs bg-gray-100 rounded px-2 py-1"
                  >
                    {id}
                  </div>
                ))}
              </div>
            </div>

            <div className="flex flex-wrap gap-2 mt-3">
              {loan.status === LoanStatus.PENDING && (
                <button
                  onClick={() => approveLoan(loan)}
                  className="flex-1 py-2 rounded bg-green-600 text-white hover:bg-green-700"
                >
                  Aprobar
                </button>
              )}

              {loan.status === LoanStatus.AWAITING_PICKUP && (
                <button
                  onClick={() => markAsPickedUp(loan)}
                  className="flex-1 py-2 rounded bg-blue-600 text-white hover:bg-blue-700"
                >
                  Marcar recogido
                </button>
              )}

              {(loan.status === LoanStatus.ON_LOAN ||
                loan.status === LoanStatus.LATE) && (
                <button
                  onClick={() => markAsReturned(loan)}
                  className="flex-1 py-2 rounded bg-green-600 text-white hover:bg-green-700"
                >
                  Marcar devuelto
                </button>
              )}

              {(loan.status === LoanStatus.ON_LOAN ||
                loan.status === LoanStatus.LATE) && (
                <button
                  onClick={() => markAsLost(loan)}
                  className="flex-1 py-2 rounded bg-red-600 text-white hover:bg-red-700"
                >
                  Marcar perdido
                </button>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* No hay préstamos */}
      {(!filteredLoans || filteredLoans.length === 0) && (
        <div className="text-center py-8">
          <p className="text-gray-500">
            {activeTab === "active"
              ? statusFilter === "ALL"
                ? "No hay préstamos activos registrados"
                : `No hay préstamos activos con estado "${getStatusText(
                    statusFilter as LoanStatus
                  )}"`
              : activeTab === "returned"
              ? "No hay préstamos devueltos registrados"
              : "No hay préstamos perdidos registrados"}
          </p>
        </div>
      )}

      {/* Pagination */}
      {filteredLoans && filteredLoans.length > 0 && (
        <div className="flex flex-wrap justify-center mt-6 gap-2">
          <button
            onClick={() => setCurrentPage(0)}
            disabled={currentPage === 0}
            className="w-12 h-12 flex items-center justify-center rounded-md hover:bg-blue-50 disabled:opacity-50 text-gray-600"
            aria-label="Primera página"
          >
            <i className="fa-solid fa-angles-left"></i>
          </button>
          <button
            onClick={handleClickPrev}
            disabled={currentPage === 0}
            className="w-12 h-12 flex items-center justify-center rounded-md hover:bg-blue-50 disabled:opacity-50 text-gray-600"
            aria-label="Página anterior"
          >
            <i className="fa-solid fa-angle-left"></i>
          </button>

          {/* Mobile pagination - just show current page */}
          <div className="md:hidden flex items-center">
            <span className="mx-2 text-sm">
              Página {currentPage + 1} de {pageCount}
            </span>
          </div>

          {/* Desktop pagination - show page numbers */}
          <div className="hidden md:flex gap-2">
            {Array.from({ length: Math.min(5, pageCount) }, (_, i) => {
              // Show pages around current page
              let pageToShow = currentPage - 2 + i;
              if (currentPage < 2) pageToShow = i;
              if (currentPage > pageCount - 3) pageToShow = pageCount - 5 + i;
              if (pageToShow >= 0 && pageToShow < pageCount) {
                return (
                  <button
                    key={pageToShow}
                    onClick={() => setCurrentPage(pageToShow)}
                    className={`w-12 h-12 flex items-center justify-center rounded-md text-lg font-medium ${
                      currentPage === pageToShow
                        ? "bg-blue-500 text-white"
                        : "hover:bg-blue-50 text-gray-800"
                    }`}
                    aria-label={`Página ${pageToShow + 1}`}
                    aria-current={
                      currentPage === pageToShow ? "page" : undefined
                    }
                  >
                    {pageToShow + 1}
                  </button>
                );
              }
              return null;
            })}
          </div>

          <button
            onClick={handleClickNext}
            disabled={currentPage >= pageCount - 1}
            className="w-12 h-12 flex items-center justify-center rounded-md hover:bg-blue-50 disabled:opacity-50 text-gray-600"
            aria-label="Página siguiente"
          >
            <i className="fa-solid fa-angle-right"></i>
          </button>
          <button
            onClick={() => setCurrentPage(pageCount - 1)}
            disabled={currentPage >= pageCount - 1}
            className="w-12 h-12 flex items-center justify-center rounded-md hover:bg-blue-50 disabled:opacity-50 text-gray-600"
            aria-label="Última página"
          >
            <i className="fa-solid fa-angles-right"></i>
          </button>
        </div>
      )}
    </div>
  );
}
