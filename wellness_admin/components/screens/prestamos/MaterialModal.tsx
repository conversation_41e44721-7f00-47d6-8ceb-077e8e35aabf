"use client";

import { useState } from "react";
import { Modal, useModal } from "@/components/UI/Modal";
import { axiosConfig } from "@/config/axiosConfig";
import clienteAxios from "@/config/clienteAxios";
import ImageUploader from "@/components/forms/ImageUploader";
import Swal from "sweetalert2";
import { mutate } from "swr";


export default function ModalMaterial() {
  const { active, setActive } = useModal();

  const [previewImage, setPreviewImage] = useState<{
    file: File;
    preview: string;
  } | null>(null);

  const [formData, setFormData] = useState({
    name: "",
    quantity: 0,
    minMatriculas: 0,
    leadTimeDays: 0,
    rules: "",
    replacementCost: 0,
    // image e imageId quedan por ahora en blanco, ya que la imagen se mandará como archivo.
    image: "",
    imageId: ""
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    let parsedValue = value;

    if (["quantity", "minMatriculas", "leadTimeDays"].includes(id)) {
      parsedValue = value === "" ? "0" : value;
    } else if (id === "replacementCost") {
      parsedValue = value === "" ? "0" : value;
    }

    setFormData(prev => ({ ...prev, [id]: parsedValue }));
  };

  const handleUpload = (dataUrl: string, file: File) => {
    // Puedes guardar el dataUrl para la previsualización, pero lo esencial es tener el archivo en previewImage.
    setFormData(prev => ({
      ...prev,
      image: dataUrl // opcional, si quieres conservar la representación en base64
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const config = axiosConfig(false); 
      if (!config) throw new Error("Sesión expirada, por favor inicia sesión.");

      // Crear un objeto FormData para enviar la imagen y los demás campos.
      const formDataToSend = new FormData();
      formDataToSend.append("name", formData.name);
      formDataToSend.append("quantity", formData.quantity.toString());
      formDataToSend.append("minMatriculas", formData.minMatriculas.toString());
      formDataToSend.append("leadTimeDays", formData.leadTimeDays.toString());
      formDataToSend.append("rules", formData.rules);
      formDataToSend.append("replacementCost", formData.replacementCost.toString());

      // Adjuntar la imagen real desde previewImage
      if (previewImage?.file) {
        formDataToSend.append("image", previewImage.file);
      } else {
        throw new Error("Debes seleccionar una imagen.");
      }

      const { data } = await clienteAxios.post("/material", formDataToSend, config);


      Swal.fire({
        icon: "success",
        title: data.msg || "Material creado con éxito",
        showConfirmButton: false,
        timer: 1500
      });

      mutate("/material/getAll");
      setActive(false);
      resetForm();
    } catch (error: any) {
      console.error(error);
      Swal.fire({
        icon: "error",
        title: "Error",
        text: error.response?.data?.msg || "No se pudo crear el material",
      });
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      quantity: 0,
      minMatriculas: 0,
      leadTimeDays: 0,
      rules: "",
      replacementCost: 0,
      image: "",
      imageId: ""
    });
    setPreviewImage(null);
  };

  return (
    <>
      <button
        onClick={() => setActive(true)}
        className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition duration-200 flex items-center gap-2"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M12 5v14M5 12h14"/>
        </svg>
        Crear Material
      </button>

      <Modal active={active} setActive={setActive}>
        <div className="p-2">
          <h2 className="text-xl font-bold mb-4 border-b pb-2">Crear nuevo material</h2>

          <form onSubmit={handleSubmit} className="max-h-[80vh] overflow-y-auto px-2">
            <div className="bg-blue-50 p-4 rounded-lg mb-6 border border-blue-200">
              <h3 className="text-blue-800 font-medium mb-2">Información del Material</h3>
              <p className="text-sm text-blue-700">Complete la información básica del material que desea registrar. Los campos marcados con * son obligatorios.</p>
            </div>

            {/* Nombre y Costo - Primera fila */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Nombre del material *
                </label>
                <input
                  type="text"
                  id="name"
                  placeholder="Ej: Balón de fútbol"
                  value={formData.name}
                  onChange={handleChange}
                  className="w-full border rounded px-3 py-2 focus:ring focus:ring-blue-300 focus:border-blue-500"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">Ingrese un nombre descriptivo y claro</p>
              </div>

              <div>
                <label htmlFor="replacementCost" className="block text-sm font-medium text-gray-700 mb-1">
                  Costo de reposición *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500">$</span>
                  </div>
                  <input
                    type="number"
                    id="replacementCost"
                    step="0.01"
                    min="0"
                    placeholder="0.00"
                    value={formData.replacementCost}
                    onChange={handleChange}
                    className="w-full border rounded px-3 py-2 pl-8 focus:ring focus:ring-blue-300 focus:border-blue-500"
                    required
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">Costo en caso de pérdida o daño</p>
              </div>
            </div>

            {/* Cantidades - Segunda fila */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div>
                <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">
                  Cantidad disponible *
                </label>
                <input
                  type="number"
                  id="quantity"
                  min="1"
                  placeholder="1"
                  value={formData.quantity}
                  onChange={handleChange}
                  className="w-full border rounded px-3 py-2 focus:ring focus:ring-blue-300 focus:border-blue-500"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">Número de unidades disponibles</p>
              </div>

              <div>
                <label htmlFor="minMatriculas" className="block text-sm font-medium text-gray-700 mb-1">
                  Matrículas mínimas *
                </label>
                <input
                  type="number"
                  id="minMatriculas"
                  min="1"
                  placeholder="1"
                  value={formData.minMatriculas}
                  onChange={handleChange}
                  className="w-full border rounded px-3 py-2 focus:ring focus:ring-blue-300 focus:border-blue-500"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">Mínimo de estudiantes requeridos</p>
              </div>

              <div>
                <label htmlFor="leadTimeDays" className="block text-sm font-medium text-gray-700 mb-1">
                  Días de préstamo *
                </label>
                <input
                  type="number"
                  id="leadTimeDays"
                  min="1"
                  placeholder="7"
                  value={formData.leadTimeDays}
                  onChange={handleChange}
                  className="w-full border rounded px-3 py-2 focus:ring focus:ring-blue-300 focus:border-blue-500"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">Duración máxima del préstamo</p>
              </div>
            </div>

            {/* Reglas de uso */}
            <div className="mb-6">
              <label htmlFor="rules" className="block text-sm font-medium text-gray-700 mb-1">
                Reglamento o reglas de uso
              </label>
              <textarea
                id="rules"
                placeholder="Describa las reglas de uso para este material..."
                value={formData.rules}
                onChange={handleChange}
                className="w-full border rounded px-3 py-2 focus:ring focus:ring-blue-300 focus:border-blue-500"
                rows={4}
              />
              <p className="text-xs text-gray-500 mt-1">Instrucciones especiales, restricciones o cuidados necesarios</p>
            </div>

            {/* Imagen */}
            <div className="bg-gray-50 p-4 rounded-lg mb-6 border border-gray-200">
              <h3 className="text-gray-800 font-medium mb-2">Imagen del Material *</h3>
              <p className="text-sm text-gray-700 mb-3">Suba una imagen clara del material para facilitar su identificación.</p>
              <ImageUploader
                onUpload={handleUpload}
                previewImage={previewImage}
                setPreviewImage={setPreviewImage}
              />
            </div>

            <div className="flex justify-between mt-6 border-t pt-4">
              <button
                type="button"
                onClick={() => {
                  setActive(false);
                  resetForm();
                }}
                className="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition duration-200"
              >
                Cancelar
              </button>
              <button
                type="submit"
                className="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700 transition duration-200 flex items-center gap-2"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M19 21H5a2 2 0 01-2-2V5a2 2 0 012-2h11l5 5v11a2 2 0 01-2 2z"/>
                  <polyline points="17 21 17 13 7 13 7 21"/>
                  <polyline points="7 3 7 8 15 8"/>
                </svg>
                Guardar
              </button>
            </div>
          </form>
        </div>
      </Modal>
    </>
  );
}