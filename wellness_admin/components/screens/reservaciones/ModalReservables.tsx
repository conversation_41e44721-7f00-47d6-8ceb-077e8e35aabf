"use client";

import { Fragment, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

import useSWR, { mutate } from "swr";

import { Dialog, Switch, Transition } from "@headlessui/react";
import { RootState } from "@/redux/store";
import {
  toggleModalReservable,
} from "@/redux/slices/modalSlice";
import { fetcher } from "@/config/fetcher";
import { SpaceInt } from "@/types/ModelTypes";
import { handleError } from "@/utils/errorHandler";
import { axiosConfig } from "@/config/axiosConfig";
import clienteAxios from "@/config/clienteAxios";
import Swal from "sweetalert2";
import { Admins } from "@/types/AppTypes";


import { format } from "date-fns";
import { tailwindColors } from "@/utils/tailwindColors";
import Link from "next/link";
import { setReservable } from "@/redux/slices/calendarSlice";
import { generarIdUnico } from "@/utils/helpers";

function formatearFechaUTC(fecha: Date) {
  const fechaFormateada = format(fecha, "yyyy-MM-dd'T'HH:mm");
  return fechaFormateada;
}

export default function ModalReservables() {
  /**  Información del reservable*/
  const {reservable} = useSelector((state: RootState) => state.calendar);

  console.log(reservable)

  const { modalReservable } = useSelector((state: RootState) => state.modal);

  const { data: spaces } = useSWR<SpaceInt[]>("/space/spaces", fetcher);

  const { data: admins } = useSWR<Admins[]>("/admin", fetcher);

  const [id, setId] = useState<null | number>(null);

  const [space, setSpace] = useState("");
  const [init_date, setInit_date] = useState("");
  const [end_date, setEnd_date] = useState("");
  const [repeats, setRepeats] = useState(false);
  const [booking, setBooking] = useState(false)
  const [color, setColor] = useState("");
  const [admin, setAdmin] = useState("");
  const [quota, setQuota] = useState(0);
  const [onlineQuota, setOnlineQuota] = useState(0);
  const [selectedDays, setSelectedDays] = useState<number[]>([]);
  const [repetiton_date, setRepetition_date] = useState("");



  useEffect(() => {
    if (
      reservable
    ) {
      setId(reservable?.id);
      setSpace(reservable?.spaceId.toString());
      setColor(reservable?.color);
      setAdmin(reservable?.adminId.toString());
      setQuota(reservable?.quota);
      setOnlineQuota(reservable?.onlineQuota);
      setBooking(reservable?.booking ?? false)

      setInit_date(formatearFechaUTC(new Date(reservable?.init_date)));
      setEnd_date(formatearFechaUTC(new Date(reservable?.end_date)));
    }
  }, [reservable]);

  // Use effect encargado de colocar la quota por defecto automaticamente
  useEffect(() => {
    if (space !== "" && spaces) {
      const spaceSelected = spaces?.find((sp) => sp.id === +space);
      if (spaceSelected) {
        setQuota(spaceSelected.quota);
        setOnlineQuota(spaceSelected.onlineQuota);
      }
    }
  }, [space]);

  const days = ["LU", "MA", "MI", "JU", "VI", "SA", "DO"];

  const cambiarDay = (index: number) => {
    setSelectedDays((pasado) => {
      if (pasado.includes(index)) {
        return pasado.filter((day) => day !== index);
      } else {
        return [...pasado, index];
      }
    });
  };

  const dispatch = useDispatch();

  // Función encargada de manejar el evento de que se elimine un reservable
  const handleDelete = async () => {
    // Saldrá una alerta que pregunte si se quieren eliminar todos los reservablesm solo este y un boton de cancelacion
    const result = await Swal.fire({
      title: "¿Estás seguro?",
      text: "¿Quieres borrar solo este o todos?",
      icon: "warning",
      showCancelButton: true,
      showDenyButton: true,
      confirmButtonText: "Todos",
      denyButtonText: "Solo este",
      cancelButtonText: "Cancelar",
      confirmButtonColor: "#d33",
      denyButtonColor: "#3085d6",
    });

    try {
      const config = axiosConfig();
      if (!config) {
        throw new Error("Sesión vencida inicia sesion");
      }

      // Inicializamos la variable que determina si son todos o solo será uno
      let all = false;

      if (result.isConfirmed) {
        // El usuario hizo clic en "Todos"
        all = true;
      } else if (result.isDenied) {
        // El usuario hizo clic en "Solo este" no hacemos nada
      } else {
        // El usuario hizo clic en "Cancelar" o hizo clic fuera de la alerta entonces cancelamos la accion
        return;
      }

      // Enviamos la petición al servidor
      const { data } = await clienteAxios.delete(
        `/reservable/deleteReservable/${id}/${all ? "true" : "false"}`,
        config
      );

      // Mostramos el mensaje
      Swal.fire({
        icon: "success",
        title: data.msg,
      });
      // Actualizamos los datos
      mutate("/reservable/getAllReservables");
      // Si todo salio bien actualizamos el estado del reservable y lo eliminamos
      dispatch(toggleModalReservable());
    } catch (error: any) {
      return handleError(error);
    }
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault();

    try {
      const config = axiosConfig();
      if (!config) {
        throw new Error("Sesión vencida inicia sesion");
      }

      if ([space, init_date, end_date].includes("")) {
        throw new Error("Todos los campos son obligatorios");
      }

      // Generamos el payload que comparten ambos
      const payload = {
        spaceId: +space,
        init_date: new Date(init_date).toISOString(),
        end_date: new Date(end_date).toISOString(),
        adminId: +admin,
        quota: quota,
        onlineQuota: onlineQuota,
        color: color,
        booking
      };

      // En caso de ser una creación
      if (!reservable) {
        const { data } = await clienteAxios.post(
          "/reservable/createReservable",
          {
            ...payload,
            repeats,
            repeticion: selectedDays,
            repeticion_fecha: repetiton_date,
          },
          config
        );

      
        Swal.fire({
          icon: "success",
          title: data.msg,
        })
      }else {
        // En caso de ser una edición antes debemos preguntar si se pretenden editar todos los campos
        const result = await Swal.fire({
          title: "¿Estás seguro?",
          text: "¿Deseas editar solo este o todos?",
          icon: "warning",
          showCancelButton: true,
          showDenyButton: true,
          confirmButtonText: "Todos",
          denyButtonText: "Solo este",
          cancelButtonText: "Cancelar",
          confirmButtonColor: "#22C55E",
          denyButtonColor: "#3085d6",
        });

        // Inicializamos la variable que determina si son todos o solo será uno
        let all = false;

        if (result.isConfirmed) {
          // El usuario hizo clic en "Todos"
          all = true;
        } else if (result.isDenied) {
          // El usuario hizo clic en "Solo este" no hacemos nada
        } else {
          // El usuario hizo clic en "Cancelar" o hizo clic fuera de la alerta entonces cancelamos la accion
          return;
        }

        // Ya con la decisión del usuario solo tenemos que mandar la petición
        const { data } = await clienteAxios.put(
          "/reservable/updateReservable",
          {
            ...payload,
            id,
            all
          },
          config
        );

        // Mostramos la respuesta del servidor
        Swal.fire({
          icon: "success",
          title: data.msg,
        });
    
      }

      // Actualizamos los datos
      mutate("/reservable/getAllReservables")

      // Cerramos la modal y limpiamos los datos
      dispatch(toggleModalReservable());
      dispatch(setReservable(null));
      setSpace("");
      setInit_date("");
      setEnd_date("");
      setColor("");
      setAdmin("");
      setQuota(0);
      setOnlineQuota(0);
      setRepeats(false)
      setSelectedDays([])
      setBooking(false)
    } catch (error: any) {
      console.log(error);
      return handleError(error);
    }
  };

  const handleClose = () => {
    setSpace("");
    setInit_date("");
    setEnd_date("");
    setColor("");
    setAdmin("");
    setQuota(0);
    setOnlineQuota(0);
    setBooking(false)

    
    dispatch(toggleModalReservable());
  };

  return (
    <Transition.Root show={modalReservable} as={Fragment}>
      <Dialog
        as="div"
        className="fixed z-30 inset-0 overflow-y-auto "
        onClose={() => {
          handleClose();
        }}
      >
        <div className=" min-h-screen pt-4 px-4 pb-20 text-center block p-0 ">
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <Dialog.Overlay className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </Transition.Child>

          {/* This element is to trick the browser into centering the modal contents. */}
          <span
            className=" inline-block align-middle h-screen"
            aria-hidden="true"
          >
            &#8203;
          </span>

          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0 translate-y-0 scale-95"
            enterTo="opacity-100 translate-y-0 scale-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100 translate-y-0 scale-100"
            leaveTo="opacity-0 translate-y-0 scale-95"
          >
            <div className="inline-block  bg-white rounded-lg px-4 pt-3 pb-6 overflow-hidden shadow-xl transform transition-all align-middle w-5/6 md:w-2/3 lg:w-1/3 p-6">
              <form onSubmit={handleSubmit} className="">
                <Dialog.Title
                  as="h3"
                  className="text-2xl font-semibold leading-6 text-black mb-3"
                >
                  {reservable ? (
                    <p> Editar Reservable</p>
                  ) : (
                    <p>Crear Reservable</p>
                  )}
                </Dialog.Title>

                <div className="grid grid-cols-2 place-items-center gap-3">
                  <div className="flex flex-col items-center gap-2 w-full">
                    <label className="text-lg " htmlFor="area">
                      Area
                    </label>
                    <select
                      
                      value={space}
                      onChange={(e) => {
                        setSpace(e.target.value);
                      }}
                      className="ring-1 ring-gray-500 p-1 rounded-md text-lg w-5/6 bg-gray-100 shadow-md"
                    >
                      <option  value={""}>
                        --- Seleccionar ---
                      </option>
                      
                      {spaces?.map((space) => (
                        <option key={space.id} value={space.id}>
                          {space.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="flex flex-col items-center gap-2 w-full">
                    <label className="text-lg " htmlFor="fecha">
                      Fecha Inicio
                    </label>
                    <input
                      className="ring-1 ring-gray-500 p-1 rounded-md text-lg w-5/6 bg-gray-100 shadow-md"
                      type="datetime-local"
                      value={init_date}
                      onChange={(e) => setInit_date(e.target.value)}
                     

                    />
                  </div>
                  <div className="flex flex-col items-center gap-2 w-full">
                    <label className="text-lg " htmlFor="fecha">
                      Fecha Fin{" "}
                    </label>
                    <input
                      className="ring-1 ring-gray-500 p-1 rounded-md text-lg w-5/6 bg-gray-100 shadow-md"
                      type="datetime-local"
                      value={end_date}
                      onChange={(e) => setEnd_date(e.target.value)}
                     

                    />
                  </div>
                  <div className="flex flex-col items-center gap-2 w-full">
                    <label className="text-lg" htmlFor="colores">
                      Colores{" "}
                    </label>

                    <select
                      value={color}
                      onChange={(e) => setColor(e.target.value)}
                      id="colores"
                      className="ring-1 ring-gray-500 p-1 rounded-md text-lg w-5/6 bg-gray-100 shadow-md"
                    >
                      <option value={""}>--- Seleccionar ---</option>
                      {Object.keys(tailwindColors).map((color) => (

                      <option key={generarIdUnico()} className="capitalize" value={color}>{color}</option>
                      ))}
                      
                    </select>
                  </div>
                  <div className="flex flex-col items-center gap-2 w-full">
                    <label className="text-lg" htmlFor="profesores">
                      Profesores{" "}
                    </label>

                    <select
                      value={admin}
                      onChange={(e) => {
                        setAdmin(e.target.value);
                      }}
                      className="ring-1 ring-gray-500 p-1 rounded-md text-lg w-5/6 bg-gray-100 shadow-md"
                    >
                      <option className="" value={""}>
                        --- Seleccionar ---
                      </option>
                      {admins?.map((admin) => (
                        <option key={admin.id} value={admin.id}>
                          {admin.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="flex flex-col items-center gap-2 w-full">
                    <label className="text-lg" htmlFor="colores">
                      Aforo presencial:{" "}
                    </label>

                    <input
                      className=" w-5/6 px-1 py-1 rounded-md bg-gray-100 ring-1 ring-gray-500 text-lg"
                      type="number"
                      value={quota}
                      onChange={(e) => setQuota(Number(e.target.value))}
                      placeholder="Numero"
                    />
                  </div>
                  <div className="flex flex-col items-center gap-2 w-full">
                    <label className="text-lg" htmlFor="colores">
                      Aforo en linea:{" "}
                    </label>

                    <input
                      className=" w-5/6 px-1 py-1 rounded-md bg-gray-100 ring-1 ring-gray-500 text-lg"
                      type="number"
                      value={onlineQuota}
                      onChange={(e) => setOnlineQuota(Number(e.target.value))}
                      placeholder="Numero"
                    />
                  </div>
                  
                  {!reservable && (

                    <div className="flex flex-col items-center gap-2 w-full">
                      <label className="text-lg " htmlFor="repeticion">
                        ¿Se Repite?{" "}
                      </label>

                      <Switch
                        checked={repeats}
                        onChange={(e) => {
                          setRepeats(e);
                        }}
                        className={`switch ${
                          repeats ? "bg-blue-600" : "bg-gray-100"
                        } relative inline-flex h-6 lg:h-8 w-11 lg:w-16 items-center rounded-full`}
                      >
                        <span className="sr-only">Se repite</span>
                        <span
                          className={`${
                            repeats
                              ? "translate-x-6 lg:translate-x-10"
                              : "translate-x-1"
                          } inline-block h-4 lg:h-5 w-4 lg:w-5 transform rounded-full bg-white transition`}
                        />
                      </Switch>
                    </div>
                  )}
                 
                 <div className="flex flex-col items-center gap-2 w-full">
                      <label className="text-lg " htmlFor="repeticion">
                        ¿Es Booking?{" "}
                      </label>

                      <Switch
                        checked={booking}
                        onChange={(e) => {
                          setBooking(e);
                        }}
                        className={`switch ${
                          booking ? "bg-blue-600" : "bg-gray-100"
                        } relative inline-flex h-6 lg:h-8 w-11 lg:w-16 items-center rounded-full`}
                      >
                        <span className="sr-only">Es Booking</span>
                        <span
                          className={`${
                            booking
                              ? "translate-x-6 lg:translate-x-10"
                              : "translate-x-1"
                          } inline-block h-4 lg:h-5 w-4 lg:w-5 transform rounded-full bg-white transition`}
                        />
                      </Switch>
                    </div>
                  

                  <div
                    className={`col-span-2 transition-opacity transition-height duration-300 ease-in-out ${
                      repeats
                        ? "h-auto opacity-100 visible"
                        : "h-0 opacity-0 invisible"
                    }`}
                  >
                    <p className="text-lg mt-3 mb-1">Repeticion</p>
                    <div className="flex items-center gap-3 ">
                      {days.map((day, index) => (
                        <button
                          key={index}
                          type="button"
                          onClick={() => cambiarDay(index)}
                          className={`w-8 h-8 rounded-full font-bold ${
                            selectedDays.includes(index)
                              ? "bg-blue-500 text-gray-50"
                              : "ring-blue-500 ring-2 text-blue-500"
                          }`}
                        >
                          {day}
                        </button>
                      ))}
                    </div>
                  </div>

                  <div className="col-span-2 grid place-items-center">
                    {repeats && (
                      <div className="flex flex-col items-center gap-2 w-full">
                        <label className="text-lg " htmlFor="fecha">
                          Fin de Repeticion
                        </label>
                        <input
                          className="ring-1 ring-gray-500 p-1 rounded-md text-lg w-5/6 bg-gray-100 shadow-md"
                          type="date"
                          value={repetiton_date}
                          onChange={(e) => setRepetition_date(e.target.value)}
                        />
                      </div>
                    )}

                    {!reservable && (
                      <div className="flex flex-col items-center gap-2 w-full">
                        <button
                          type="submit"
                          className="bg-blue-500 text-gray-50 text-xl font-bold px-5 py-3 rounded-md mt-6"
                        >
                          Crear
                        </button>
                      </div>
                    )}
                  </div>

                  {reservable && (
                    <div className="col-span-2 flex items-center justify-between w-full px-4">
                    
                    <div className="flex flex-col items-center gap-2 w-full">
                      <button
                        
                        type="submit"
                        className="bg-blue-500 text-gray-50 text-xl font-bold px-5 py-3 rounded-md mt-6"
                      >
                        Editar
                      </button>
                    </div>
                    <div className="flex flex-col items-center gap-2 w-full">
                      <Link
                        
                        href={`/reservable/${id}`}
                        target="_blank"
                        className="bg-blue-100 text-blue-500 text-xl font-bold px-5 py-3 rounded-md mt-6"
                      >
                        Ver Reservas
                      </Link>
                    </div>
                    <div className="flex flex-col items-center gap-2 w-full">
                      <button
                        onClick={() => {
                          handleDelete();
                        }}
                        type="button"
                        className="ring-1 ring-red-500  text-red-500 text-xl font-bold px-5 py-3 rounded-md mt-6"
                      >
                        Borrar
                      </button>
                    </div>
                    </div>
                  )}

                  
                </div>
              </form>
            </div>
          </Transition.Child>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
