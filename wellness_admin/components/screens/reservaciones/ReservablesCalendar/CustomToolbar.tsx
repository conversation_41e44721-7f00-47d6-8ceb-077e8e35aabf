"use client";

import moment from "moment";
import React from "react";

import { ToolbarProps } from "react-big-calendar";

import "react-big-calendar/lib/css/react-big-calendar.css";

import "moment/locale/es";
import { useDispatch } from "react-redux";
import { toggleModalReservable } from "@/redux/slices/modalSlice";
import { setReservable } from "@/redux/slices/calendarSlice";

moment.locale("es");

export default function CustomToolbar({
  onNavigate,
  onView,
  label,
}: ToolbarProps) {

  const dispatch = useDispatch()

  return (
    <div className="flex justify-between items-center  py-4">
      <div className="flex items-center justify-around gap-3">
        <button
          type="button"
          className="bg-blue-500 text-gray-50 px-2 py-1 rounded-md flex items-center gap-2"
          onClick={()=>{
            dispatch(setReservable(null))
            dispatch(toggleModalReservable())
          }}
        >
          <i className="fa-solid fa-plus"></i>
          <p className="">Crear</p>
        </button>
        <button
          className="bg-gray-300 px-2 py-1 rounded-md "
          onClick={() => onView("week")}
        >
          Semana
        </button>
        <button
          className="bg-gray-300 px-2 py-1 rounded-md"
          onClick={() => onView("day")}
        >
          Día
        </button>
      </div>
      <span className="text-xl capitalize">{label}</span>
      <div>
        <button
          className="bg-gray-300 px-2 py-1 mr-2 rounded-md"
          onClick={() => onNavigate("PREV")}
        >
          Anterior
        </button>
        <button
          className="ring-blue-500 ring-1 text-blue-500 font-bold px-2 py-1 rounded-md"
          onClick={() => onNavigate("TODAY")}
        >
          Hoy
        </button>
        <button
          className="bg-gray-300 px-2 py-1 ml-2 rounded-md"
          onClick={() => onNavigate("NEXT")}
        >
          Siguiente
        </button>
      </div>
    </div>
  );
}
