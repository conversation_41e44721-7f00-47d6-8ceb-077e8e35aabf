"use client";
import { ReservableInt } from "@/types/ModelTypes";
import moment from "moment";
import React from "react";

import {
  Calendar,
  momentLocalizer,
} from "react-big-calendar";

import "react-big-calendar/lib/css/react-big-calendar.css";
import "../../../styles/calendarStyles.css";

import "moment/locale/es";
import CustomHeader from "./ReservablesCalendar/CustomHeader";
import CustomToolbar from "./ReservablesCalendar/CustomToolbar";

import { useDispatch } from "react-redux";

import { toggleModalReservable } from "@/redux/slices/modalSlice";

import { tailwindColors } from "@/utils/tailwindColors";
import { setReservable } from "@/redux/slices/calendarSlice";


moment.locale("es");

// Funcion que convierte correctamente en eventos los reservables
const reservablesToEvents = (reservables: ReservableInt[]) => {
  return reservables.map((reservable) => ({
    title: `${reservable.space.name} - ${reservable.coach}`,
    start: new Date(reservable.init_date),
    end: new Date(reservable.end_date),
    allDay: false,
    resource: reservable,
    color: reservable.color,
    coach: reservable.spaceId
  }));
};

// Funcion que crea el localizador que es necesario para el calendario
const localizer = momentLocalizer(moment);

// Personalización al español
const messages = {
  today: "Hoy",
  back: "Atrás",
  next: "Siguiente",
  month: "Mes",
  week: "Semana",
  day: "Día",
  agenda: "Agenda",
  date: "Fecha",
  time: "Hora",
  event: "Evento",
};




interface Props {
  reservables: ReservableInt[];
}

export default function ReservablesCalendar({ reservables }: Props) {

  const dispatch = useDispatch();

  const events = reservablesToEvents(reservables);

  const handleSelectedEvent = (event: { title: string; start: Date; end: Date; allDay: boolean; resource: ReservableInt; color: string; }) => {

    dispatch(setReservable(event.resource));
    
    dispatch(toggleModalReservable());     
  }




  return (
    <div className="h-[120vh] mx-4  text-ba">
      <Calendar
        localizer={localizer}
        events={events}
        
        startAccessor={"start"}
        endAccessor={"end"}
        style={{ height: "100%" }}
        defaultView="week"
        views={["week", "day"]}
        messages={messages}
        onSelectEvent={(e) => handleSelectedEvent(e)}

        min={moment("07:00:00", "HH:mm:ss").toDate()} // 7 de la mañana
        max={moment("22:00:00", "HH:mm:ss").toDate()} // 10 de la noche
        components={{
          header: CustomHeader, // Header de semana personalizado
          toolbar: CustomToolbar,

        }}
        eventPropGetter={
          (event) => ({

            style: {
              backgroundColor:tailwindColors?.[event.color]?.[500]
            }

          })
        }
      />
        
    </div>



  );
}
