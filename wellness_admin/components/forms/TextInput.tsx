import React, { Change<PERSON>ventH<PERSON><PERSON>, HTMLInputTypeAttribute } from "react";

interface Props {
  value: string;
  onChange: ChangeEventHandler<HTMLInputElement> | undefined;
  placeholder?: string;
  label: string;
  type: HTMLInputTypeAttribute;
  className?: string;
  showLabel?: boolean;
}

export default function TextInput({
  value,
  onChange,
  placeholder,
  label,
  type,
  className,
  showLabel = true,
}: Props) {
  return (
    <div className={`flex flex-col items-start w-full ${className}`}>
      {showLabel && <label className="">{label}</label>}
      <input
        className=" text-sm w-full px-1 py-1 rounded-md bg-gray-50 border-gray-300 placeholder-gray-500"
        onChange={onChange}
        type={type}
        value={value}
        {...(placeholder && { placeholder })}
      />
    </div>
  );
}
