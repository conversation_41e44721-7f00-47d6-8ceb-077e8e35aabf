// ImageUploader.tsx
import useImageDropzone from "@/hooks/useImageDropzone";
import React, {FC } from "react";


interface PreviewImage {
  file: File;
  preview: string;
}

interface ImageUploaderProps {
  onUpload: (dataUrl: string, file: File) => void;
  previewImage: PreviewImage | null;
  setPreviewImage: (previewImage: PreviewImage | null) => void;
}

const ImageUploader: FC<ImageUploaderProps> = ({ onUpload,previewImage, setPreviewImage }) => {


  const {getRootProps,getInputProps,isDragActive} = useImageDropzone({onUpload,previewImage, setPreviewImage})

  return (
    <div className="w-full">
      <div
        className={`border-2 border-dashed ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-slate-400'} p-6 rounded-lg transition-colors duration-200 flex flex-col items-center justify-center cursor-pointer hover:bg-slate-50`}
        {...getRootProps()}
      >
        <input {...getInputProps()} />

        {/* Icon */}
        <div className={`mb-3 p-3 rounded-full ${isDragActive ? 'bg-blue-100 text-blue-600' : 'bg-slate-100 text-slate-500'}`}>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </div>

        {isDragActive ? (
          <p className="text-blue-600 font-medium text-center">Suelta la imagen aquí...</p>
        ) : (
          <>
            <p className="font-medium text-center mb-1">
              Arrastra y suelta una imagen aquí
            </p>
            <p className="text-sm text-gray-500 text-center">
              o haz clic para seleccionar un archivo
            </p>
            <p className="text-xs text-gray-400 mt-2 text-center">
              Formatos aceptados: JPG, PNG, GIF (máx. 5MB)
            </p>
          </>
        )}
      </div>

      {previewImage && (
        <div className="mt-6 bg-white p-4 border rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <p className="font-medium text-gray-700">Vista previa</p>
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                setPreviewImage(null);
              }}
              className="text-red-500 hover:text-red-700 text-sm"
            >
              Eliminar
            </button>
          </div>

          <div className="bg-slate-50 p-2 rounded-lg flex justify-center">
            <img
              className="h-40 object-contain my-2 rounded-lg"
              src={previewImage.preview}
              alt="Vista previa"
            />
          </div>

          <p className="text-xs text-gray-500 mt-2 text-center">
            {previewImage.file.name} ({Math.round(previewImage.file.size / 1024)} KB)
          </p>
        </div>
      )}
    </div>
  );
};

export default ImageUploader;
