// WebSocket service for camera monitoring
import { getToken } from "@/config/axiosConfig";

// Types for WebSocket messages
export interface ClientInfo {
  type: "client_info";
  client_id: string;
  client_type: string;
  token?: string;
}

export interface FrameData {
  type: "frame";
  client_id: string;
  timestamp: number;
  frame: string; // base64 encoded image
  processed?: boolean; // Indica si el frame ha sido procesado por el cliente de procesamiento
}

export interface StatusMessage {
  type: "status";
  message: string;
  connected?: boolean;
}

export interface CounterData {
  type: "counter_data";
  total_exits: number;
  current_count: number;
  timestamp: number;
}

export interface ConfigUpdate {
  type: "config_update";
  config: {
    camera: {
      frame_rate: number;
      resize_width: number;
      resize_height: number;
      frame_skip: number;
    };
    processor: {
      confidence: number;
      iou: number;
      frame_rate: number;
      line_position_percent: number;
      tracker: {
        track_thresh: number;
        track_buffer: number;
        match_thresh: number;
        aspect_ratio_thresh: number;
        min_box_area: number;
      };
    };
  };
}

export interface ConnectionStatus {
  server: boolean;
  camera: boolean;
}

export interface PerformanceMetrics {
  camera: {
    fps: number;
    bandwidth: number;
    frame_size: { width: number; height: number };
    avg_capture_time: number;
    avg_send_time: number;
    last_update: number;
  };
  processor: {
    fps: number;
    max_fps_capacity: number;
    avg_processing_time: number;
    last_update: number;
  };
}

export interface CameraWebSocketCallbacks {
  onConnect?: () => void;
  onDisconnect?: () => void;
  onFrame?: (frame: string, processed?: boolean) => void;
  onStatus?: (status: ConnectionStatus) => void;
  onCounterData?: (data: { totalExits: number; currentCount: number; lastUpdate: Date }) => void;
  onConfigUpdate?: (config: ConfigUpdate["config"]) => void;
  onQueueUpdate?: (queueSize: number) => void;
  onPerformanceUpdate?: (metrics: PerformanceMetrics) => void;
  onError?: (error: string) => void;
}

export class CameraWebSocketService {
  private ws: WebSocket | null = null;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private clientId: string;
  private callbacks: CameraWebSocketCallbacks;
  private connectionStatus: ConnectionStatus = {
    server: false,
    camera: false,
  };
  private frameQueue: string[] = [];
  private isProcessingFrame: boolean = false;
  private maxQueueSize: number = 10; // Máximo número de frames en cola

  constructor(clientId: string, callbacks: CameraWebSocketCallbacks = {}) {
    this.clientId = clientId;
    this.callbacks = callbacks;
  }

  public connect(): void {
    try {
      // Get auth token
      const token = getToken();
      if (!token) {
        this.handleError("No se ha iniciado sesión");
        return;
      }

      // Connect to WebSocket server
      // Determine if we're in development or production
      const isDevelopment = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost';

      // Use appropriate WebSocket URL based on environment
      let wsUrl = '';

      // Check if there's a URL override in localStorage (for testing)
      const urlOverride = typeof window !== 'undefined' ? localStorage.getItem('cameraWsUrlOverride') : null;

      if (urlOverride) {
        wsUrl = urlOverride;
        console.log("Using WebSocket URL override from localStorage:", wsUrl);
      } else if (isDevelopment) {
        wsUrl = "ws://localhost:4000/ws/contador-salidas";
      } else {
        // Use the backend URL from environment variable
        const backendUrl = process.env.BACKEND_URL || "https://wellness-94dd0b1a4748.herokuapp.com";
        // Convert http/https to ws/wss
        wsUrl = backendUrl.replace('http', 'ws') + "/ws/contador-salidas";
      }

      console.log("Connecting to WebSocket URL:", wsUrl);
      this.ws = new WebSocket(wsUrl);

      // Set up event handlers
      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onerror = this.handleError.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
    } catch (err) {
      this.handleError(`Error connecting to WebSocket: ${err}`);
    }
  }

  public disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
  }

  public updateConfig(config: Partial<ConfigUpdate["config"]>): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.error("Cannot update config: WebSocket not connected");
      return;
    }

    // Send configuration update to backend
    // Determine if we're in development or production
    const isDevelopment = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost';

    // Use appropriate API URL based on environment
    const baseUrl = isDevelopment
      ? 'http://localhost:4000'
      : (process.env.BACKEND_URL || 'https://wellness-94dd0b1a4748.herokuapp.com');

    console.log(`Sending config update to: ${baseUrl}/api/contador-salidas/config`);

    fetch(`${baseUrl}/api/contador-salidas/config`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`
      },
      body: JSON.stringify(config)
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      console.log("Configuration updated successfully:", data);
      if (this.callbacks.onConfigUpdate && data.config) {
        this.callbacks.onConfigUpdate(data.config);
      }
    })
    .catch(error => {
      console.error("Error updating configuration:", error);
      if (this.callbacks.onError) {
        this.callbacks.onError(`Error al actualizar la configuración: ${error.message}`);
      }
    });
  }

  private handleOpen(): void {
    console.log("WebSocket connected");
    this.connectionStatus.server = true;
    this.updateStatus();

    if (this.callbacks.onConnect) {
      this.callbacks.onConnect();
    }

    // Send authentication message
    const token = getToken();
    if (this.ws && token) {
      console.log("Sending authentication message with token");
      this.ws.send(JSON.stringify({
        type: "client_info",
        client_id: this.clientId,
        client_type: "admin",
        token
      }));
    } else {
      console.error("Cannot authenticate: No token available");
    }
  }

  private handleMessage(event: MessageEvent): void {
    try {
      console.log("Received WebSocket message:", event.data.substring(0, 100) + "...");
      const data = JSON.parse(event.data);

      // Handle different message types
      switch (data.type) {
        case "frame":
          console.log("Received frame data, length:", data.frame ? data.frame.length : 0,
                     "processed:", data.processed ? "yes" : "no");
          if (this.callbacks.onFrame) {
            // Ensure we're getting a valid base64 string
            if (data.frame && typeof data.frame === 'string' && data.frame.length > 0) {
              // Agregar el frame a la cola
              this.frameQueue.push(data.frame);

              // Limitar el tamaño de la cola para evitar uso excesivo de memoria
              if (this.frameQueue.length > this.maxQueueSize) {
                // Si la cola está llena, eliminar frames antiguos
                this.frameQueue = this.frameQueue.slice(-this.maxQueueSize);
                console.log(`Frame queue exceeded max size, trimmed to ${this.maxQueueSize} frames`);
              }

              // Notificar sobre el tamaño de la cola
              if (this.callbacks.onQueueUpdate) {
                this.callbacks.onQueueUpdate(this.frameQueue.length);
              }

              // Procesar el siguiente frame si no estamos procesando uno actualmente
              this.processNextFrame(data.processed);
            } else {
              console.error("Received invalid frame data:", data.frame);
            }
          }
          break;
        case "status":
          console.log("Received status message:", data.message, data.connected);
          if (data.message.includes("camera")) {
            this.connectionStatus.camera = data.connected || false;
            this.updateStatus();
          }
          break;
        case "counter_data":
          console.log("Received counter data:", data.total_exits, data.current_count);
          if (this.callbacks.onCounterData) {
            this.callbacks.onCounterData({
              totalExits: data.total_exits,
              currentCount: data.current_count,
              lastUpdate: new Date(),
            });
          }
          break;
        case "config_update":
          console.log("Received configuration update:", data.config);
          if (this.callbacks.onConfigUpdate && data.config) {
            this.callbacks.onConfigUpdate(data.config);
          }
          break;
        case "performance_update":
          console.log("Received performance metrics:", data.metrics);
          if (this.callbacks.onPerformanceUpdate && data.metrics) {
            this.callbacks.onPerformanceUpdate(data.metrics);
          }
          break;
        default:
          console.log("Unknown message type:", data.type);
      }
    } catch (err) {
      console.error("Error parsing WebSocket message:", err);
    }
  }

  private handleError(error: any): void {
    console.error("WebSocket error:", error);

    if (this.callbacks.onError) {
      this.callbacks.onError(typeof error === 'string' ? error : "Error de conexión con el servidor");
    }
  }

  private handleClose(): void {
    console.log("WebSocket disconnected");
    this.connectionStatus = { server: false, camera: false };
    this.updateStatus();

    if (this.callbacks.onDisconnect) {
      this.callbacks.onDisconnect();
    }

    // Schedule reconnection
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }

    this.reconnectTimeout = setTimeout(() => {
      this.connect();
    }, 5000);
  }

  private updateStatus(): void {
    if (this.callbacks.onStatus) {
      this.callbacks.onStatus({ ...this.connectionStatus });
    }
  }

  /**
   * Procesa el siguiente frame en la cola
   */
  private processNextFrame(processed: boolean = false): void {
    // Si ya estamos procesando un frame o la cola está vacía, no hacer nada
    if (this.isProcessingFrame || this.frameQueue.length === 0) {
      return;
    }

    this.isProcessingFrame = true;

    // Obtener el siguiente frame de la cola
    const frame = this.frameQueue.shift();

    // Procesar el frame
    if (frame && this.callbacks.onFrame) {
      this.callbacks.onFrame(frame, processed);
    }

    // Marcar como procesado
    this.isProcessingFrame = false;

    // Notificar sobre el tamaño de la cola
    if (this.callbacks.onQueueUpdate) {
      this.callbacks.onQueueUpdate(this.frameQueue.length);
    }

    // Si hay más frames en la cola, procesar el siguiente
    if (this.frameQueue.length > 0) {
      // Usar setTimeout para evitar bloquear el hilo principal
      setTimeout(() => this.processNextFrame(processed), 0);
    }
  }

  /**
   * Sincroniza la transmisión descartando frames antiguos
   */
  public syncStream(): void {
    // Vaciar la cola excepto el último frame (el más reciente)
    if (this.frameQueue.length > 1) {
      const lastFrame = this.frameQueue[this.frameQueue.length - 1];
      this.frameQueue = lastFrame ? [lastFrame] : [];
      console.log("Stream synchronized, discarded old frames");
    }

    // Resetear el estado de procesamiento
    this.isProcessingFrame = false;

    // Notificar sobre el tamaño de la cola
    if (this.callbacks.onQueueUpdate) {
      this.callbacks.onQueueUpdate(this.frameQueue.length);
    }

    // Procesar el frame más reciente si existe
    if (this.frameQueue.length > 0) {
      this.processNextFrame(true);
    }
  }
}

export default CameraWebSocketService;
