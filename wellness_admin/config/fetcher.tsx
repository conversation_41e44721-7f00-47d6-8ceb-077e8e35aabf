import { axiosConfig } from "./axiosConfig";
import clienteAxios from "./clienteAxios";



export const fetcher = (url: string) => clienteAxios(url,axiosConfig() ?? undefined).then((datos) => datos.data)

export const microServiceFetcher = (url: string) => clienteAxios(url,{
    headers:{
        apikey: "3F7F88C1454CA53549A1CC04AEF4F0B06543EF11E43510FD3CEDBA01A5F8482F"
    }
}).then((datos) => datos.data)