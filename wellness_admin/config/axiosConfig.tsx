import jsCookie from "js-cookie";

export function axiosConfig(json:boolean = true){
    
    const token = jsCookie.get("token");
    if(!token) return null
    const config = {
        headers: {
        "Content-Type": json ? "application/json" : "multipart/form-data",
        Authorization: `Bearer ${token}`,
        "ngrok-skip-browser-warning":true
        },
    };
    return config
}

export function getToken(){
    return jsCookie.get("token")
}