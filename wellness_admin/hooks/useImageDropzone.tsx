import {useState,useEffect,useCallback} from 'react'
import { useDropzone, DropzoneOptions } from "react-dropzone";


interface PreviewImage {
    file: File;
    preview: string;
  }
interface ImageDropzoneProps {
    onUpload: (dataUrl: string, file: File) => void;
    previewImage: PreviewImage | null;
    setPreviewImage: (previewImage: PreviewImage | null) => void;
  }
export default function useImageDropzone({ onUpload,previewImage, setPreviewImage }: ImageDropzoneProps) {
    const onDrop = useCallback(
        (acceptedFiles: File[]) => {
          if (acceptedFiles.length === 0) return;
    
          const file = acceptedFiles[0];
          const reader = new FileReader();
    
          reader.onload = () => {
            const binaryStr = reader.result as string;
            onUpload(binaryStr, file);
          };
    
          reader.readAsDataURL(file);
    
          setPreviewImage({
            file,
            preview: URL.createObjectURL(file),
          });
        },
        [onUpload]
      );
    
      const dropzoneOptions: DropzoneOptions = {
        onDrop,
        maxFiles: 1, // Asegúrate de que solo se acepte un archivo a la vez
      };
    
      const { getRootProps, getInputProps, isDragActive } =
        useDropzone(dropzoneOptions);
    
      useEffect(() => {
        return () => {
          if (previewImage) {
            URL.revokeObjectURL(previewImage.preview);
          }
        };
      }, [previewImage]);

      return {
        getRootProps,
        getInputProps,
        isDragActive
        
      }
    
}
