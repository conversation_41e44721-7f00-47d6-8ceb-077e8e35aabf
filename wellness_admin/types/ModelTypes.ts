export interface AdminInt {
  id: number;
  name: string;
  email: string;
  password: string;
  role: AdminRoles;
}

export interface UserInt {
  id: number;
  name: string;
  password: string | null;
  registration: string;
  gender: string;
  program_key: string;
  academic_exercise: string | null;
  program: string | null;
  reservations?: ReservationInt[];
  updatedAt: Date;
}

export interface ReservationInt {
  id: number;
  reservableId: number;
  reservable?: ReservableInt;
  userId: number;
  user?: UserInt;
  reservation_date: Date;
  status: ReservationStatus;
  type: ReservationTypes;
}

export enum ReservationStatus {
  APPROVED = "APPROVED",
  CANCELED = "CANCELED",
  PENDING = "PENDING"
}

export interface MaterialInt {
  id: number;
  name: string;
  image?: string;
  image_id?: string;
  quantity: number;
  minMatriculas: number;
  leadTimeDays: number;
  rules: string;
  replacementCost: number;
  deleted: boolean;
  createdAt: Date;
}

export interface LoanInt {
  id: number;
  materialId: number;
  responsibleId: string;
  studentIds: string[];
  quantity: number;
  pickupDate: Date;
  returnDate: Date;
  actualReturnDate?: Date;
  pickupTime: string;
  returnTime: string;
  status: LoanStatus;
  createdAt: Date;
  penaltyApplied: boolean;
  penaltyNotes?: string;
  material?: MaterialInt;

  // Additional fields from material relation
  material_name?: string;
  material_image?: string;
}

export enum LoanStatus {
  PENDING = "PENDING",
  AWAITING_PICKUP = "AWAITING_PICKUP",
  ON_LOAN = "ON_LOAN",
  LATE = "LATE",
  LOST = "LOST",
  RETURNED = "RETURNED"
}


export enum ReservationTypes {
  ONLINE = "ONLINE",
  PRESENTIAL = "PRESENTIAL"
}

export interface SpaceInt {
  id: number;
  name: string;
  adminId: number;
  quota: number;
  onlineQuota: number;
  open_time: string;
  close_time: string;
  open: boolean;
  location: string;
  materials: string[];
  materials_required: boolean
  image?: string;
}

export interface ReservableInt {
  id: number;
  adminId: number;
  spaceId: number;
  space: SpaceInt
  init_date: string;
  end_date: string;
  quota: number;
  onlineQuota: number;
  color: string;
  coach: string
  booking: boolean
}


export interface AnnounceInt {
  id: number;
  title: string;
  init_date: string;
  end_date: string;
  event_date: string;
}

export interface eventosInt{
  actual: number;
  coach: string;
  end_date: Date;
  foto: string;
  init_date: Date;
  nombre: string;
  quota: number;
}

export interface CarouselImageInt {
  id: number;
  url: string;
  image_id: string;
  order: number;
  createdAt: Date;
}