{"name": "wellness_usuario", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 2000", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^1.7.14", "@next/font": "13.1.6", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-slot": "^1.1.0", "@reduxjs/toolkit": "^1.9.5", "@tailwindcss/line-clamp": "^0.4.2", "axios": "^1.3.4", "bson-objectid": "^2.0.4", "class-variance-authority": "^0.7.0", "cookie": "^0.5.0", "date-fns": "^2.30.0", "js-cookie": "^3.0.1", "lucide-react": "^0.441.0", "luxon": "^3.5.0", "moment": "^2.29.4", "next": "^13.5.4", "react": "18.2.0", "react-big-calendar": "^1.8.5", "react-circular-progressbar": "^2.1.0", "react-day-picker": "^8.9.1", "react-dom": "18.2.0", "react-dropzone": "^14.2.3", "react-icons": "^4.8.0", "react-qr-reader": "^3.0.0-beta-1", "react-redux": "^8.1.2", "react-responsive-carousel": "^3.2.23", "react-select": "^5.7.3", "react-slick": "^0.29.0", "react-swipeable-list": "^1.7.1", "recharts": "^2.8.0", "slick-carousel": "^1.8.1", "sweetalert2": "^11.7.3", "swr": "^2.1.5", "tailwind-merge": "^2.2.2"}, "devDependencies": {"@types/cookie": "^0.5.1", "@types/js-cookie": "^3.0.3", "@types/luxon": "^3.4.2", "@types/node": "18.15.6", "@types/react": "18.2.28", "@types/react-big-calendar": "^1.8.3", "autoprefixer": "^10.4.14", "cypress": "^12.9.0", "file-loader": "^6.2.0", "postcss": "^8.4.23", "tailwindcss": "^3.3.1", "typescript": "5.2.2"}}