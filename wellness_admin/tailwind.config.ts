import type { Config } from 'tailwindcss'


const config: Config = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  safelist:[
    {
      pattern: /^bg-/,
      variants: ["hover"],
    }
  ],
  theme: {
    extend: {
      height: {
        '45vh': '45vh',
        '50vh': '50vh',
        '30vh': '30vh',
        '70vh': '70vh',
        '25vh': '25vh'
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic':
          'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
    },

    fontFamily: {
      inter: ['Inter', 'sans-serif'],
    },


  },
  plugins: [],
}
export default config
