{"name": "welness_admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^1.7.17", "@next/font": "13.1.6", "@nextui-org/react": "^2.2.9", "@reduxjs/toolkit": "^1.9.7", "@tailwindcss/line-clamp": "^0.4.2", "axios": "^1.3.4", "bson-objectid": "^2.0.4", "cookie": "^0.5.0", "framer-motion": "^10.16.5", "js-cookie": "^3.0.1", "jsonwebtoken": "^9.0.2", "luxon": "^3.5.0", "next": "^13.5.6", "qrcode.react": "^3.1.0", "react": "18.2.0", "react-circular-progressbar": "^2.1.0", "react-dom": "18.2.0", "react-icons": "^4.8.0", "react-redux": "^8.1.3", "react-responsive-carousel": "^3.2.23", "react-select": "^5.7.3", "react-slick": "^0.29.0", "react-swipeable-list": "^1.7.1", "recharts": "^2.10.1", "slick-carousel": "^1.8.1", "sweetalert2": "^11.7.3", "swr": "^2.1.5"}, "devDependencies": {"@types/cookie": "^0.5.1", "@types/js-cookie": "^3.0.3", "@types/luxon": "^3.4.2", "@types/node": "18.15.6", "@types/react": "^18.0.29", "@types/react-slick": "^0.23.12", "autoprefixer": "^10.4.14", "cypress": "^12.9.0", "postcss": "^8.4.23", "tailwindcss": "^3.3.1", "typescript": "^5.0.4"}}