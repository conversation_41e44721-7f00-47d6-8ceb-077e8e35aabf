import { PrismaClient } from "@prisma/client";
import dotenv from "dotenv";
dotenv.config();

import fs from "fs";
// archivo donde se pueden ejecutar queries a bd

async function query() {
    // Obtenemos los datos de la base de datos
    const prisma = new PrismaClient();

    // Obtenemos la información de las asistencias
    const asistencias = await prisma.asistencia.findMany();

    // Obtenemos los datos de los alumnos
    const alumnos = await prisma.alumno.findMany();


    console.log("Datos Fetcheados asistencias Cantidad", asistencias.length);
    console.log("Datos Fetcheados alumnos Cantidad", alumnos.length);

    // La guardamos en un archivo
    fs.writeFileSync("./asistencia.json", JSON.stringify(asistencias, null, 2));
    fs.writeFileSync("./alumno.json", JSON.stringify(alumnos, null, 2));

    console.log("Datos guardados en asistencia.json")
    console.log("Datos guardados en alumno.json")

    // Cerramos la conexión
    await prisma.$disconnect();
    
 
}
query();
