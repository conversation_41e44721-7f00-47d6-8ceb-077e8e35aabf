import { PrismaClient } from "@prisma/client";
import dotenv from "dotenv";
dotenv.config();

import fs from "fs";
// archivo donde se pueden ejecutar queries a bd

// Tipado de los datos de entrada

interface Alumno {
  id: number;
  nombre_completo: string;
  matricula: string;
  desc_genero: number;
  clave_programa: string;
  desc_programa: null | string;
  desc_ejercicio_academico: string | null;
}
interface Asistencia {
  id: number;
  id_alumno: number;
  fecha_y_hora: string;
  observaciones: string | null;
}
async function query() {
  console.log("Iniciando proceso de migración de datos...");

  console.log("Cargando datos de alumnos y asistencias...");
  // Primero leeremos el archivo de alumnos
  const alumnos: Alumno[] = JSON.parse(
    fs.readFileSync("./alumno.json", "utf-8")
  );

  // Luego leeremos el archivo de asistencias
  const asistencias: Asistencia[] = JSON.parse(
    fs.readFileSync("./asistencia.json", "utf-8")
  );

  console.log("Conectando a la base de datos...");
  // Conectar a la bd
  const prisma = new PrismaClient();

  try {
    // Eliminando todas las asistencias
    console.log("Eliminando asistencias existentes...");
    await prisma.attendance.deleteMany();
    console.log("Todas las asistencias existentes han sido eliminadas.");
    // Eliminamos todos los alumnos
    console.log("Eliminando alumnos existentes...");
    await prisma.user.deleteMany();
    console.log("Todos los alumnos existentes han sido eliminados.");

    // Volvemos a añadir a los alumnos
    console.log("Insertando nuevos alumnos...");
    await prisma.user.createMany({
      data: alumnos.map((alumno) => ({
        id: alumno.id,
        name: alumno.nombre_completo,
        password: null,
        gender:
          alumno.desc_genero.toString() === "1" ? "Masculino" : "Femenino",
        academic_exercise: alumno.desc_ejercicio_academico,
        program: alumno.desc_programa,
        registration: alumno.matricula.toLowerCase(),
        program_key: alumno.clave_programa,
      })),
      skipDuplicates: true,
    });

    console.log(`Se han insertado ${alumnos.length} alumnos.`);

    // Insertar las asistencias
    console.log("Preparando para la inserción de asistencias...");
    const existingAlumnoIds = new Set(alumnos.map((alumno) => alumno.id));

    // Crear un mapa para agrupar asistencias por alumno
    const asistenciasPorAlumno: Map<number,Asistencia[]> = asistencias.reduce((map, asistencia) => {
      if (!map.has(asistencia.id_alumno)) {
        map.set(asistencia.id_alumno, []);
      }
      map.get(asistencia.id_alumno).push(asistencia);
      return map;
    }, new Map());

    console.log("Insertando asistencias...");
    // Luego, insertar cada grupo de asistencias por lote
    for (const [idAlumno, asistenciasGrupo] of asistenciasPorAlumno) {
  
       

      if (!existingAlumnoIds.has(idAlumno)) {
        console.log(`Saltando asistencias para el alumno ID ${idAlumno}: alumno no encontrado.`);
        continue; // Saltar si el alumno no existe
      }

      await prisma.attendance.createMany({
        data: asistenciasGrupo.map((asistencia) => ({
          userId: idAlumno,
          date: new Date(asistencia.fecha_y_hora)
                    
        })),
        skipDuplicates: true,
      });
      console.log(`Asistencias insertadas para el alumno ID ${idAlumno}.`);
    }

    // Cerrar la conexión
    await prisma.$disconnect();

    console.log("Datos insertados correctamente");
  } catch (error: any) {
    console.log(error);
  }
}
query();
