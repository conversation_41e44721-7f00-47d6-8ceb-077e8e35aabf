import { PrismaClient } from "@prisma/client";
import dotenv from "dotenv";
dotenv.config();
import readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});
// archivo donde se pueden ejecutar queries a bd

// Tipado de los datos de entrada

interface Alumno {
  id: number;
  nombre_completo: string;
  matricula: string;
  desc_genero: number;
  clave_programa: string;
  desc_programa: null | string;
  desc_ejercicio_academico: string | null;
}
interface Asistencia {
  id: number;
  id_alumno: number;
  fecha_y_hora: string;
  observaciones: string;
}
async function query() {
  const prisma = new PrismaClient();
  try {

    // Preguntar si esta seguro que quiere eliminar todo
    rl.question('¿Estás seguro de que quieres eliminar todo? (s/n) ', async (answer) => {
      if (answer.toLowerCase() === 's') {
        // Aquí va el código para eliminar todos los usuarios sin un password asignado
        await prisma.user.deleteMany({
          where: {
            password: null
          }
        })

        console.log("Eliminado correctamente")
      } else {
        console.log('Operación cancelada.');
      }
      rl.close();
    });

    // Cerrar la conexión
    await prisma.$disconnect();

  } catch (error: any) {
    console.log(error);
  }
}
query();
