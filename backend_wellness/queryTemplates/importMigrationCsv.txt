import { PrismaClient } from "@prisma/client";
import dotenv from "dotenv";
dotenv.config();

import fs from "fs";
// archivo donde se pueden ejecutar queries a bd

// Tipado de los datos de entrada

interface Alumno {
  id: number;
  nombre_completo: string;
  matricula: string;
  desc_genero: number;
  clave_programa: string;
  desc_programa: null | string;
  desc_ejercicio_academico: string | null;
}
interface Asistencia {
  id: number;
  id_alumno: number;
  fecha_y_hora: string;
  observaciones: string;
}
async function query() {
  // Primero leeremos el archivo de alumnos
  const alumnos: Alumno[] = JSON.parse(
    fs.readFileSync("./alumno.json", "utf-8")
  );

  // Luego leeremos el archivo de asistencias
  const asistencias: Asistencia[] = JSON.parse(
    fs.readFileSync("./asistencia.json", "utf-8")
  );

  // Conectar a la bd
  const prisma = new PrismaClient();

  // Variables de conteo
  let alumnosInsertados = 0;
  let asistenciasInsertadas = 0;

  // Total de alumnos
  const totalAlumnos = alumnos.length;

  // Total de asistencias
  const totalAsistencias = asistencias.length;

  try {
    // Insertar los alumnos

    for (const alumno of alumnos) {
      const data = alumno;

      // Verificar si el alumno ya existe
      const alumnoExiste = await prisma.user.findUnique({
        where: {
          registration: alumno.matricula.toLowerCase(),
          id: alumno.id,
        },
      });

      // Si Agregamos al conteo
      alumnosInsertados++
      console.log("Alumnos % completdo: ", (alumnosInsertados/totalAlumnos)*100, "%")




      // Si el alumno existe saltamos la iteracion
      if (alumnoExiste) continue;

      await prisma.user.create({
        data: {
          id: alumno.id,
          name: data.nombre_completo,
          password: null,
          gender: alumno.desc_genero.toString() === "1" ? "Masculino" : "Femenino",
          academic_exercise: alumno.desc_ejercicio_academico,
          program: alumno.desc_programa,
          registration: alumno.matricula.toLowerCase(),
          program_key: alumno.clave_programa,
        },
        
      });
    }

    // Insertar las asistencias
    for (const asistencia of asistencias) {
      const { id, ...data } = asistencia;


        // Verificamos que existe el alumno al que se le colocara la asistencia
        const alumnoExiste = await prisma.user.findUnique({
            where: {
            id: asistencia.id_alumno,
            }
        })

        // Si Agregamos al conteo
        asistenciasInsertadas++
        console.log("Asistencias % completdo: ", (asistenciasInsertadas/totalAsistencias)*100, "%")

        // Si el alumno no existe saltamos la iteracion
        if (!alumnoExiste){
            console.log("Alumno no encontrado saltando iteracion")
            continue;
        };

      await prisma.attendance.create({
        data: {
          userId: asistencia.id_alumno,
          date: new Date(asistencia.fecha_y_hora),
          observation: asistencia.observaciones,
        },
      });
    }

    // Cerrar la conexión
    await prisma.$disconnect();

    console.log("Datos insertados correctamente");
  } catch (error: any) {
    console.log(error);
  }
}
query();
