import dotenv from "dotenv";
dotenv.config();
import { PrismaClient } from "@prisma/client";
import csvParser from "csv-parser";

import fs from "fs";
// archivo donde se pueden ejecutar queries a bd

// Tipado de los datos de entrada

const prisma = new PrismaClient();

console.log("Inicio");

const matriculas : string[] = []

fs.createReadStream("alumnos_quitar.csv")
  // Ejecutamos el parser de csv
  .pipe(csvParser())
  // Hacemos el preprocesamiento de los datos
  .on("data", (rawRow) => {
    // Hacemos el mapeo de los datos
    const matricula = rawRow.Matricula;
    matriculas.push(matricula.toLowerCase());

    return;
  })
  // Si hay un error
  .on("error", (_) => {
    return console.error({ msg: "Error al leer el archivo" });
  })
  .on("end", async () => {
    console.log(matriculas)


    // Sincronizamos los usuarios con la base de datos

    // Eliminamos los usuarios que esten en el arreglo
    const deletedUsers = await prisma.user.deleteMany({
      where: {
        registration: {
          in: matriculas
        },
      },
    });
   
    //Si todo funcion sin errores entonces devolvemos un mensaje
    return console.log({ msg: "Archivo procesado correctamente" });
  });
