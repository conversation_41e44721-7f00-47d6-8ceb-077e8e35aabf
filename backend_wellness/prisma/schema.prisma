generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model carouselImage {
  id        Int      @id @default(autoincrement())
  url       String
  image_id  String   @unique
  order     Int      @default(0)
  createdAt DateTime @default(now())
}

model admin {
  id          Int          @id @default(autoincrement())
  name        String
  email       String       @unique
  password    String
  role        AdminRoles   @default(ADMIN)
  lastSeen    DateTime     @default(now())
  reservables reservable[]
  spaces      space[]
}

model space {
  id                 Int            @id @default(autoincrement())
  name               String
  adminId            Int
  quota              Int
  image              String?
  location           String         @default("Wellness Center")
  materials          String[]       @default(["cosas", "mas cosas"])
  materials_required Boolean        @default(true)
  image_id           String?
  close_time         String         @default("18:00")
  open_time          String         @default("9:00")
  onlineQuota        Int            @default(0)
  closed_spaces      closed_space[]
  reservables        reservable[]
  admin              admin          @relation(fields: [adminId], references: [id])
}

model closed_space {
  id          Int      @id @default(autoincrement())
  start_date  DateTime
  end_date    DateTime
  description String
  spaceId     Int
  space       space    @relation(fields: [spaceId], references: [id], onDelete: Cascade)
}

model user {
  id                Int           @id @default(autoincrement())
  name              String
  password          String?
  academic_exercise String?
  gender            String
  program_key       String
  registration      String        @unique
  program           String?
  updatedAt         DateTime      @default(now()) @updatedAt
  session_id        String?
  lastSeen          DateTime      @default(now())
  observation       String?
  deleted           Boolean       @default(false)
  attendances       attendance[]
  reservations      reservation[]
}

model attendance {
  id          Int      @id @default(autoincrement())
  userId      Int?
  date        DateTime
  observation String?
  user        user?    @relation(fields: [userId], references: [id])
}

model reservable {
  id              Int           @id @default(autoincrement())
  spaceId         Int
  init_date       DateTime
  end_date        DateTime
  quota           Int
  color           String
  adminId         Int?          @default(1)
  reservableGroup String        @default(uuid())
  booking         Boolean       @default(false)
  onlineQuota     Int           @default(0)
  admin           admin?        @relation(fields: [adminId], references: [id])
  space           space         @relation(fields: [spaceId], references: [id], onDelete: Cascade)
  reservations    reservation[]
}

model reservation {
  id               Int               @id @default(autoincrement())
  reservableId     Int
  userId           Int
  reservation_date DateTime
  status           ReservationStatus @default(PENDING)
  type             ReservationTypes  @default(PRESENTIAL)
  reservable       reservable        @relation(fields: [reservableId], references: [id], onDelete: Cascade)
  user             user              @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model announce {
  id         Int      @id @default(autoincrement())
  title      String
  init_date  DateTime
  end_date   DateTime
  event_date DateTime
}

model personExit {
  id   Int      @id @default(autoincrement())
  date DateTime
}

model permanenceTime {
  id       Int       @id @default(autoincrement())
  schedule Schedules @default(MORNING)
  hours    Int
  minutes  Int
}

model material {
  id              Int      @id @default(autoincrement())
  name            String
  image           String
  image_id        String
  quantity        Int
  minMatriculas   Int
  leadTimeDays    Int
  rules           String
  replacementCost Float
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  deleted         Boolean  @default(false)
  loans           loan[]
}

model loan {
  id               Int        @id @default(autoincrement())
  materialId       Int
  quantity         Int
  status           LoanStatus @default(PENDING)
  createdAt        DateTime   @default(now())
  pickupDate       DateTime
  pickupTime       String
  responsibleId    String
  returnDate       DateTime
  returnTime       String
  studentIds       String[]
  actualReturnDate DateTime?
  penaltyApplied   Boolean    @default(false)
  penaltyNotes     String?
  updatedAt        DateTime   @default(now()) @updatedAt
  material         material   @relation(fields: [materialId], references: [id])
}

enum LoanStatus {
  PENDING
  AWAITING_PICKUP
  ON_LOAN
  LATE
  LOST
  RETURNED
}

enum AdminRoles {
  ADMIN
  SUPERADMIN
}

enum ReservationStatus {
  APPROVED
  CANCELED
  PENDING
}

enum ReservationTypes {
  ONLINE
  PRESENTIAL
}

enum Schedules {
  MORNING
  NIGHT
}
