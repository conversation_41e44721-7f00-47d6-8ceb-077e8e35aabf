-- Create<PERSON><PERSON>
CREATE TYPE "AdminRoles" AS ENUM ('ADMIN', 'SUPERADMIN');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "ReservationStatus" AS ENUM ('APPROVED', 'CANCELED', 'PENDING');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "ReservationTypes" AS ENUM ('ONLINE', 'PRESENTIAL');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "Schedules" AS ENUM ('MORNING', 'NIGHT');

-- CreateTable
CREATE TABLE "admin" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "role" "AdminRoles" NOT NULL DEFAULT 'ADMIN',
    "lastSeen" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "admin_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "announce" (
    "id" SERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "init_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3) NOT NULL,
    "event_date" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "announce_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "attendance" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER,
    "date" TIMESTAMP(3) NOT NULL,
    "observation" TEXT,

    CONSTRAINT "attendance_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "closed_space" (
    "id" SERIAL NOT NULL,
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3) NOT NULL,
    "description" TEXT NOT NULL,
    "spaceId" INTEGER NOT NULL,

    CONSTRAINT "closed_space_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "permanenceTime" (
    "id" SERIAL NOT NULL,
    "schedule" "Schedules" NOT NULL DEFAULT 'MORNING',
    "hours" INTEGER NOT NULL,
    "minutes" INTEGER NOT NULL,

    CONSTRAINT "permanenceTime_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "personExit" (
    "id" SERIAL NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "personExit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "reservable" (
    "id" SERIAL NOT NULL,
    "spaceId" INTEGER NOT NULL,
    "init_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3) NOT NULL,
    "quota" INTEGER NOT NULL,
    "color" TEXT NOT NULL,
    "adminId" INTEGER DEFAULT 1,
    "reservableGroup" TEXT NOT NULL,
    "booking" BOOLEAN NOT NULL DEFAULT false,
    "onlineQuota" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "reservable_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "reservation" (
    "id" SERIAL NOT NULL,
    "reservableId" INTEGER NOT NULL,
    "userId" INTEGER NOT NULL,
    "reservation_date" TIMESTAMP(3) NOT NULL,
    "status" "ReservationStatus" NOT NULL DEFAULT 'PENDING',
    "type" "ReservationTypes" NOT NULL DEFAULT 'PRESENTIAL',

    CONSTRAINT "reservation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "space" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "adminId" INTEGER NOT NULL,
    "quota" INTEGER NOT NULL,
    "image" TEXT,
    "location" TEXT NOT NULL DEFAULT 'Wellness Center',
    "materials" TEXT[] DEFAULT ARRAY['cosas', 'mas cosas']::TEXT[],
    "materials_required" BOOLEAN NOT NULL DEFAULT true,
    "image_id" TEXT,
    "close_time" TEXT NOT NULL DEFAULT '18:00',
    "open_time" TEXT NOT NULL DEFAULT '9:00',
    "onlineQuota" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "space_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "password" TEXT,
    "academic_exercise" TEXT,
    "gender" TEXT NOT NULL,
    "program_key" TEXT NOT NULL,
    "registration" TEXT NOT NULL,
    "program" TEXT,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "session_id" TEXT,
    "lastSeen" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "admin_email_key" ON "admin"("email" ASC);

-- CreateIndex
CREATE UNIQUE INDEX "user_registration_key" ON "user"("registration" ASC);

-- AddForeignKey
ALTER TABLE "attendance" ADD CONSTRAINT "attendance_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "closed_space" ADD CONSTRAINT "closed_space_spaceId_fkey" FOREIGN KEY ("spaceId") REFERENCES "space"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reservable" ADD CONSTRAINT "reservable_adminId_fkey" FOREIGN KEY ("adminId") REFERENCES "admin"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reservable" ADD CONSTRAINT "reservable_spaceId_fkey" FOREIGN KEY ("spaceId") REFERENCES "space"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reservation" ADD CONSTRAINT "reservation_reservableId_fkey" FOREIGN KEY ("reservableId") REFERENCES "reservable"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reservation" ADD CONSTRAINT "reservation_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "space" ADD CONSTRAINT "space_adminId_fkey" FOREIGN KEY ("adminId") REFERENCES "admin"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

