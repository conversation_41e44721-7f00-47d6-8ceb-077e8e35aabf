/*
  Warnings:

  - You are about to drop the `Loan` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Material` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "Loan" DROP CONSTRAINT "Loan_materialId_fkey";

-- DropTable
DROP TABLE "Loan";

-- DropTable
DROP TABLE "Material";

-- CreateTable
CREATE TABLE "material" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "image" TEXT NOT NULL,
    "image_id" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "minMatriculas" INTEGER NOT NULL,
    "leadTimeDays" INTEGER NOT NULL,
    "rules" TEXT NOT NULL,
    "replacementCost" DOUBLE PRECISION NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "material_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "loan" (
    "id" SERIAL NOT NULL,
    "materialId" INTEGER NOT NULL,
    "responsable" TEXT NOT NULL,
    "matriculas" TEXT[],
    "quantity" INTEGER NOT NULL,
    "fechaRecoleccion" TIMESTAMP(3) NOT NULL,
    "fechaEntrega" TIMESTAMP(3) NOT NULL,
    "horarioRecoleccion" TEXT NOT NULL,
    "horarioEntrega" TEXT NOT NULL,
    "status" "LoanStatus" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "loan_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "loan" ADD CONSTRAINT "loan_materialId_fkey" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
