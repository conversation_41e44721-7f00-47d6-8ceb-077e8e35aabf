-- CreateEnum
CREATE TYPE "LoanStatus" AS ENUM ('PENDING', 'CONFIRMED', 'IN_USE', 'RETURNED');

-- CreateTable
CREATE TABLE "Material" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "image" TEXT NOT NULL,
    "image_id" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "minMatriculas" INTEGER NOT NULL,
    "leadTimeDays" INTEGER NOT NULL,
    "rules" TEXT NOT NULL,
    "replacementCost" DOUBLE PRECISION NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Material_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Loan" (
    "id" SERIAL NOT NULL,
    "materialId" INTEGER NOT NULL,
    "responsable" TEXT NOT NULL,
    "matriculas" TEXT[],
    "quantity" INTEGER NOT NULL,
    "fechaRecoleccion" TIMESTAMP(3) NOT NULL,
    "fechaEntrega" TIMESTAMP(3) NOT NULL,
    "horarioRecoleccion" TEXT NOT NULL,
    "horarioEntrega" TEXT NOT NULL,
    "status" "LoanStatus" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Loan_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Loan" ADD CONSTRAINT "Loan_materialId_fkey" FOREIGN KEY ("materialId") REFERENCES "Material"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
