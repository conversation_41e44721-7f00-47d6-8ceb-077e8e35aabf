/*
  Warnings:

  - You are about to drop the column `fechaEntrega` on the `loan` table. All the data in the column will be lost.
  - You are about to drop the column `fechaRecoleccion` on the `loan` table. All the data in the column will be lost.
  - You are about to drop the column `horarioEntrega` on the `loan` table. All the data in the column will be lost.
  - You are about to drop the column `horarioRecoleccion` on the `loan` table. All the data in the column will be lost.
  - You are about to drop the column `matriculas` on the `loan` table. All the data in the column will be lost.
  - You are about to drop the column `responsable` on the `loan` table. All the data in the column will be lost.
  - Added the required column `pickupDate` to the `loan` table without a default value. This is not possible if the table is not empty.
  - Added the required column `pickupTime` to the `loan` table without a default value. This is not possible if the table is not empty.
  - Added the required column `responsibleId` to the `loan` table without a default value. This is not possible if the table is not empty.
  - Added the required column `returnDate` to the `loan` table without a default value. This is not possible if the table is not empty.
  - Added the required column `returnTime` to the `loan` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "loan" DROP COLUMN "fechaEntrega",
DROP COLUMN "fechaRecoleccion",
DROP COLUMN "horarioEntrega",
DROP COLUMN "horarioRecoleccion",
DROP COLUMN "matriculas",
DROP COLUMN "responsable",
ADD COLUMN     "pickupDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "pickupTime" TEXT NOT NULL DEFAULT '7:00–9:00',
ADD COLUMN     "responsibleId" TEXT NOT NULL DEFAULT 'SIN_RESPONSABLE',
ADD COLUMN     "returnDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "returnTime" TEXT NOT NULL DEFAULT '14:00–16:00',
ADD COLUMN     "studentIds" TEXT[];
