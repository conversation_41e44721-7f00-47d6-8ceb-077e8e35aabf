/*
  Warnings:

  - The values [CONFIRMED,IN_USE,RETURNED] on the enum `LoanStatus` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "LoanStatus_new" AS ENUM ('PENDING', 'AWAITING_PICKUP', 'ON_LOAN', 'LATE', 'LOST');
ALTER TABLE "loan" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "loan" ALTER COLUMN "status" TYPE "LoanStatus_new" USING ("status"::text::"LoanStatus_new");
ALTER TYPE "LoanStatus" RENAME TO "LoanStatus_old";
ALTER TYPE "LoanStatus_new" RENAME TO "LoanStatus";
DROP TYPE "LoanStatus_old";
ALTER TABLE "loan" ALTER COLUMN "status" SET DEFAULT 'PENDING';
COMMIT;

-- AlterTable
ALTER TABLE "loan" ADD COLUMN     "actualReturnDate" TIMESTAMP(3),
ADD COLUMN     "penaltyApplied" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "penaltyNotes" TEXT,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;
