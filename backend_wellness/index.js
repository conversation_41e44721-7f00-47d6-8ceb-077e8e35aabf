import dotenv from "dotenv";
dotenv.config();
import express from "express";
import cors from "cors";
import morgan from "morgan";
import http from "http";
import { PrismaClient } from "@prisma/client";
import adminRoutes from "./routes/adminRoutes.js";
import userRoutes from "./routes/userRoutes.js";
import spaceRoutes from "./routes/spaceRoutes.js";
import reservableRoutes from "./routes/reservableRoutes.js";
import reservationRoutes from "./routes/reservationRoutes.js";
import announceRoutes from "./routes/announceRoutes.js";
import graphsRoutes from "./routes/graphsRoutes.js";
import personCounterRoutes from "./routes/personCounterRoutes.js";
import accessControlRoutes from "./routes/accessControlRoutes.js";
import materialRoutes from "./routes/materialRoutes.js";
import loanRoutes from "./routes/loanRoutes.js";
import contadorSalidasRoutes, { setupContadorSalidasWebSocket } from "./routes/contadorSalidasRoutes.js";
const app = express();
const server = http.createServer(app);
app.use(morgan("dev"));
export const prisma = new PrismaClient();
const whitelist = process.env.WHITELISTED_DOMAINS?.split(",") ?? [];
let corsOptions;
if (process.env.NODE_ENV === "development") {
    corsOptions = {
        origin: true,
        methods: ["GET", "POST", "PUT", "DELETE"],
    };
}
else {
    corsOptions = {
        origin: function (origin, callback) {
            console.log(whitelist, origin);
            if (whitelist.includes(origin) || !origin) {
                callback(null, true);
            }
            else {
                console.log(origin);
                callback(new Error("Error de Cors"));
            }
        },
    };
}
app.use(cors(corsOptions));
app.use(express.json());
app.use("/api/admin", adminRoutes);
app.use("/api/user", userRoutes);
app.use("/api/space", spaceRoutes);
app.use("/api/announce", announceRoutes);
app.use("/api/reservable", reservableRoutes);
app.use("/api/reservation", reservationRoutes);
app.use("/api/graphs", graphsRoutes);
app.use("/api/personCounter", personCounterRoutes);
app.use("/api/material", materialRoutes);
app.use("/api/loan", loanRoutes);
app.use("/api/accessControl", accessControlRoutes);
app.use("/api/contador-salidas", contadorSalidasRoutes);
const PORT = process.env.PORT || 4000;
setupContadorSalidasWebSocket(server);
server.listen(PORT, () => {
    console.log(`Servidor Corriendo en ${PORT}`);
});
["SIGINT", "SIGTERM"].forEach((signal) => {
    process.on(signal, async () => {
        await prisma.$disconnect();
        process.exit(1);
    });
});
