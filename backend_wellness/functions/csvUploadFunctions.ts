// ======== En este archivo definiremos las funciones que se usan para el endpoint /api/accessControl/data/updateStudentsCsv ========

import { AlumnoInt, KeyValueStringObj } from "../types/GeneralTypes";
import { carreras, otrasCarreras } from "../constants/index.js";
import { quitarAcentos } from "../helpers/helpers.js";
// Path: functions/attendanceFunctions.ts

// Tipados solo validos para este archivo

type AlumnoCsvOmits = "id"
export interface AlumnoCsv extends Omit<AlumnoInt,AlumnoCsvOmits> {
  desc_nivel_acad_alumno: string;
}

// === Función encargada de modificar los programas para que incluyan ciertas llaves

function modifyProgramas(obj: KeyValueStringObj) {
  // Lista de llaves a excluir
  const excludeKeys = ["MAF-V", "MBA - G", "MBA-I", "MBA-V"];

  // Copia del objeto original
  const newObj = { ...obj };

  // Iterar sobre las llaves del objeto
  for (let key in obj) {
    // Verificar si la llave tiene un guión y no está en la lista de exclusión
    if (key.includes("-") && !excludeKeys.includes(key)) {
      // Crear la nueva llave tomando los caracteres antes del guión
      const newKey = key.split("-")[0].trim();
      // Insertar la nueva llave y su valor en el objeto copiado
      newObj[newKey] = obj[key];
    }
  }

  return newObj;
}

// === Funcion encargada de limpiar las claves de programa

function cleanValue(value: string) {
  return value.trim();
}

// == Función encargada de transformar las filas al formato correcto

export function transformRow(row: AlumnoCsv) {
  // Definimos los programas
  const programas = modifyProgramas(carreras);

  // Si el nombre contiene "NO HA LLENADO EL FORMULARIO NO PASAR", retornar null
  if (row.nombre_completo.includes("NO HA LLENADO EL FORMULARIO NO PASAR")) {
    console.log("Ignorando registro por nombre de formulario no pasar");
    return null;
  }

  // Verificar si la propiedad 'clave_programa' no existe y que 'desc_nivel_acad_alumno' no contenga las palabras deseadas
  if (typeof row.clave_programa === "undefined") {
    row.clave_programa = ".";

    // Verificamos si hay un nivel academico que no nos interesa agregar
    if (
      !/intercambio|internacional|residencias/i.test(row.desc_nivel_acad_alumno)
    ) {
      console.log(
        "Ignorando registro por intercambio, internacional o residencias"
      );
      return null;
    }

  }

    // Verificamos que tenga una descripcion de genero

    if (typeof row.desc_genero === "undefined") {
      // Si no tiene un genero asignado entonces ignoramos
      console.log("Ignorando registro por no tener genero");
      return null;
    }

    // Pasamos la matricula a minusculas

    row.matricula = row.matricula.toLowerCase();

    // Funcion que hace el test de regex para buscar una palabra en la
    // propiedad de clave_programa y desc_nivel_acad_alumno

    const hasKeyword = (keyword: string) => {
      const regex = new RegExp(keyword, "i");

      return (
        regex.test(row.clave_programa) || regex.test(row.desc_nivel_acad_alumno)
      );
    };

    // Funcion que verifica si la clave programa es MATCHED_KEY

    const isMatchedKey = () =>
      Object.keys(programas).find((key) => row.clave_programa.startsWith(key))
        ? "MATCHED_KEY"
        : false;

    // Funcion que determina si pertenece a la clave programa de otras carreras
    const isOtrasCarreras = () =>
      otrasCarreras.some((carrera) => row.clave_programa.startsWith(carrera))
        ? "OTRAS_CARRERAS"
        : false;

    // Funcion que determina si es de intercambio
    const isIntercambio = () =>
      hasKeyword("intercambio") ? "INTERCAMBIO" : false;

    // Funcion que determina si es internacional
    const isInternacional = () =>
      hasKeyword("internacional") ? "INTERNACIONAL" : false;

    // Funcion que determina si es de residencias
    const isResidencias = () =>
      hasKeyword("residencias") ? "RESIDENCIAS" : false;

    // Determinamos el caso en el que se encuentra el alumno

    const caseType =
      isMatchedKey() ||
      isOtrasCarreras() ||
      isIntercambio() ||
      isInternacional() ||
      isResidencias() ||
      "DEFAULT";

    // Limpiamos las claves de programa

    const cleanedClavePrograma = cleanValue(row.clave_programa);

    // Hacemos las acciones correspondientes a cada caso

    switch (caseType) {
      case "MATCHED_KEY":
        const keyForProgramas = cleanedClavePrograma.slice(0, 2);
        row.clave_programa = cleanedClavePrograma;
        row.desc_programa = programas[keyForProgramas];
        break;
      case "OTRAS_CARRERAS":
        break;
      case "INTERCAMBIO":
        row.clave_programa = "Intercambio";
        row.desc_programa = null;
        break;
      case "INTERNACIONAL":
        // Comprobamos si la descripcion del nivel academico contiene la palabra internacional
        if (hasKeyword("internacional")) {
          row.clave_programa = /internacional/i.test(row.desc_nivel_acad_alumno)
            ? row.desc_nivel_acad_alumno
            : row.clave_programa;
        }
        row.desc_programa = null;
        break;
      case "RESIDENCIAS":
        if (hasKeyword("residencias")) {
          row.clave_programa = /residencias/i.test(row.desc_nivel_acad_alumno)
            ? row.desc_nivel_acad_alumno
            : row.clave_programa;
        }
        row.desc_programa = null;
        break;
      default:
        // Si no coincide con ningun caso entonces retornamos null
        return null;
        break;
    }

    // Transformamos el valor de desc_genero
    const generoTrim = row.desc_genero.trim()
    row.desc_genero = generoTrim

    // Transformamos la matricula a minusculas
    row.matricula = row.matricula.toLowerCase()

    // Eliminamos los acentos del nombre
    row.nombre_completo = quitarAcentos(row.nombre_completo)
    
    // Retornamos el objeto transformado
    return row;
  }

