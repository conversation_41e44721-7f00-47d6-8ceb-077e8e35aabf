// Functions for handling material CSV uploads

// Interface for CSV row data
export interface MaterialCsvRow {
  name: string;
  quantity: string;
  minMatriculas: string;
  leadTimeDays: string;
  rules: string;
  replacementCost: string;
  image?: string; // Optional image URL
}

/**
 * Validates a CSV row for material creation
 * @param row The CSV row data
 * @returns Validated material data or null if invalid
 */
export function validateMaterialRow(row: any): MaterialCsvRow | null {
  try {
    // Check required fields
    if (!row.name || row.name.trim() === '') {
      console.log("Ignorando material sin nombre");
      return null;
    }

    // Validate numeric fields
    const quantity = parseInt(row.quantity);
    const minMatriculas = parseInt(row.minMatriculas);
    const leadTimeDays = parseInt(row.leadTimeDays);
    const replacementCost = parseFloat(row.replacementCost);

    if (isNaN(quantity) || quantity < 0) {
      console.log(`Ignorando material "${row.name}" con cantidad inválida`);
      return null;
    }

    if (isNaN(minMatriculas) || minMatriculas < 0) {
      console.log(`Ignorando material "${row.name}" con matrículas mínimas inválidas`);
      return null;
    }

    if (isNaN(leadTimeDays) || leadTimeDays < 0) {
      console.log(`Ignorando material "${row.name}" con días de préstamo inválidos`);
      return null;
    }

    if (isNaN(replacementCost) || replacementCost < 0) {
      console.log(`Ignorando material "${row.name}" con costo de reposición inválido`);
      return null;
    }

    // Return validated row
    return {
      name: row.name.trim(),
      quantity: quantity.toString(),
      minMatriculas: minMatriculas.toString(),
      leadTimeDays: leadTimeDays.toString(),
      rules: row.rules || "Sin reglas específicas",
      replacementCost: replacementCost.toString(),
      image: row.image
    };
  } catch (error) {
    console.error("Error validating material row:", error);
    return null;
  }
}

/**
 * Normalizes CSV row keys
 * @param row Raw CSV row
 * @returns Normalized row with consistent keys
 */
export function normalizeRowKeys(row: any): any {
  const normalizedRow: any = {};

  // Map of possible column names to standardized keys
  const keyMap: Record<string, string> = {
    'nombre': 'name',
    'name': 'name',
    'cantidad': 'quantity',
    'quantity': 'quantity',
    'matriculasminimas': 'minMatriculas',
    'minmatriculas': 'minMatriculas',
    'diasdeprestamo': 'leadTimeDays',
    'leadtimedays': 'leadTimeDays',
    'reglas': 'rules',
    'rules': 'rules',
    'costodereposicion': 'replacementCost',
    'replacementcost': 'replacementCost',
    'imagen': 'image',
    'image': 'image'
  };

  // Normalize each key in the row
  Object.keys(row).forEach(key => {
    // Remove accents, spaces, and convert to lowercase for matching
    const normalizedKey = key.trim()
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/\s+/g, "");

    // Map to standard key if possible
    if (keyMap[normalizedKey]) {
      normalizedRow[keyMap[normalizedKey]] = row[key];
    } else {
      // Keep original key if no mapping found
      normalizedRow[key] = row[key];
    }
  });

  return normalizedRow;
}
