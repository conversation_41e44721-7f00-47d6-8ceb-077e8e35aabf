export function validateMaterialRow(row) {
    try {
        if (!row.name || row.name.trim() === '') {
            console.log("Ignorando material sin nombre");
            return null;
        }
        const quantity = parseInt(row.quantity);
        const minMatriculas = parseInt(row.minMatriculas);
        const leadTimeDays = parseInt(row.leadTimeDays);
        const replacementCost = parseFloat(row.replacementCost);
        if (isNaN(quantity) || quantity < 0) {
            console.log(`Ignorando material "${row.name}" con cantidad inválida`);
            return null;
        }
        if (isNaN(minMatriculas) || minMatriculas < 0) {
            console.log(`Ignorando material "${row.name}" con matrículas mínimas inválidas`);
            return null;
        }
        if (isNaN(leadTimeDays) || leadTimeDays < 0) {
            console.log(`Ignorando material "${row.name}" con días de préstamo inválidos`);
            return null;
        }
        if (isNaN(replacementCost) || replacementCost < 0) {
            console.log(`Ignorando material "${row.name}" con costo de reposición inválido`);
            return null;
        }
        return {
            name: row.name.trim(),
            quantity: quantity.toString(),
            minMatriculas: minMatriculas.toString(),
            leadTimeDays: leadTimeDays.toString(),
            rules: row.rules || "Sin reglas específicas",
            replacementCost: replacementCost.toString(),
            image: row.image
        };
    }
    catch (error) {
        console.error("Error validating material row:", error);
        return null;
    }
}
export function normalizeRowKeys(row) {
    const normalizedRow = {};
    const keyMap = {
        'nombre': 'name',
        'name': 'name',
        'cantidad': 'quantity',
        'quantity': 'quantity',
        'matriculasminimas': 'minMatriculas',
        'minmatriculas': 'minMatriculas',
        'diasdeprestamo': 'leadTimeDays',
        'leadtimedays': 'leadTimeDays',
        'reglas': 'rules',
        'rules': 'rules',
        'costodereposicion': 'replacementCost',
        'replacementcost': 'replacementCost',
        'imagen': 'image',
        'image': 'image'
    };
    Object.keys(row).forEach(key => {
        const normalizedKey = key.trim()
            .toLowerCase()
            .normalize("NFD")
            .replace(/[\u0300-\u036f]/g, "")
            .replace(/\s+/g, "");
        if (keyMap[normalizedKey]) {
            normalizedRow[keyMap[normalizedKey]] = row[key];
        }
        else {
            normalizedRow[key] = row[key];
        }
    });
    return normalizedRow;
}
