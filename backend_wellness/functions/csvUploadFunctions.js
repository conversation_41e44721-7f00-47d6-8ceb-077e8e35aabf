import { carreras, otrasCarreras } from "../constants/index.js";
import { quitarAcentos } from "../helpers/helpers.js";
function modifyProgramas(obj) {
    const excludeKeys = ["MAF-V", "MBA - G", "MBA-I", "MBA-V"];
    const newObj = { ...obj };
    for (let key in obj) {
        if (key.includes("-") && !excludeKeys.includes(key)) {
            const newKey = key.split("-")[0].trim();
            newObj[newKey] = obj[key];
        }
    }
    return newObj;
}
function cleanValue(value) {
    return value.trim();
}
export function transformRow(row) {
    const programas = modifyProgramas(carreras);
    if (row.nombre_completo.includes("NO HA LLENADO EL FORMULARIO NO PASAR")) {
        console.log("Ignorando registro por nombre de formulario no pasar");
        return null;
    }
    if (typeof row.clave_programa === "undefined") {
        row.clave_programa = ".";
        if (!/intercambio|internacional|residencias/i.test(row.desc_nivel_acad_alumno)) {
            console.log("Ignorando registro por intercambio, internacional o residencias");
            return null;
        }
    }
    if (typeof row.desc_genero === "undefined") {
        console.log("Ignorando registro por no tener genero");
        return null;
    }
    row.matricula = row.matricula.toLowerCase();
    const hasKeyword = (keyword) => {
        const regex = new RegExp(keyword, "i");
        return (regex.test(row.clave_programa) || regex.test(row.desc_nivel_acad_alumno));
    };
    const isMatchedKey = () => Object.keys(programas).find((key) => row.clave_programa.startsWith(key))
        ? "MATCHED_KEY"
        : false;
    const isOtrasCarreras = () => otrasCarreras.some((carrera) => row.clave_programa.startsWith(carrera))
        ? "OTRAS_CARRERAS"
        : false;
    const isIntercambio = () => hasKeyword("intercambio") ? "INTERCAMBIO" : false;
    const isInternacional = () => hasKeyword("internacional") ? "INTERNACIONAL" : false;
    const isResidencias = () => hasKeyword("residencias") ? "RESIDENCIAS" : false;
    const caseType = isMatchedKey() ||
        isOtrasCarreras() ||
        isIntercambio() ||
        isInternacional() ||
        isResidencias() ||
        "DEFAULT";
    const cleanedClavePrograma = cleanValue(row.clave_programa);
    switch (caseType) {
        case "MATCHED_KEY":
            const keyForProgramas = cleanedClavePrograma.slice(0, 2);
            row.clave_programa = cleanedClavePrograma;
            row.desc_programa = programas[keyForProgramas];
            break;
        case "OTRAS_CARRERAS":
            break;
        case "INTERCAMBIO":
            row.clave_programa = "Intercambio";
            row.desc_programa = null;
            break;
        case "INTERNACIONAL":
            if (hasKeyword("internacional")) {
                row.clave_programa = /internacional/i.test(row.desc_nivel_acad_alumno)
                    ? row.desc_nivel_acad_alumno
                    : row.clave_programa;
            }
            row.desc_programa = null;
            break;
        case "RESIDENCIAS":
            if (hasKeyword("residencias")) {
                row.clave_programa = /residencias/i.test(row.desc_nivel_acad_alumno)
                    ? row.desc_nivel_acad_alumno
                    : row.clave_programa;
            }
            row.desc_programa = null;
            break;
        default:
            return null;
            break;
    }
    const generoTrim = row.desc_genero.trim();
    row.desc_genero = generoTrim;
    row.matricula = row.matricula.toLowerCase();
    row.nombre_completo = quitarAcentos(row.nombre_completo);
    return row;
}
