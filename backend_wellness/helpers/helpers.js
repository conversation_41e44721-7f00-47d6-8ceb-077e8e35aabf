import { DateTime } from "luxon";
export const capitalize = (s) => s.charAt(0).toUpperCase() + s.slice(1);
export const generarId = () => {
    const random = Math.random().toString(32).substring(2);
    const fecha = Date.now().toString(32);
    return random + fecha;
};
export function graphBigIntToInt(data) {
    return data.map(dat => ({ label: dat.label, value: Number(dat.value) }));
}
export function dateFormatter(date) {
    const options = {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
    };
    return date.toLocaleDateString("es-MX", options);
}
export function hourFormatter(date) {
    const options = {
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
    };
    return date.toLocaleDateString("es-MX", options);
}
export function quitarAcentos(s) {
    return s.normalize("NFD").replace(/[\u0300-\u036f]/g, "");
}
export function toMonterreyTimeZone(date) {
    return DateTime.fromJSDate(date).setZone("America/Monterrey").toJSDate();
}
