import express from "express";
import { adminCreateReservation, adminDownloadReservations, adminGetAllReservations, adminUpdateReservation, userCreateReservation, userDeleteReservation, userReservations} from "../controllers/ReservationController.js";
import adminAuth from "../middleware/adminAuth.js";
import userAuth from "../middleware/userAuth.js";

const router = express.Router();

//router.post("/createReserva", createReservation);
// Ruta padre "/api/reservation"

router.get("/all/:reservableId",adminAuth,adminGetAllReservations)
router.post("/downloadReservations",adminAuth,adminDownloadReservations)

// = Endpoint para modificar una reserva
router.put("/updateReservation",adminAuth,adminUpdateReservation)

// = Endpoint para reservar un espacio
router.post("/reserve/admin",adminAuth,adminCreateReservation)
router.post("/reserve/:id",userAuth,userCreateReservation)

// = Endpointa para obtener las reservas de un usuario
router.get("/reserves",userAuth,userReservations)

// = Borrar reservacion
router.delete("/delete/:id",userAuth,userDeleteReservation)

export default router;