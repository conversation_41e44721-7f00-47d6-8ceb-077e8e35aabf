// Este grupo de rutas se encarga de manejar la conexión con el microservicio de control de acceso

import express from "express";
import { addUser, getUser, getUserByIdController, getUsers, getUsersLastAttendance, updateUserController } from "../../controllers/AccessControlControllers/AlumnoController.js";


const router = express.Router();


// Ruta padre "/api/accessControl/alumno"

// == Conexión con el sistema de accesos
router.get("/getUser/:matricula",getUser)
router.get("/getAlumnos",getUsers)
router.get("/getUsersLastAttendance/:search",getUsersLastAttendance)

router.post("/addAlumno",addUser)

router.get("/getAlumno/:id",getUserByIdController)
router.put("/updateAlumno",updateUserController)

export default router;
