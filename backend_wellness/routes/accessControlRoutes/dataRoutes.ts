// Este grupo de rutas se encarga de manejar la conexión con el microservicio de control de acceso

import express from "express";
import { exportAsistenciaDataCsv, exportAsistenciaDataExcel, exportDataCsv, exportUniqueAttendancesDataCsv, updateStudentsCsv } from "../../controllers/AccessControlControllers/DataController.js";
import multer from "multer";
import timeout from 'connect-timeout';

const router = express.Router();

// Usar multer
const storage = multer.diskStorage({});
const upload = multer({ storage });


// Ruta padre "/api/accessControl/data"

router.use(timeout('300s'));

router.post("/exportAsistenciaDataCsv",exportAsistenciaDataCsv)
router.post("/exportUniqueAttendancesDataCsv",exportUniqueAttendancesDataCsv)
router.post("/exportAsistenciaDataExcel",exportAsistenciaDataExcel)

router.post("/exportDataCsv",exportDataCsv)
router.post("/updateStudentsCsv",upload.single("alumnos"),updateStudentsCsv as any)

export default router;