import express from "express";
import { getAsistencia, getAsistenciaCount, graphs, markAttendance, updateAttendance } from "../../controllers/AccessControlControllers/AttendanceController.js";
const router = express.Router();
router.get("/getAsistencia", getAsistencia);
router.get("/getAsistenciaCount", getAsistenciaCount);
router.post("/updateAttendance", updateAttendance);
router.post("/markAttendance", markAttendance);
router.get("/graphs/:startDate/:endDate", graphs);
export default router;
