// Este grupo de rutas se encarga de manejar la conexión con el microservicio de control de acceso

import express from "express";
import { getAsistencia, getAsistenciaCount, getAsistenciaToday, graphs, markAttendance, updateAttendance } from "../../controllers/AccessControlControllers/AttendanceController.js";



const router = express.Router();


// Ruta padre "/api/accessControl/asistencia"

router.get("/getAsistencia",getAsistencia)
//router.get("/getAsistenciaToday",getAsistenciaToday)
router.get("/getAsistenciaCount",getAsistenciaCount)

router.post("/updateAttendance",updateAttendance)

router.post("/markAttendance",markAttendance)

router.get("/graphs/:startDate/:endDate",graphs)



export default router;
