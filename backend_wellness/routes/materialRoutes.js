import express from "express";
import { createMaterial, getAllMaterials, getAvailableMaterials, getMaterialById, updateMaterial, deleteMaterial, uploadCsv } from "../controllers/MaterialController.js";
import adminAuth from "../middleware/adminAuth.js";
import userAuth from "../middleware/userAuth.js";
import multiAuth from "../middleware/multiAuth.js";
import multer from "multer";
import fs from 'fs';
const tempDir = './temp';
if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
}
const router = express.Router();
const storage = multer.diskStorage({});
const upload = multer({ storage });
router.post('/', adminAuth, upload.single('image'), createMaterial);
router.post('/uploadCsv', adminAuth, upload.single('file'), uploadCsv);
router.get('/getAll', multiAuth, getAllMaterials);
router.get('/getById/:id', multiAuth, getMaterialById);
router.put('/update/:id', adminAuth, upload.single('image'), updateMaterial);
router.delete('/delete/:id', adminAuth, deleteMaterial);
router.get('/available', userAuth, getAvailableMaterials);
router.get('/details/:id', userAuth, getMaterialById);
router.get('/public/available', getAvailableMaterials);
router.get('/public/all', getAllMaterials);
router.get('/public/:id', multiAuth, getMaterialById);
export default router;
