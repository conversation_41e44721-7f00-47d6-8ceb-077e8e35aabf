import express from "express";
import {
    adminNextClass,
    createUser,
    deleteAdmin,
    deleteCarouselImage,
    getAdmins,
    getCarouselImages,
    login,
    profile,
    updateAdmin,
    updateCarouselImage,
    uploadCarouselImage
    
} from "../controllers/AdminController.js"

import adminAuth from "../middleware/adminAuth.js"
import {topAdminVerify} from "../middleware/topAdminVerify.js"

import multer from "multer";
import multiAuth from "../middleware/multiAuth.js";

const router = express.Router();

// Usar multer
const storage = multer.diskStorage({});
const upload = multer({ storage });


// Ruta Padre  /api/admin

router.post("/login", login)
router.get("/profile",adminAuth,profile)

//Imagenes del carousel
router.post("/uploadCarouselImage",adminAuth,upload.single("image"),uploadCarouselImage as any);
router.delete("/deleteCarouselImage/:id",adminAuth,deleteCarouselImage);
router.put("/updateCarouselImage",adminAuth,updateCarouselImage);
router.get("/carouselImages",multiAuth,getCarouselImages);

router.get("/nextClass",adminAuth,adminNextClass)

router.post("/", adminAuth,topAdminVerify,createUser)
router.put("/", adminAuth,topAdminVerify,updateAdmin)
router.get("/",adminAuth,getAdmins)
router.delete("/:id",adminAuth,topAdminVerify,deleteAdmin)



export default router;
