import express from "express";
import { WebSocketServer } from "ws";
import dotenv from 'dotenv';
import jwt from 'jsonwebtoken';
import { prisma } from '../index.js';
dotenv.config();
const router = express.Router();
router.get('/config', (_, res) => {
    console.log("GET /config - Returning current configuration");
    res.json(currentConfig);
});
router.post('/config', (req, res) => {
    console.log("POST /config - Received configuration update:", req.body);
    try {
        const newConfig = req.body;
        if (newConfig.camera) {
            if (newConfig.camera.frame_rate)
                currentConfig.camera.frame_rate = Number(newConfig.camera.frame_rate);
            if (newConfig.camera.resize_width)
                currentConfig.camera.resize_width = Number(newConfig.camera.resize_width);
            if (newConfig.camera.resize_height)
                currentConfig.camera.resize_height = Number(newConfig.camera.resize_height);
            if (newConfig.camera.frame_skip)
                currentConfig.camera.frame_skip = Number(newConfig.camera.frame_skip);
        }
        if (newConfig.processor) {
            if (newConfig.processor.confidence)
                currentConfig.processor.confidence = Number(newConfig.processor.confidence);
            if (newConfig.processor.iou)
                currentConfig.processor.iou = Number(newConfig.processor.iou);
            if (newConfig.processor.frame_rate)
                currentConfig.processor.frame_rate = Number(newConfig.processor.frame_rate);
            if (newConfig.processor.line_position_percent)
                currentConfig.processor.line_position_percent = Number(newConfig.processor.line_position_percent);
            if (newConfig.processor.tracker) {
                if (newConfig.processor.tracker.track_thresh)
                    currentConfig.processor.tracker.track_thresh = Number(newConfig.processor.tracker.track_thresh);
                if (newConfig.processor.tracker.track_buffer)
                    currentConfig.processor.tracker.track_buffer = Number(newConfig.processor.tracker.track_buffer);
                if (newConfig.processor.tracker.match_thresh)
                    currentConfig.processor.tracker.match_thresh = Number(newConfig.processor.tracker.match_thresh);
                if (newConfig.processor.tracker.aspect_ratio_thresh)
                    currentConfig.processor.tracker.aspect_ratio_thresh = Number(newConfig.processor.tracker.aspect_ratio_thresh);
                if (newConfig.processor.tracker.min_box_area)
                    currentConfig.processor.tracker.min_box_area = Number(newConfig.processor.tracker.min_box_area);
            }
        }
        if (router.broadcastConfigUpdate) {
            router.broadcastConfigUpdate();
        }
        res.json({ success: true, config: currentConfig });
    }
    catch (error) {
        console.error('Error updating configuration:', error);
        res.status(400).json({ success: false, error: 'Invalid configuration data' });
    }
});
const defaultConfig = {
    camera: {
        frame_rate: 3,
        resize_width: 600,
        resize_height: 400,
        frame_skip: 2
    },
    processor: {
        confidence: 0.4,
        iou: 0.45,
        frame_rate: 15,
        line_position_percent: 50,
        tracker: {
            track_thresh: 0.4,
            track_buffer: 50,
            match_thresh: 0.65,
            aspect_ratio_thresh: 1.6,
            min_box_area: 10
        }
    }
};
const performanceMetrics = {
    camera: {
        fps: 0,
        bandwidth: 0,
        frame_size: { width: 0, height: 0 },
        avg_capture_time: 0,
        avg_send_time: 0,
        last_update: 0
    },
    processor: {
        fps: 0,
        max_fps_capacity: 0,
        avg_processing_time: 0,
        last_update: 0
    }
};
let currentConfig = JSON.parse(JSON.stringify(defaultConfig));
export function setupContadorSalidasWebSocket(server) {
    const wss = new WebSocketServer({ noServer: true });
    const cameraClients = new Map();
    const processingClients = new Map();
    const adminClients = new Map();
    function broadcastConfigUpdate() {
        cameraClients.forEach((ws) => {
            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'command',
                    action: 'set_config',
                    ...currentConfig.camera
                }));
            }
        });
        processingClients.forEach((ws) => {
            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'command',
                    action: 'set_config',
                    ...currentConfig.processor
                }));
            }
        });
        adminClients.forEach((ws) => {
            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'config_update',
                    config: currentConfig
                }));
            }
        });
    }
    router.broadcastConfigUpdate = broadcastConfigUpdate;
    server.on('upgrade', (request, socket, head) => {
        const pathname = request.url || '';
        if (pathname === '/ws/contador-salidas') {
            const auth = request.headers['authorization']?.replace('Bearer ', '') || '';
            wss.handleUpgrade(request, socket, head, (ws) => {
                ws.authToken = auth;
                wss.emit('connection', ws, request);
            });
        }
    });
    wss.on('connection', (ws) => {
        let clientId = null;
        let clientType = null;
        let adminId = null;
        ws.on('message', async (message) => {
            try {
                const data = JSON.parse(message);
                if (data.type === 'client_info') {
                    clientId = data.client_id;
                    clientType = data.client_type;
                    let authenticated = false;
                    const authToken = ws.authToken;
                    if (clientType === 'camera' || clientType === 'processor') {
                        authenticated = authToken === process.env.MICROSERVICE_APIKEY;
                        if (!authenticated) {
                            console.log(`Authentication failed for ${clientType} client: ${clientId}`);
                            ws.close(1008, 'Authentication failed');
                            return;
                        }
                    }
                    else if (clientType === 'admin' && data.token) {
                        console.log(`Authenticating admin client: ${clientId}`);
                        try {
                            const jwtSecret = process.env.JWT_SECRET || '';
                            console.log(`JWT Secret available: ${jwtSecret ? 'Yes' : 'No'}`);
                            const decoded = jwt.verify(data.token, jwtSecret);
                            console.log(`JWT decoded successfully, admin ID: ${decoded?.id}`);
                            if (decoded && decoded.id) {
                                const admin = await prisma.admin.findUnique({
                                    where: { id: decoded.id }
                                });
                                if (admin) {
                                    authenticated = true;
                                    adminId = admin.id;
                                    console.log(`Admin found in database, ID: ${admin.id}`);
                                }
                                else {
                                    console.log(`Admin not found in database for ID: ${decoded.id}`);
                                }
                            }
                        }
                        catch (error) {
                            console.error('JWT verification error:', error);
                        }
                        if (!authenticated) {
                            console.log(`Authentication failed for admin client: ${clientId}`);
                            ws.close(1008, 'Authentication failed');
                            return;
                        }
                        else {
                            console.log(`Admin client authenticated successfully: ${clientId}, Admin ID: ${adminId}`);
                        }
                    }
                    if (clientType === 'camera') {
                        console.log(`Camera client connected: ${clientId}`);
                        cameraClients.set(clientId, ws);
                        ws.send(JSON.stringify({
                            type: 'command',
                            action: 'set_config',
                            ...currentConfig.camera
                        }));
                        processingClients.forEach((processorWs) => {
                            processorWs.send(JSON.stringify({
                                type: 'camera_connected',
                                client_id: clientId
                            }));
                        });
                        adminClients.forEach((adminWs) => {
                            adminWs.send(JSON.stringify({
                                type: 'status',
                                message: 'camera connected',
                                connected: true
                            }));
                        });
                    }
                    else if (clientType === 'processor') {
                        console.log(`Processing client connected: ${clientId}`);
                        processingClients.set(clientId, ws);
                        ws.send(JSON.stringify({
                            type: 'command',
                            action: 'set_config',
                            ...currentConfig.processor
                        }));
                        const cameras = Array.from(cameraClients.keys());
                        ws.send(JSON.stringify({
                            type: 'camera_list',
                            cameras: cameras
                        }));
                    }
                    else if (clientType === 'admin') {
                        console.log(`Admin client connected: ${clientId}, Admin ID: ${adminId}`);
                        adminClients.set(clientId, ws);
                        const cameraConnected = cameraClients.size > 0;
                        ws.send(JSON.stringify({
                            type: 'status',
                            message: 'camera status',
                            connected: cameraConnected
                        }));
                        ws.send(JSON.stringify({
                            type: 'config_update',
                            config: currentConfig
                        }));
                    }
                    ws.send(JSON.stringify({
                        type: 'status',
                        message: 'Connected successfully'
                    }));
                }
                else if (data.type === 'frame' && clientType === 'camera') {
                    console.log(`Received frame from camera ${clientId}, forwarding to ${processingClients.size} processors`);
                    processingClients.forEach((processorWs) => {
                        if (processorWs.readyState === 1) {
                            processorWs.send(message);
                        }
                    });
                }
                else if (data.type === 'processed_frame' && clientType === 'processor') {
                    console.log(`Received processed frame from processor ${clientId}, forwarding to ${adminClients.size} admins`);
                    if (adminClients.size > 0) {
                        console.log(`Sending processed frame to ${adminClients.size} admin clients, frame length: ${data.frame ? data.frame.length : 0}`);
                        if (data.frame && typeof data.frame === 'string' && data.frame.length > 0) {
                            const frameMessage = {
                                type: 'frame',
                                client_id: data.client_id,
                                timestamp: data.timestamp || Date.now(),
                                frame: data.frame,
                                processed: true
                            };
                            const frameJson = JSON.stringify(frameMessage);
                            adminClients.forEach((adminWs) => {
                                if (adminWs.readyState === 1) {
                                    adminWs.send(frameJson);
                                }
                            });
                        }
                        else {
                            console.log('Invalid processed frame data received, not forwarding to admin clients');
                        }
                    }
                    else {
                        console.log('No admin clients connected to send processed frame to');
                    }
                }
                else if (data.type === 'command' && clientType === 'processor') {
                    const targetClientId = data.target_client_id;
                    if (targetClientId && cameraClients.has(targetClientId)) {
                        const cameraWs = cameraClients.get(targetClientId);
                        if (cameraWs.readyState === 1) {
                            cameraWs.send(message);
                        }
                    }
                    else if (!targetClientId) {
                        cameraClients.forEach((cameraWs) => {
                            if (cameraWs.readyState === 1) {
                                cameraWs.send(message);
                            }
                        });
                    }
                }
                else if (data.type === 'exit_detected' && clientType === 'processor') {
                    console.log(`Exit detected by processor ${clientId}`);
                    if (data.total_exits !== undefined && data.current_count !== undefined) {
                        const counterData = {
                            type: 'counter_data',
                            total_exits: data.total_exits,
                            current_count: data.current_count,
                            timestamp: Date.now()
                        };
                        adminClients.forEach((adminWs) => {
                            if (adminWs.readyState === 1) {
                                adminWs.send(JSON.stringify(counterData));
                            }
                        });
                    }
                }
                else if (data.type === 'counter_data' && clientType === 'processor') {
                    adminClients.forEach((adminWs) => {
                        if (adminWs.readyState === 1) {
                            adminWs.send(message);
                        }
                    });
                }
                else if (data.type === 'performance_metrics' && data.metrics) {
                    console.log(`Received performance metrics from client ${clientId}, type: ${clientType}`);
                    console.log('Metrics data:', JSON.stringify(data.metrics).substring(0, 200));
                    if (clientType === 'processor') {
                        console.log('Processing processor metrics...');
                        performanceMetrics.processor = {
                            fps: data.metrics.current_fps || 0,
                            max_fps_capacity: data.metrics.max_fps_capacity || 0,
                            avg_processing_time: data.metrics.avg_processing_time || 0,
                            last_update: Date.now()
                        };
                        console.log('Updated processor metrics:', JSON.stringify(performanceMetrics.processor));
                    }
                    else if (clientType === 'camera') {
                        console.log('Processing camera metrics...');
                        performanceMetrics.camera = {
                            fps: data.metrics.current_fps || 0,
                            bandwidth: data.metrics.bandwidth || 0,
                            frame_size: data.metrics.frame_size || { width: 0, height: 0 },
                            avg_capture_time: data.metrics.avg_capture_time || 0,
                            avg_send_time: data.metrics.avg_send_time || 0,
                            last_update: Date.now()
                        };
                        console.log('Updated camera metrics:', JSON.stringify(performanceMetrics.camera));
                    }
                    else {
                        console.log('Ignoring metrics - unknown client type:', clientType);
                    }
                    console.log('Forwarding performance metrics to admin clients...');
                    adminClients.forEach((adminWs) => {
                        if (adminWs.readyState === 1) {
                            adminWs.send(JSON.stringify({
                                type: "performance_update",
                                metrics: performanceMetrics
                            }));
                        }
                    });
                }
                else if (data.type === 'camera_metrics') {
                    console.log(`Received legacy camera metrics from client ${clientId}, type: ${clientType}`);
                    console.log('Metrics data:', JSON.stringify(data.metrics).substring(0, 200));
                    if (clientType === 'camera' && data.metrics) {
                        console.log('Processing legacy camera metrics...');
                        performanceMetrics.camera = {
                            fps: data.metrics.current_fps || 0,
                            bandwidth: data.metrics.bandwidth || 0,
                            frame_size: data.metrics.frame_size || { width: 0, height: 0 },
                            avg_capture_time: data.metrics.avg_capture_time || 0,
                            avg_send_time: data.metrics.avg_send_time || 0,
                            last_update: Date.now()
                        };
                        console.log('Updated camera metrics from legacy format:', JSON.stringify(performanceMetrics.camera));
                    }
                    console.log('Forwarding performance metrics to admin clients...');
                    adminClients.forEach((adminWs) => {
                        if (adminWs.readyState === 1) {
                            adminWs.send(JSON.stringify({
                                type: "performance_update",
                                metrics: performanceMetrics
                            }));
                        }
                    });
                }
            }
            catch (error) {
                console.error('Error processing WebSocket message:', error);
            }
        });
        ws.on('close', () => {
            if (clientId) {
                if (clientType === 'camera') {
                    console.log(`Camera client disconnected: ${clientId}`);
                    cameraClients.delete(clientId);
                    processingClients.forEach((processorWs) => {
                        processorWs.send(JSON.stringify({
                            type: 'camera_disconnected',
                            client_id: clientId
                        }));
                    });
                    adminClients.forEach((adminWs) => {
                        adminWs.send(JSON.stringify({
                            type: 'status',
                            message: 'camera disconnected',
                            connected: false
                        }));
                    });
                }
                else if (clientType === 'processor') {
                    console.log(`Processing client disconnected: ${clientId}`);
                    processingClients.delete(clientId);
                }
                else if (clientType === 'admin') {
                    console.log(`Admin client disconnected: ${clientId}`);
                    adminClients.delete(clientId);
                }
            }
        });
    });
    console.log('WebSocket server for contador salidas is set up');
    return wss;
}
export default router;
