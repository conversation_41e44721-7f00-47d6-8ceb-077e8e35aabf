import express from "express";
import {create, login, profile, searchUser} from "../controllers/UserController.js"

import userAuth from "../middleware/userAuth.js"
import { getUserSpaces } from "../controllers/SpaceController.js";
import adminAuth from "../middleware/adminAuth.js";

const router = express.Router();

// === Ruta Padre /api/user


// == Autenticacion
router.post("/create", create)
router.post("/login",login)
router.get("/profile",userAuth,profile)


router.get("/search/:query",adminAuth,searchUser)

//router.post("/addUser")

router.get("/getSpaces",userAuth, getUserSpaces)




export default router;
