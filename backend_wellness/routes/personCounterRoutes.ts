// Grupo de rutas encargado de conectar los microservicios relacionados con el contador de personas

import dotenv from 'dotenv';
dotenv.config();

import { Router } from 'express';
import { countPerson } from '../controllers/PersonCounterController.js';
import microserviceAuth from '../middleware/microserviceAuth.js';

const router = Router();

// Ruta padre "/api/personCounter"

router.post("/countPerson",(req,res,next)=> microserviceAuth(req,res,next,process.env.MICROSERVICE_APIKEY ?? ""),countPerson)

export default router;

