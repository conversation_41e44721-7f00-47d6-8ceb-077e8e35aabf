import { Router } from "express";
import { createLoan, getAllLoans, updateLoanStatus, getLoansByResponsible, getLoansByStatus, checkLateLoans, validateStudentIds } from "../controllers/LoanController.js";
import adminAuth from "../middleware/adminAuth.js";
import userAuth from "../middleware/userAuth.js";
import multiAuth from "../middleware/multiAuth.js";
const router = Router();
router.get("/getAll", adminAuth, getAllLoans);
router.get("/byStatus/:status", adminAuth, getLoansByStatus);
router.put("/updateStatus/:id", adminAuth, updateLoanStatus);
router.post("/checkLate", adminAuth, checkLateLoans);
router.post("/create", multiAuth, createLoan);
router.get("/myLoans/:registration", userAuth, getLoansByResponsible);
router.post("/validateStudentIds", multiAuth, validateStudentIds);
export default router;
