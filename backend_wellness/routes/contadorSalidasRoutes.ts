// Routes for contador salidas WebSocket relay
import express from "express";
import { WebSocketServer } from "ws";
import http from "http";
import microserviceAuth from "../middleware/microserviceAuth.js";
import dotenv from 'dotenv';
import jwt from 'jsonwebtoken';
import { prisma } from '../index.js';

dotenv.config();

const router = express.Router();

// API endpoint to get current configuration
router.get('/config', (_, res) => {
    console.log("GET /config - Returning current configuration");
    res.json(currentConfig);
});

// API endpoint to update configuration
router.post('/config', (req, res) => {
    console.log("POST /config - Received configuration update:", req.body);
    try {
        const newConfig = req.body;

        // Validate configuration (basic validation)
        if (newConfig.camera) {
            // Update camera config
            if (newConfig.camera.frame_rate) currentConfig.camera.frame_rate = Number(newConfig.camera.frame_rate);
            if (newConfig.camera.resize_width) currentConfig.camera.resize_width = Number(newConfig.camera.resize_width);
            if (newConfig.camera.resize_height) currentConfig.camera.resize_height = Number(newConfig.camera.resize_height);
            if (newConfig.camera.frame_skip) currentConfig.camera.frame_skip = Number(newConfig.camera.frame_skip);
        }

        if (newConfig.processor) {
            // Update processor config
            if (newConfig.processor.confidence) currentConfig.processor.confidence = Number(newConfig.processor.confidence);
            if (newConfig.processor.iou) currentConfig.processor.iou = Number(newConfig.processor.iou);
            if (newConfig.processor.frame_rate) currentConfig.processor.frame_rate = Number(newConfig.processor.frame_rate);
            if (newConfig.processor.line_position_percent) currentConfig.processor.line_position_percent = Number(newConfig.processor.line_position_percent);

            // Update tracker config if provided
            if (newConfig.processor.tracker) {
                if (newConfig.processor.tracker.track_thresh) currentConfig.processor.tracker.track_thresh = Number(newConfig.processor.tracker.track_thresh);
                if (newConfig.processor.tracker.track_buffer) currentConfig.processor.tracker.track_buffer = Number(newConfig.processor.tracker.track_buffer);
                if (newConfig.processor.tracker.match_thresh) currentConfig.processor.tracker.match_thresh = Number(newConfig.processor.tracker.match_thresh);
                if (newConfig.processor.tracker.aspect_ratio_thresh) currentConfig.processor.tracker.aspect_ratio_thresh = Number(newConfig.processor.tracker.aspect_ratio_thresh);
                if (newConfig.processor.tracker.min_box_area) currentConfig.processor.tracker.min_box_area = Number(newConfig.processor.tracker.min_box_area);
            }
        }

        // Send updated configuration to all clients
        if ((router as any).broadcastConfigUpdate) {
            (router as any).broadcastConfigUpdate();
        }

        res.json({ success: true, config: currentConfig });
    } catch (error) {
        console.error('Error updating configuration:', error);
        res.status(400).json({ success: false, error: 'Invalid configuration data' });
    }
});

// Default configuration for clients
const defaultConfig = {
    camera: {
        frame_rate: 3,
        resize_width: 600,
        resize_height: 400,
        frame_skip: 2
    },
    processor: {
        confidence: 0.4,
        iou: 0.45,
        frame_rate: 15,
        line_position_percent: 50, // Line position as percentage of frame height
        tracker: {
            track_thresh: 0.4,
            track_buffer: 50,
            match_thresh: 0.65,
            aspect_ratio_thresh: 1.6,
            min_box_area: 10
        }
    }
};

// Performance metrics storage
const performanceMetrics = {
    camera: {
        fps: 0,
        bandwidth: 0,
        frame_size: { width: 0, height: 0 },
        avg_capture_time: 0,
        avg_send_time: 0,
        last_update: 0
    },
    processor: {
        fps: 0,
        max_fps_capacity: 0,
        avg_processing_time: 0,
        last_update: 0
    }
};

// Current configuration (starts with defaults)
let currentConfig = JSON.parse(JSON.stringify(defaultConfig));

// Create a WebSocket server for contador salidas
export function setupContadorSalidasWebSocket(server: http.Server) {
    const wss = new WebSocketServer({ noServer: true });

    // Store connected clients
    const cameraClients = new Map();
    const processingClients = new Map();
    const adminClients = new Map();

    // Function to broadcast configuration updates to all clients
    function broadcastConfigUpdate() {
        // Send camera config to camera clients
        cameraClients.forEach((ws: WebSocket) => {
            if (ws.readyState === 1) { // WebSocket.OPEN
                ws.send(JSON.stringify({
                    type: 'command',
                    action: 'set_config',
                    ...currentConfig.camera
                }));
            }
        });

        // Send processor config to processing clients
        processingClients.forEach((ws: WebSocket) => {
            if (ws.readyState === 1) { // WebSocket.OPEN
                ws.send(JSON.stringify({
                    type: 'command',
                    action: 'set_config',
                    ...currentConfig.processor
                }));
            }
        });

        // Send full config to admin clients
        adminClients.forEach((ws: WebSocket) => {
            if (ws.readyState === 1) { // WebSocket.OPEN
                ws.send(JSON.stringify({
                    type: 'config_update',
                    config: currentConfig
                }));
            }
        });
    }

    // Make broadcastConfigUpdate available to the router endpoints
    (router as any).broadcastConfigUpdate = broadcastConfigUpdate;

    // Handle WebSocket connections
    server.on('upgrade', (request, socket, head) => {
        const pathname = request.url || '';

        // Only handle WebSocket connections to /ws/contador-salidas
        if (pathname === '/ws/contador-salidas') {
            // Get authorization from headers
            const auth = request.headers['authorization']?.replace('Bearer ', '') || '';

            // We'll authenticate in the connection handler based on client type
            // This allows both API key and JWT token authentication
            wss.handleUpgrade(request, socket, head, (ws) => {
                // Store the auth token with the connection for later verification
                (ws as any).authToken = auth;
                wss.emit('connection', ws, request);
            });
        }
    });

    // Handle WebSocket messages
    wss.on('connection', (ws) => {
        let clientId: string | null = null;
        let clientType: 'camera' | 'processor' | 'admin' | null = null;
        let adminId: number | null = null;

        // Handle messages from clients
        ws.on('message', async (message: string) => {
            try {
                const data = JSON.parse(message);

                // Handle client identification
                if (data.type === 'client_info') {
                    clientId = data.client_id;
                    clientType = data.client_type;

                    // Authenticate based on client type
                    let authenticated = false;
                    const authToken = (ws as any).authToken;

                    if (clientType === 'camera' || clientType === 'processor') {
                        // Authenticate using API key
                        authenticated = authToken === process.env.MICROSERVICE_APIKEY;

                        if (!authenticated) {
                            console.log(`Authentication failed for ${clientType} client: ${clientId}`);
                            ws.close(1008, 'Authentication failed');
                            return;
                        }
                    } else if (clientType === 'admin' && data.token) {
                        // Authenticate using JWT token
                        console.log(`Authenticating admin client: ${clientId}`);
                        try {
                            const jwtSecret = process.env.JWT_SECRET || '';
                            console.log(`JWT Secret available: ${jwtSecret ? 'Yes' : 'No'}`);

                            const decoded = jwt.verify(data.token, jwtSecret) as any;
                            console.log(`JWT decoded successfully, admin ID: ${decoded?.id}`);

                            if (decoded && decoded.id) {
                                // Verify admin exists in database
                                const admin = await prisma.admin.findUnique({
                                    where: { id: decoded.id }
                                });

                                if (admin) {
                                    authenticated = true;
                                    adminId = admin.id;
                                    console.log(`Admin found in database, ID: ${admin.id}`);
                                } else {
                                    console.log(`Admin not found in database for ID: ${decoded.id}`);
                                }
                            }
                        } catch (error) {
                            console.error('JWT verification error:', error);
                        }

                        if (!authenticated) {
                            console.log(`Authentication failed for admin client: ${clientId}`);
                            ws.close(1008, 'Authentication failed');
                            return;
                        } else {
                            console.log(`Admin client authenticated successfully: ${clientId}, Admin ID: ${adminId}`);
                        }
                    }

                    // Handle authenticated clients
                    if (clientType === 'camera') {
                        console.log(`Camera client connected: ${clientId}`);
                        cameraClients.set(clientId, ws);

                        // Send current configuration to camera client
                        ws.send(JSON.stringify({
                            type: 'command',
                            action: 'set_config',
                            ...currentConfig.camera
                        }));

                        // Notify processing clients about new camera
                        processingClients.forEach((processorWs) => {
                            processorWs.send(JSON.stringify({
                                type: 'camera_connected',
                                client_id: clientId
                            }));
                        });

                        // Notify admin clients about camera connection
                        adminClients.forEach((adminWs) => {
                            adminWs.send(JSON.stringify({
                                type: 'status',
                                message: 'camera connected',
                                connected: true
                            }));
                        });
                    } else if (clientType === 'processor') {
                        console.log(`Processing client connected: ${clientId}`);
                        processingClients.set(clientId, ws);

                        // Send current configuration to processing client
                        ws.send(JSON.stringify({
                            type: 'command',
                            action: 'set_config',
                            ...currentConfig.processor
                        }));

                        // Send list of connected cameras to the processor
                        const cameras = Array.from(cameraClients.keys());
                        ws.send(JSON.stringify({
                            type: 'camera_list',
                            cameras: cameras
                        }));
                    } else if (clientType === 'admin') {
                        console.log(`Admin client connected: ${clientId}, Admin ID: ${adminId}`);
                        adminClients.set(clientId, ws);

                        // Send camera status to new admin
                        const cameraConnected = cameraClients.size > 0;
                        ws.send(JSON.stringify({
                            type: 'status',
                            message: 'camera status',
                            connected: cameraConnected
                        }));

                        // Send current configuration to admin client
                        ws.send(JSON.stringify({
                            type: 'config_update',
                            config: currentConfig
                        }));
                    }

                    // Send acknowledgment
                    ws.send(JSON.stringify({
                        type: 'status',
                        message: 'Connected successfully'
                    }));
                }
                // Handle frame data from camera
                else if (data.type === 'frame' && clientType === 'camera') {
                    console.log(`Received frame from camera ${clientId}, forwarding to ${processingClients.size} processors`);

                    // Relay frame to all processing clients
                    processingClients.forEach((processorWs) => {
                        if (processorWs.readyState === 1) { // WebSocket.OPEN
                            processorWs.send(message);
                        }
                    });

                    // No longer forwarding raw camera frames to admin clients
                    // They will receive processed frames from the processing client
                }
                // Handle processed frame data from processing client
                else if (data.type === 'processed_frame' && clientType === 'processor') {
                    console.log(`Received processed frame from processor ${clientId}, forwarding to ${adminClients.size} admins`);

                    // Relay processed frame to all admin clients
                    if (adminClients.size > 0) {
                        console.log(`Sending processed frame to ${adminClients.size} admin clients, frame length: ${data.frame ? data.frame.length : 0}`);

                        // Verificar que el frame sea válido
                        if (data.frame && typeof data.frame === 'string' && data.frame.length > 0) {
                            // Crear un objeto nuevo para asegurarnos de que el formato es correcto
                            // Usamos type: 'frame' para mantener compatibilidad con el cliente admin
                            const frameMessage = {
                                type: 'frame',
                                client_id: data.client_id,
                                timestamp: data.timestamp || Date.now(),
                                frame: data.frame,
                                processed: true
                            };

                            const frameJson = JSON.stringify(frameMessage);

                            adminClients.forEach((adminWs) => {
                                if (adminWs.readyState === 1) { // WebSocket.OPEN
                                    adminWs.send(frameJson);
                                }
                            });
                        } else {
                            console.log('Invalid processed frame data received, not forwarding to admin clients');
                        }
                    } else {
                        console.log('No admin clients connected to send processed frame to');
                    }
                }
                // Handle commands from processing clients
                else if (data.type === 'command' && clientType === 'processor') {
                    const targetClientId = data.target_client_id;

                    // If target is specified, send only to that client
                    if (targetClientId && cameraClients.has(targetClientId)) {
                        const cameraWs = cameraClients.get(targetClientId);
                        if (cameraWs.readyState === 1) { // WebSocket.OPEN
                            cameraWs.send(message);
                        }
                    }
                    // Otherwise broadcast to all cameras
                    else if (!targetClientId) {
                        cameraClients.forEach((cameraWs) => {
                            if (cameraWs.readyState === 1) { // WebSocket.OPEN
                                cameraWs.send(message);
                            }
                        });
                    }
                }
                // Handle exit notifications from processing clients
                else if (data.type === 'exit_detected' && clientType === 'processor') {
                    // This could trigger a call to the backend API to record the exit
                    console.log(`Exit detected by processor ${clientId}`);

                    // Forward counter data to admin clients
                    if (data.total_exits !== undefined && data.current_count !== undefined) {
                        const counterData = {
                            type: 'counter_data',
                            total_exits: data.total_exits,
                            current_count: data.current_count,
                            timestamp: Date.now()
                        };

                        adminClients.forEach((adminWs) => {
                            if (adminWs.readyState === 1) { // WebSocket.OPEN
                                adminWs.send(JSON.stringify(counterData));
                            }
                        });
                    }
                }
                // Handle counter data from processing clients
                else if (data.type === 'counter_data' && clientType === 'processor') {
                    // Forward counter data to admin clients
                    adminClients.forEach((adminWs) => {
                        if (adminWs.readyState === 1) { // WebSocket.OPEN
                            adminWs.send(message);
                        }
                    });
                }
                // Handle performance metrics from any client
                else if (data.type === 'performance_metrics' && data.metrics) {
                    console.log(`Received performance metrics from client ${clientId}, type: ${clientType}`);
                    console.log('Metrics data:', JSON.stringify(data.metrics).substring(0, 200));

                    // Handle processor metrics
                    if (clientType === 'processor') {
                        console.log('Processing processor metrics...');
                        // Store processor performance metrics
                        performanceMetrics.processor = {
                            fps: data.metrics.current_fps || 0,
                            max_fps_capacity: data.metrics.max_fps_capacity || 0,
                            avg_processing_time: data.metrics.avg_processing_time || 0,
                            last_update: Date.now()
                        };
                        console.log('Updated processor metrics:', JSON.stringify(performanceMetrics.processor));
                    }
                    // Handle camera metrics
                    else if (clientType === 'camera') {
                        console.log('Processing camera metrics...');
                        // Store camera performance metrics
                        performanceMetrics.camera = {
                            fps: data.metrics.current_fps || 0,
                            bandwidth: data.metrics.bandwidth || 0,
                            frame_size: data.metrics.frame_size || { width: 0, height: 0 },
                            avg_capture_time: data.metrics.avg_capture_time || 0,
                            avg_send_time: data.metrics.avg_send_time || 0,
                            last_update: Date.now()
                        };
                        console.log('Updated camera metrics:', JSON.stringify(performanceMetrics.camera));
                    } else {
                        console.log('Ignoring metrics - unknown client type:', clientType);
                    }

                    // Forward metrics to admin clients
                    console.log('Forwarding performance metrics to admin clients...');
                    adminClients.forEach((adminWs) => {
                        if (adminWs.readyState === 1) { // WebSocket.OPEN
                            adminWs.send(JSON.stringify({
                                type: "performance_update",
                                metrics: performanceMetrics
                            }));
                        }
                    });
                }
                // Handle legacy camera metrics (for backward compatibility)
                else if (data.type === 'camera_metrics') {
                    console.log(`Received legacy camera metrics from client ${clientId}, type: ${clientType}`);
                    console.log('Metrics data:', JSON.stringify(data.metrics).substring(0, 200));

                    if (clientType === 'camera' && data.metrics) {
                        console.log('Processing legacy camera metrics...');
                        // Store camera performance metrics
                        performanceMetrics.camera = {
                            fps: data.metrics.current_fps || 0,
                            bandwidth: data.metrics.bandwidth || 0,
                            frame_size: data.metrics.frame_size || { width: 0, height: 0 },
                            avg_capture_time: data.metrics.avg_capture_time || 0,
                            avg_send_time: data.metrics.avg_send_time || 0,
                            last_update: Date.now()
                        };
                        console.log('Updated camera metrics from legacy format:', JSON.stringify(performanceMetrics.camera));
                    }

                    // Forward metrics to admin clients
                    console.log('Forwarding performance metrics to admin clients...');
                    adminClients.forEach((adminWs) => {
                        if (adminWs.readyState === 1) { // WebSocket.OPEN
                            adminWs.send(JSON.stringify({
                                type: "performance_update",
                                metrics: performanceMetrics
                            }));
                        }
                    });
                }
            } catch (error) {
                console.error('Error processing WebSocket message:', error);
            }
        });

        // Handle client disconnection
        ws.on('close', () => {
            if (clientId) {
                if (clientType === 'camera') {
                    console.log(`Camera client disconnected: ${clientId}`);
                    cameraClients.delete(clientId);

                    // Notify processing clients
                    processingClients.forEach((processorWs) => {
                        processorWs.send(JSON.stringify({
                            type: 'camera_disconnected',
                            client_id: clientId
                        }));
                    });

                    // Notify admin clients
                    adminClients.forEach((adminWs) => {
                        adminWs.send(JSON.stringify({
                            type: 'status',
                            message: 'camera disconnected',
                            connected: false
                        }));
                    });
                } else if (clientType === 'processor') {
                    console.log(`Processing client disconnected: ${clientId}`);
                    processingClients.delete(clientId);
                } else if (clientType === 'admin') {
                    console.log(`Admin client disconnected: ${clientId}`);
                    adminClients.delete(clientId);
                }
            }
        });
    });

    console.log('WebSocket server for contador salidas is set up');
    return wss;
}

export default router;
