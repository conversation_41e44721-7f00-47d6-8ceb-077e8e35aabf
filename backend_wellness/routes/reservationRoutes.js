import express from "express";
import { adminCreateReservation, adminDownloadReservations, adminGetAllReservations, adminUpdateReservation, userCreateReservation, userDeleteReservation, userReservations } from "../controllers/ReservationController.js";
import adminAuth from "../middleware/adminAuth.js";
import userAuth from "../middleware/userAuth.js";
const router = express.Router();
router.get("/all/:reservableId", adminAuth, adminGetAllReservations);
router.post("/downloadReservations", adminAuth, adminDownloadReservations);
router.put("/updateReservation", adminAuth, adminUpdateReservation);
router.post("/reserve/admin", adminAuth, adminCreateReservation);
router.post("/reserve/:id", userAuth, userCreateReservation);
router.get("/reserves", userAuth, userReservations);
router.delete("/delete/:id", userAuth, userDeleteReservation);
export default router;
