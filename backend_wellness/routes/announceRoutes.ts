import express from "express";

import adminAuth from "../middleware/adminAuth.js"
import userAuth from "../middleware/userAuth.js";
import { createAnnounce, deleteAnnounce, editAnnounce, getAllAnnounces, getAnnounce, getAnnounces } from "../controllers/AnnounceController.js";

const router = express.Router();

// ==== Ruta Padre /api/announce

// == Crear un anuncio
router.post("/",adminAuth,createAnnounce)

// == Editar un anuncio
router.put("/",adminAuth,editAnnounce)

// == Eliminar un anuncio
router.delete('/:id',adminAuth,deleteAnnounce)
// Obtener todos los anuncios(admin)
router.get("/all",adminAuth,getAllAnnounces)

// Obtener los anuncios (authenticated users)
router.get("/",userAuth,getAnnounces)

// Obtener los anuncios (public access)
router.get("/public",getAnnounces)

// Obtener un anuncio
router.get("/:id",adminAuth,getAnnounce)





export default router;