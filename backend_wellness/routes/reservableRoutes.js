import express from "express";
import { createReservable, deteteReservables, getAllReservables, getReservable, getReservables, updateReservable, displayReservable } from "../controllers/ReservableController.js";
import adminAuth from "../middleware/adminAuth.js";
import userAuth from "../middleware/userAuth.js";
const router = express.Router();
router.post("/createReservable", adminAuth, createReservable);
router.put("/updateReservable", adminAuth, updateReservable);
router.get("/getReservable/:id", userAuth, getReservable);
router.get("/getReservable/get/:id", adminAuth, getReservable);
router.get("/getReservable/public/:id", getReservable);
router.get("/getReservables/:spaceId", userAuth, getReservables);
router.get("/getReservables/public/:spaceId", getReservables);
router.get("/getAllReservables", adminAuth, getAllReservables);
router.get("/displayReservable", adminAuth, displayReservable);
router.delete("/deleteReservable/:id/:all", adminAuth, deteteReservables);
export default router;
