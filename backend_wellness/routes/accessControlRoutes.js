import express from "express";
import alumnoRoutes from "./accessControlRoutes/alumnoRoutes.js";
import dataRoutes from "./accessControlRoutes/dataRoutes.js";
import asistenciaRoutes from "./accessControlRoutes/asistenciaRoutes.js";
import { ClerkExpressRequireAuth } from "@clerk/clerk-sdk-node";
const router = express.Router();
router.use("/alumno", ClerkExpressRequireAuth({}), alumnoRoutes);
router.use("/data", ClerkExpressRequireAuth({}), dataRoutes);
router.use("/asistencia", ClerkExpressRequireAuth({}), asistenciaRoutes);
export default router;
