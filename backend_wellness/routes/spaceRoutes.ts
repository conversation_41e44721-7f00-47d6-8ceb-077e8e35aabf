import dotenv from 'dotenv';
dotenv.config();

import express from "express";

import {
    changeQuota,
     closeSpace,
    createReservable,
    createSpace,
    editSpace,
    getPermanence,
    getQuota,
    getSpaces,  getWellnessAttendances, getWellnessLiveStatus, openSpace,
    setPermanence
} from "../controllers/SpaceController.js"

import adminAuth from "../middleware/adminAuth.js"
import multer from "multer";
import userAuth from "../middleware/userAuth.js";
import microserviceAuth from "../middleware/microserviceAuth.js";
import multiAuth from '../middleware/multiAuth.js';


const router = express.Router();

// Usar multer
const storage = multer.diskStorage({});
const upload = multer({ storage });

// === Ruta padre /api/space

router.post("/changeQuota", adminAuth,changeQuota)
//router.get("/getQuota", getQuota)
router.post("/createSpace",adminAuth, upload.single("image"),createSpace as any)
router.put("/updateSpace",adminAuth, upload.single("image"),editSpace as any)

//router.post("/changeQuotaReservable", changeQuotaReservable)
router.post("/createReservable",adminAuth, createReservable)
//router.get("/getSpaces",userAuth, getUserSpaces)

// Spaces
router.get("/spaces",adminAuth,getSpaces)
router.get("/spaces/public",getSpaces) // Public access to view spaces
router.put("/closeSpace",adminAuth,closeSpace)
router.put("/openSpace/:id",adminAuth,openSpace)

// Live Status
router.get("/currentPermanence",adminAuth,getPermanence)
router.post("/setPermanence",adminAuth,setPermanence)
router.get("/liveStatus",multiAuth,getWellnessLiveStatus)
router.get("/liveStatus/public",getWellnessLiveStatus) // Public access to live status
router.get("/wellnessAttendances",multiAuth,getWellnessAttendances)
router.get("/wellnessAttendances/public",getWellnessAttendances) // Public access to attendance data

// Microservice for pilot app
router.get("/liveStatusApp",(req,res,next)=> microserviceAuth(req,res,next,process.env.LIVESTATUS_APIKEY ?? ""),getWellnessLiveStatus)
router.get("/wellnessAttendancesApp",(req,res,next)=> microserviceAuth(req,res,next,process.env.LIVESTATUS_APIKEY ?? ""),getWellnessAttendances)


  

export default router;
