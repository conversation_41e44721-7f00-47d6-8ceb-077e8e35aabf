// Este grupo de rutas se encarga de manejar la conexión con el microservicio de control de acceso

import express from "express";

import alumnoRoutes from "./accessControlRoutes/alumnoRoutes.js";
import dataRoutes from "./accessControlRoutes/dataRoutes.js";
import asistenciaRoutes from "./accessControlRoutes/asistenciaRoutes.js";
import { ClerkExpressRequireAuth } from "@clerk/clerk-sdk-node";

const router = express.Router();


// Ruta padre "/api/accessControl"

// Agrupamos la rutas que tienen que ver con los alumnos

router.use("/alumno",ClerkExpressRequireAuth({}),alumnoRoutes)

// Agrupamos las rutas que tienen que ver con los datos
router.use("/data",ClerkExpressRequireAuth({}),dataRoutes)

// Agrupamos las rutas que tienen que ver con la asistencia
router.use("/asistencia",ClerkExpressRequireAuth({}),asistenciaRoutes)

export default router;
