import express from "express";
import {
  createMaterial,
  getAllMaterials,
  getAvailableMaterials,
  getMaterialById,
  updateMaterial,
  deleteMaterial,
  uploadCsv
} from "../controllers/MaterialController.js";
import adminAuth from "../middleware/adminAuth.js";
import userAuth from "../middleware/userAuth.js";
import multiAuth from "../middleware/multiAuth.js";
import multer from "multer";
import fs from 'fs';

// Asegúrate de que existe la carpeta temporal para uploads
const tempDir = './temp';
if (!fs.existsSync(tempDir)) {
  fs.mkdirSync(tempDir, { recursive: true });
}

const router = express.Router();

// Configuración de multer para almacenamiento temporal de archivos
const storage = multer.diskStorage({});
const upload = multer({ storage });

// Rutas para administradores
router.post('/', adminAuth, upload.single('image'), createMaterial);
router.post('/uploadCsv', adminAuth, upload.single('file'), uploadCsv);
router.get('/getAll', multiAuth, getAllMaterials);
router.get('/getById/:id', multiAuth, getMaterialById);
router.put('/update/:id', adminAuth, upload.single('image'), updateMaterial);
router.delete('/delete/:id', adminAuth, deleteMaterial);

// Rutas para usuarios
router.get('/available', userAuth, getAvailableMaterials);
router.get('/details/:id', userAuth, getMaterialById);

// Rutas públicas (sin autenticación)
router.get('/public/available', getAvailableMaterials); // Public access to available materials
router.get('/public/all', getAllMaterials); // Public access to all materials

// Rutas accesibles para ambos (admin y usuario)
router.get('/public/:id', multiAuth, getMaterialById);
export default router;