import dotenv from 'dotenv';
dotenv.config();
import express from "express";
import { changeQuota, closeSpace, createReservable, createSpace, editSpace, getPermanence, getSpaces, getWellnessAttendances, getWellnessLiveStatus, openSpace, setPermanence } from "../controllers/SpaceController.js";
import adminAuth from "../middleware/adminAuth.js";
import multer from "multer";
import microserviceAuth from "../middleware/microserviceAuth.js";
import multiAuth from '../middleware/multiAuth.js';
const router = express.Router();
const storage = multer.diskStorage({});
const upload = multer({ storage });
router.post("/changeQuota", adminAuth, changeQuota);
router.post("/createSpace", adminAuth, upload.single("image"), createSpace);
router.put("/updateSpace", adminAuth, upload.single("image"), editSpace);
router.post("/createReservable", adminAuth, createReservable);
router.get("/spaces", adminAuth, getSpaces);
router.get("/spaces/public", getSpaces);
router.put("/closeSpace", adminAuth, closeSpace);
router.put("/openSpace/:id", adminAuth, openSpace);
router.get("/currentPermanence", adminAuth, getPermanence);
router.post("/setPermanence", adminAuth, setPermanence);
router.get("/liveStatus", multiAuth, getWellnessLiveStatus);
router.get("/liveStatus/public", getWellnessLiveStatus);
router.get("/wellnessAttendances", multiAuth, getWellnessAttendances);
router.get("/wellnessAttendances/public", getWellnessAttendances);
router.get("/liveStatusApp", (req, res, next) => microserviceAuth(req, res, next, process.env.LIVESTATUS_APIKEY ?? ""), getWellnessLiveStatus);
router.get("/wellnessAttendancesApp", (req, res, next) => microserviceAuth(req, res, next, process.env.LIVESTATUS_APIKEY ?? ""), getWellnessAttendances);
export default router;
