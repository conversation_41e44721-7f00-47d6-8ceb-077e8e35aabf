import express from "express";
import adminAuth from "../middleware/adminAuth.js";
import userAuth from "../middleware/userAuth.js";
import { createAnnounce, deleteAnnounce, editAnnounce, getAllAnnounces, getAnnounce, getAnnounces } from "../controllers/AnnounceController.js";
const router = express.Router();
router.post("/", adminAuth, createAnnounce);
router.put("/", adminAuth, editAnnounce);
router.delete('/:id', adminAuth, deleteAnnounce);
router.get("/all", adminAuth, getAllAnnounces);
router.get("/", userAuth, getAnnounces);
router.get("/public", getAnnounces);
router.get("/:id", adminAuth, getAnnounce);
export default router;
