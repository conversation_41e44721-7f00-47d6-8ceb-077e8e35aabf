// ==== Archivo de tipados que contendrá la definición de las interfaces de prisma pero con las adaptaciones necesarias

import { admin, reservable, user } from "@prisma/client";


type UserIntExcludes = ""
export interface UserInt extends user {}

type AdminIntExcludes = ""
export interface AdminInt extends admin {}

type ReservableIntExcludes = "init_date" | "end_date"
export interface ReservableInt extends Omit<reservable,ReservableIntExcludes>{
  init_date:string
  end_date:string
}