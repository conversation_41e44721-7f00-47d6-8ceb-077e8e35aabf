
import { AdminInt, UserInt } from "./ModelTypes";
import { Request } from "express";



export interface ReqAdmin extends Request{
    admin ?: AdminInt
    file ?: any
}


export interface ReqUser extends Request {
    user?: UserInt;
}



export interface ReqFileInt extends Request {
    file: {
        fieldname: string;
        originalname: string;
        encoding: string;
        mimetype: string;
        size: number;
        destination: string;
        filename: string;
        path: string;
        buffer: Buffer;
        stream:any
    };
}

// Tipado de los datos de usuario en español
export interface AlumnoInt {
    id: number;
    nombre_completo: string;
    matricula: string;
    desc_genero: string;
    clave_programa: string;
    desc_programa: string | null;
    desc_ejercicio_academico: string | null;
  }

// Tipado de las constantes

export type KeyValueStringObj = {
    [key: string]: string;
}

// === Tipado del csv de alumnos

export interface RawAlumnoCsv {
    Matricula: string;
  'Nombre Completo': string;
  'Desc Genero': string;
  'Clave Programa Academico': string;
  'Desc Programa Academico': string;
  'Desc Periodo Academico': string;
  'Clave Periodo Academico': string;
  'Promedio Prog Ultimo Ejer Cursado': string;
  'Desc Nivel Acad Alumno': string;
  'Clave Escuela Programa': string;
  'Desc Escuela Programa': string;
}

// === Tipados de grafica
export interface GraphInt{
    label:string
    value:number
}


export interface ProyectionGraphInt{
    label:string
    value:number
    projection:boolean
}

