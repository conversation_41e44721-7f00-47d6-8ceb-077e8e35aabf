// Types for Loan Controller requests
import { LoanStatus } from "@prisma/client";

// Create Loan
export interface CreateLoanRT {
  materialId: number | string;
  responsibleId: string;
  studentIds: string[];
  quantity: number | string;
  pickupDate: string;
  returnDate: string;
  pickupTime: string;
  returnTime: string;
}

// Update Loan Status
export interface UpdateLoanStatusRT {
  status: LoanStatus;
  notes?: string;
}

// Get Loans By Status
export interface GetLoansByStatusRT {
  status: LoanStatus;
}

// Get Loans By Responsible
export interface GetLoansByResponsibleRT {
  registration: string;
}

// Validate Student IDs
export interface ValidateStudentIdsRT {
  studentIds: string[];
}
