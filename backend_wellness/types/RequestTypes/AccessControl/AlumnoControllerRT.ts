import { user } from "@prisma/client";


// === AddUser

type AddUserOmit = "id"
export interface addUserRT extends Omit<user,AddUserOmit>{

}

// === getUsers

export interface getUsersRT {
    page?:string
    limit?:string
    query?:string
    filterBy?: "registration" | "name"
}

// == getUser

export interface getUserRT{
    matricula:string
}

// == getUsersLastAttendance

export interface getUsersLastAttendanceRT{
    search:string
}