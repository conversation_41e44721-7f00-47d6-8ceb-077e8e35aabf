

// == getAsistencia

export interface getAsistenciaRT {
    page?:string
    limit?:string
    query?:string
    filterBy?: "registration" | "name"
}

// == MarkAttendance

export interface MarkAttendanceRT {
    matricula:string
}

// == updateAttendance

export interface updateAttendanceRT {
    id_alumno:number
    attendedToday:boolean
}

// == graphs

export interface graphsRT {
    startDate:string
    endDate:string
}