
// ==== Archivo de tipados donde se deben colocar los datos que se esperan recibir una request

import { reservable } from "@prisma/client";
import { ReservableInt } from "../ModelTypes";

// == Create Reservable

export type CreateReservableRTOmits = "id"

export interface CreateReservableRT extends Omit<ReservableInt,CreateReservableRTOmits>{
    repeats:boolean
    repeticion: number[]
    repeticion_fecha:string
}


// == GetReservable
export interface GetReservableRT {
    id:string
}

// == UpdateReservable

export interface UpdateReservableRT extends ReservableInt{
    all:boolean
}

// == Delete Reservable
export interface DeleteReservableRT{
    id:string
    all:"true" | "false"
}