// Tipo para la creación de materiales
export interface CreateMaterialRT {
    name: string;
    quantity: number | string;
    minMatriculas: number | string;
    leadTimeDays: number | string;
    rules: string;
    replacementCost: number | string;
    // La imagen se maneja como archivo, no como parte del json
}

// Tipo para la actualización de materiales (si lo necesitas en el futuro)
export interface UpdateMaterialRT {
    id: number | string;
    name?: string;
    quantity?: number | string;
    minMatriculas?: number | string;
    leadTimeDays?: number | string;
    rules?: string;
    replacementCost?: number | string;
    deleted?: boolean;
}