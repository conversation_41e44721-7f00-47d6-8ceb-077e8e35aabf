import { announce } from "@prisma/client";
import {ChangeDateToString} from "../ConvertionTypes"

// Interfaz de crear un anuncio
type createAnnounceExceptions = "id"
export interface createAnnounceRT extends Omit<ChangeDateToString<announce>,createAnnounceExceptions> {

}

// Interfaz de editar un anuncio
export interface editAnnounceRT extends ChangeDateToString<announce> {

}

// Interfaz de eliminar un anuncio
export interface deleteAnnounceRT {
    id:string
}

// Interfaz de obtener un anuncio
export interface getAnnounceRT {
    id:string
}

