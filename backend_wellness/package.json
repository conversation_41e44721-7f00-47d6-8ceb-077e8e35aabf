{"name": "backend_wellness", "version": "1.0.0", "description": "Backend para el ecosistema del wellness center", "type": "module", "main": "index.js", "scripts": {"test": "text", "dev": "nodemon index.js", "start": "node index.js"}, "keywords": ["Wellness"], "author": "Wellness Developer Team", "license": "ISC", "dependencies": {"@clerk/clerk-sdk-node": "^4.12.22", "@prisma/client": "^5.22.0", "@sentry/node": "^7.60.1", "@types/luxon": "^3.4.2", "@types/multer": "^1.4.10", "archiver": "^6.0.1", "bcrypt": "^5.1.0", "body-parser": "^1.20.2", "cli-progress": "^3.12.0", "cloudinary": "^1.35.0", "connect-timeout": "^1.9.0", "cors": "^2.8.5", "csv-parser": "^3.0.0", "dotenv": "^16.0.3", "excel4node": "^1.8.2", "express": "^4.18.2", "firebase-admin": "^11.9.0", "fs": "^0.0.1-security", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.0", "luxon": "^3.4.4", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.3", "path": "^0.12.7", "stripe": "^12.8.0", "typescript": "^5.6.2", "url": "^0.11.0", "uuid": "^9.0.1", "ws": "^8.18.2"}, "devDependencies": {"@types/bcrypt": "^5.0.0", "@types/cli-progress": "^3.11.0", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jsonwebtoken": "^9.0.1", "@types/moment": "^2.13.0", "@types/morgan": "^1.9.4", "@types/node": "^18.15.10", "@types/nodemailer": "^6.4.8", "@types/uuid": "^9.0.7", "@types/ws": "^8.18.1", "nodemon": "^3.0.1", "prisma": "^5.22.0"}}