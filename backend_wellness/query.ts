import dotenv from "dotenv";
dotenv.config();
import { PrismaClient } from "@prisma/client";
import { DateTime } from "luxon";
import fs from 'fs';

const prisma = new PrismaClient();

const daysInMonth = DateTime.now().daysInMonth;

const results: any[] = [];

for (let day = 1; day <= daysInMonth; day++) {
  const startOfDay = DateTime.now().startOf('month').plus({ days: day - 1 }).startOf('day').toJSDate();
  const endOfDay = DateTime.now().startOf('month').plus({ days: day - 1 }).endOf('day').toJSDate();

  const dailyAttendances = await prisma.attendance.count({
    where: {
      AND: [
        { date: { gte: startOfDay } },
        { date: { lte: endOfDay } },
      ],
    },
  });

  const dailyExits = await prisma.personExit.count({
    where: {
      AND: [
        { date: { gte: startOfDay } },
        { date: { lte: endOfDay } },
      ],
    },
  });

  if (dailyExits === 0) {
    continue;
  }

  const dailyAttendanceRecords = await prisma.attendance.findMany({
    where: {
      AND: [
        { date: { gte: startOfDay } },
        { date: { lte: endOfDay } },
      ],
    },
  });

  const dailyExitRecords = await prisma.personExit.findMany({
    where: {
      AND: [
        { date: { gte: startOfDay } },
        { date: { lte: endOfDay } },
      ],
    },
  });

  const dailyAttendancesPerHour: { [key: string]: number } = {};
  dailyAttendanceRecords.forEach((record) => {
    const hour = DateTime.fromJSDate(record.date).toFormat('HH');
    dailyAttendancesPerHour[hour] = (dailyAttendancesPerHour[hour] || 0) + 1;
  });

  const dailyExitsPerHour: { [key: string]: number } = {};
  dailyExitRecords.forEach((record) => {
    const hour = DateTime.fromJSDate(record.date).toFormat('HH');
    dailyExitsPerHour[hour] = (dailyExitsPerHour[hour] || 0) + 1;
  });

  const dailyPeopleInsidePerHour: { [key: string]: number } = {};
  let dailyCumulativePeople = 0;

  const dailyAttendanceHours = Object.keys(dailyAttendancesPerHour).sort();

  dailyAttendanceHours.forEach((hour) => {
    dailyCumulativePeople += dailyAttendancesPerHour[hour] || 0;
    dailyCumulativePeople -= dailyExitsPerHour[hour] || 0;
    dailyPeopleInsidePerHour[hour] = dailyCumulativePeople;
  });

  const dailyPeopleInsidePerHourFixedStay: { [key: string]: number } = {};
  let dailyCumulativePeopleFixedStay = 0;

  dailyAttendanceRecords.forEach((record) => {
    const entryHour = DateTime.fromJSDate(record.date).toFormat('HH');
    const exitHour = DateTime.fromJSDate(record.date).plus({ hours: 2 }).toFormat('HH');

    for (let hour = parseInt(entryHour); hour < parseInt(exitHour); hour++) {
      const hourStr = hour.toString().padStart(2, '0');
      dailyPeopleInsidePerHourFixedStay[hourStr] = (dailyPeopleInsidePerHourFixedStay[hourStr] || 0) + 1;
    }
  });

  const dailyPercentageDifferences: { [key: string]: number } = {};
  dailyAttendanceHours.forEach((hour) => {
    const original = dailyPeopleInsidePerHour[hour] || 0;
    const fixedStay = dailyPeopleInsidePerHourFixedStay[hour] || 0;
    const difference = ((fixedStay - original) / original) * 100;
    dailyPercentageDifferences[hour] = difference;
  });

  results.push({
    date: DateTime.fromJSDate(startOfDay).toFormat('yyyy-MM-dd'),
    dailyAttendances,
    dailyExits,
    precision: dailyExits*100/dailyAttendances, 
    dailyAttendancesPerHour,
    dailyExitsPerHour,
    dailyPeopleInsidePerHour,
    dailyPeopleInsidePerHourFixedStay,
    dailyPercentageDifferences,
  });
}

fs.writeFileSync('results.json', JSON.stringify(results, null, 2));