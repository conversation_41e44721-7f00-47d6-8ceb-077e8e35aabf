## Como correr el proyecto
# Primero correr el compilador de TypeScript
```shell
tsc --watch
```

# Para luego correr el back completo
```shell
npm run dev
```

# Para migrar lo de prisma (como un commit), para actualizar la base de datos y realizar cambios
```shell
npx prisma migrate dev
```

# Para sincronizar la base de datos, y hacer la ejecución
```shell
npx prisma migrate deploy
```

# Opcional (proveedor)
```shell
npx prisma generate
```