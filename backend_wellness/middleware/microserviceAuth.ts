// Modulo encargado de la autenticación de los microservicios usando usuario y contraseña


import { Request, Response, NextFunction } from "express";


const microserviceAuth = async (
    req: Request,
    res: Response,
    next: NextFunction,
    serverApiKey: string
) => {
    const { apikey} = req.headers;

    // Perform authentication logic here
    // Example: Check if username and password are valid

    if (apikey === serverApiKey) {
        // Authentication successful
        return next();
    }         
    
    return res.status(401).json({ msg: "No autorizado" });

};

export default microserviceAuth;



