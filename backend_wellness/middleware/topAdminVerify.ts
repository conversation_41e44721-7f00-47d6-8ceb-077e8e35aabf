import { NextFunction, Response } from "express";
import { ReqAdmin } from "../types/GeneralTypes";


export const topAdminVerify = async (
    req: ReqAdmin,
    res: Response,
    next: NextFunction
  ) => {
    // Obtenemos el administrador de la request
    const {admin} = req

    // Si no hay devolvemos un error
    if(!admin){
        return res.status(401).json({msg:"Debes tener privilegios se superadmin"})
    }

    // Si lo hay verificamos que sea un superadmin
    if(admin.role !=="SUPERADMIN"){
        return res.status(401).json({msg:"Debes tener privilegios se superadmin"})
    }
    
    return next()
  }