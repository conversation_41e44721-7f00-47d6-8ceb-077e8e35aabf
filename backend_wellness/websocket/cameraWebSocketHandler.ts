import WebSocket from 'ws';
import jwt from 'jsonwebtoken';
import { prisma } from '../index.js';

// Define client types
type ClientType = 'camera' | 'admin' | 'processor';

// Client connection information
interface Client {
  ws: WebSocket;
  id: string;
  type: ClientType;
  authenticated: boolean;
  adminId?: number;
}

// Message types
interface ClientInfoMessage {
  type: 'client_info';
  client_id: string;
  client_type: ClientType;
  token?: string;
  api_key?: string;
}

interface FrameMessage {
  type: 'frame';
  client_id: string;
  timestamp: number;
  frame: string; // base64 encoded image
}

interface StatusMessage {
  type: 'status';
  message: string;
  connected?: boolean;
}

interface CounterDataMessage {
  type: 'counter_data';
  total_exits: number;
  current_count: number;
  timestamp: number;
}

type Message = ClientInfoMessage | FrameMessage | StatusMessage | CounterDataMessage;

class CameraWebSocketHandler {
  private clients: Map<string, Client> = new Map();
  private cameraClients: Map<string, Client> = new Map();
  private adminClients: Map<string, Client> = new Map();
  private processorClients: Map<string, Client> = new Map();
  private wss: WebSocket.Server;
  private jwtSecret: string;
  private apiKey: string;

  constructor(server: any, path: string, jwtSecret: string, apiKey: string) {
    this.wss = new WebSocket.Server({ noServer: true });
    this.jwtSecret = jwtSecret;
    this.apiKey = apiKey;

    // Handle WebSocket upgrade
    server.on('upgrade', (request: any, socket: any, head: any) => {
      const pathname = new URL(request.url, `http://${request.headers.host}`).pathname;
      
      if (pathname === path) {
        this.wss.handleUpgrade(request, socket, head, (ws) => {
          this.wss.emit('connection', ws, request);
        });
      }
    });

    // Handle WebSocket connection
    this.wss.on('connection', this.handleConnection.bind(this));

    console.log(`WebSocket server started on path: ${path}`);
  }

  private handleConnection(ws: WebSocket): void {
    console.log('New WebSocket connection');
    
    // Generate temporary client ID
    const tempId = `temp-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
    
    // Add client to map with unauthenticated status
    this.clients.set(tempId, {
      ws,
      id: tempId,
      type: 'admin', // Default type, will be updated after authentication
      authenticated: false
    });

    // Set up event handlers
    ws.on('message', (message: WebSocket.Data) => this.handleMessage(tempId, message));
    ws.on('close', () => this.handleClose(tempId));
    ws.on('error', (error) => this.handleError(tempId, error));

    // Set authentication timeout
    setTimeout(() => {
      const client = this.clients.get(tempId);
      if (client && !client.authenticated) {
        console.log(`Client ${tempId} authentication timeout`);
        ws.close(1008, 'Authentication timeout');
        this.clients.delete(tempId);
      }
    }, 10000); // 10 seconds timeout for authentication
  }

  private async handleMessage(clientId: string, message: WebSocket.Data): Promise<void> {
    const client = this.clients.get(clientId);
    if (!client) return;

    try {
      const data = JSON.parse(message.toString()) as Message;

      // Handle client identification and authentication
      if (data.type === 'client_info') {
        await this.handleClientInfo(clientId, data);
      } 
      // Only process other message types if client is authenticated
      else if (client.authenticated) {
        switch (data.type) {
          case 'frame':
            this.handleFrame(client, data);
            break;
          case 'counter_data':
            this.handleCounterData(client, data);
            break;
          case 'status':
            this.handleStatus(client, data);
            break;
          default:
            console.log(`Unknown message type: ${(data as any).type}`);
        }
      }
    } catch (error) {
      console.error('Error handling message:', error);
    }
  }

  private async handleClientInfo(clientId: string, data: ClientInfoMessage): Promise<void> {
    const client = this.clients.get(clientId);
    if (!client) return;

    const { client_id, client_type, token, api_key } = data;

    // Authenticate based on client type
    let authenticated = false;
    let adminId: number | undefined;

    if (client_type === 'camera' || client_type === 'processor') {
      // Authenticate using API key
      authenticated = api_key === this.apiKey;
    } else if (client_type === 'admin' && token) {
      // Authenticate using JWT token
      try {
        const decoded = jwt.verify(token, this.jwtSecret) as any;
        if (decoded && decoded.id) {
          // Verify admin exists in database
          const admin = await prisma.admin.findUnique({
            where: { id: decoded.id }
          });

          if (admin) {
            authenticated = true;
            adminId = admin.id;
          }
        }
      } catch (error) {
        console.error('JWT verification error:', error);
      }
    }

    if (authenticated) {
      // Update client information
      const updatedClient: Client = {
        ...client,
        id: client_id,
        type: client_type,
        authenticated: true,
        adminId
      };

      // Remove temporary client entry
      this.clients.delete(clientId);
      
      // Add to appropriate client maps
      this.clients.set(client_id, updatedClient);
      
      if (client_type === 'camera') {
        this.cameraClients.set(client_id, updatedClient);
        
        // Notify admin clients about camera connection
        this.broadcastToAdmins({
          type: 'status',
          message: 'camera connected',
          connected: true
        });
      } else if (client_type === 'admin') {
        this.adminClients.set(client_id, updatedClient);
        
        // Send camera status to new admin
        const cameraConnected = this.cameraClients.size > 0;
        client.ws.send(JSON.stringify({
          type: 'status',
          message: 'camera status',
          connected: cameraConnected
        }));
      } else if (client_type === 'processor') {
        this.processorClients.set(client_id, updatedClient);
      }

      // Send acknowledgment
      client.ws.send(JSON.stringify({
        type: 'status',
        message: 'authenticated successfully'
      }));

      console.log(`Client ${client_id} authenticated as ${client_type}`);
    } else {
      // Authentication failed
      client.ws.close(1008, 'Authentication failed');
      this.clients.delete(clientId);
      console.log(`Client ${client_id} authentication failed`);
    }
  }

  private handleFrame(client: Client, data: FrameMessage): void {
    // Forward frame to admin clients
    this.broadcastToAdmins(data);
  }

  private handleCounterData(client: Client, data: CounterDataMessage): void {
    // Forward counter data to admin clients
    this.broadcastToAdmins(data);
  }

  private handleStatus(client: Client, data: StatusMessage): void {
    // Forward status to admin clients
    this.broadcastToAdmins(data);
  }

  private handleClose(clientId: string): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    console.log(`Client ${client.id} disconnected`);

    // Remove from client maps
    this.clients.delete(clientId);
    
    if (client.type === 'camera') {
      this.cameraClients.delete(client.id);
      
      // Notify admin clients about camera disconnection
      this.broadcastToAdmins({
        type: 'status',
        message: 'camera disconnected',
        connected: false
      });
    } else if (client.type === 'admin') {
      this.adminClients.delete(client.id);
    } else if (client.type === 'processor') {
      this.processorClients.delete(client.id);
    }
  }

  private handleError(clientId: string, error: Error): void {
    console.error(`WebSocket error for client ${clientId}:`, error);
  }

  private broadcastToAdmins(data: any): void {
    this.adminClients.forEach((client) => {
      if (client.ws.readyState === WebSocket.OPEN) {
        client.ws.send(JSON.stringify(data));
      }
    });
  }
}

export default CameraWebSocketHandler;
