import dotenv from "dotenv";
dotenv.config();

import express from "express";
import cors from "cors";
import morgan from "morgan";
import http from "http";

import { PrismaClient } from "@prisma/client";

import adminRoutes from "./routes/adminRoutes.js";
import userRoutes from "./routes/userRoutes.js";
import spaceRoutes from "./routes/spaceRoutes.js";
import reservableRoutes from "./routes/reservableRoutes.js";
import reservationRoutes from "./routes/reservationRoutes.js";
import announceRoutes from "./routes/announceRoutes.js";
import graphsRoutes from "./routes/graphsRoutes.js";
import personCounterRoutes from "./routes/personCounterRoutes.js";
import accessControlRoutes from "./routes/accessControlRoutes.js";
import { ClerkExpressRequireAuth } from '@clerk/clerk-sdk-node';

/* I add the material and loan routes */
import materialRoutes from "./routes/materialRoutes.js";
import loanRoutes from "./routes/loanRoutes.js";

/* Import contador salidas WebSocket routes */
import contadorSalidasRoutes, { setupContadorSalidasWebSocket } from "./routes/contadorSalidasRoutes.js";

const app = express();
const server = http.createServer(app);
//
app.use(morgan("dev"))

export const prisma = new PrismaClient();

const whitelist = process.env.WHITELISTED_DOMAINS?.split(",") ?? []

let corsOptions: any;

if (process.env.NODE_ENV === "development") {
  // Desarrollo
  corsOptions = {
    origin: true, // Permitir cualquier origen
    methods: ["GET", "POST", "PUT", "DELETE"], // Permitir todos los métodos HTTP
  };
} else {
  // Produccion

  corsOptions = {
    origin: function (origin: string, callback: Function) {
      console.log(whitelist, origin);
      if (whitelist.includes(origin) || !origin) {
        // Puede consultar la API
        callback(null, true);
      } else {
        // No esta permitido su request
        console.log(origin);
        callback(new Error("Error de Cors"));
      }
    },
  };
}
app.use(cors(corsOptions));

app.use(express.json());

// Routing

// app.use("ruta",archivoDeRuta)
// TODAS LAS RUTAS
app.use("/api/admin",adminRoutes)
app.use("/api/user",userRoutes)
app.use("/api/space",spaceRoutes)
app.use("/api/announce",announceRoutes)

app.use("/api/reservable", reservableRoutes)
app.use("/api/reservation", reservationRoutes);
app.use("/api/graphs", graphsRoutes);
app.use("/api/personCounter", personCounterRoutes);

app.use("/api/material", materialRoutes);
app.use("/api/loan", loanRoutes);

// Control de acceso
app.use("/api/accessControl", accessControlRoutes)

// Contador salidas routes
app.use("/api/contador-salidas", contadorSalidasRoutes);

const PORT = process.env.PORT || 4000;

// Set up WebSocket server for contador salidas
setupContadorSalidasWebSocket(server);

// Use HTTP server instead of Express server
server.listen(PORT, () => {
  console.log(`Servidor Corriendo en ${PORT}`);
});

["SIGINT", "SIGTERM"].forEach((signal) => {
  process.on(signal, async () => {
    await prisma.$disconnect();
    process.exit(1);
  });
});
