import { Request, Response } from "express";
import { prisma } from "../../index.js";
import { handleServerError } from "../../helpers/handleServerError.js";

import { addUserRT, getUserRT, getUsersLastAttendanceRT, getUsersRT } from "../../types/RequestTypes/AccessControl/AlumnoControllerRT.js";
import moment from "moment";
import { user } from "@prisma/client";

// Endpoint para agregar un usuario manualmente
export const addUser = async (req: Request, res: Response) => {
  const body = req.body as addUserRT;

  try {

    // Verificamos que no exista un usuario con la misma matricula
    const user = await prisma.user.findUnique({
      where:{
        registration:body.registration.toLowerCase()
      }
    })

    if(user){
      return res.status(400).json({msg:"Ya existe un usuario con esa matricula"})
    }

    const newAlumno = await prisma.user.create({
      data: body,
    });

    return res.json(newAlumno );
  } catch (error: any) {
    return handleServerError(error, "Usuario", res);
  }
};

export const getUser = async (req: Request, res: Response) => {
  const { matricula } = req.params as unknown as getUserRT;

  try {
    const user = await prisma.user.findFirstOrThrow({
      where: {
        registration: matricula,
        deleted: false // Solo incluimos los usuarios no eliminados
      },
    });

    return res.json({status:"success",data:user});
  } catch (error: any) {
    return handleServerError(error, "Asistencia", res);
  }
};
export const getUsers = async (req: Request, res: Response) => {

  const query = req.query as unknown as getUsersRT;

  const limit = query.limit ? +query.limit : 100;
  const page = query.page ? +query.page : 0;
  const filterBy = query.filterBy ?? "name"
  const queryValue = query.query ?? ""

  const offset = (page ) * limit;

  try {
    // Obtenemos el total de usuarios que cumplan la condicion (excluyendo los eliminados)
    const total = await prisma.user.count({
      where: {
        [filterBy]: {
          contains: queryValue,
          mode: "insensitive",
        },
        deleted: false, // Solo contamos los usuarios no eliminados
      },
    });

    // Calculamos el numero de paginas
    const totalPages = Math.ceil(total / limit);

    // Obtenemos los usuarios con el limite y el offset (excluyendo los eliminados)
    const users = await prisma.user.findMany({
      take: limit,
      skip: offset,
      where: {
        [filterBy]: {
          contains: queryValue,
          mode: "insensitive",
        },
        deleted: false, // Solo incluimos los usuarios no eliminados
      },

    });

    return res.json({users, totalPages});
  } catch (error: any) {
    return handleServerError(error, "Asistencia", res);
  }
};

export const getUsersLastAttendance = async (req: Request, res: Response) => {

  const {search} = req.params as unknown as getUsersLastAttendanceRT;

  // Limpiamos la busqueda del %20 para que no afecte la busqueda
  const cleanSearch = search.replace(/%20/g, " ");

  try {
    // Obtenemos los usuarios con su ultima asistencia de hoy

    // Creamos le facha de hoy
    const initial_date = moment().tz("America/Monterrey").startOf("day").toDate();


    const users = await prisma.user.findMany({
      where:{
        OR:[
          {
            name:{
              contains:cleanSearch,
              mode:"insensitive"
            }
          }
        ],
        deleted: false, // Solo incluimos los usuarios no eliminados
      },
      include: {
        attendances: {
          where: {
            date: {
              gte: initial_date,
              lte: new Date()
            },
          },
          orderBy: {
            date: "desc",
          },
          select: {
            date: true,
          },
          take: 1,
        },
      },
      take:10
    });

    // Devolvemos el mapeo para que cumpla el formato

    // Obtenemos la fecha de hoy en formato ISO
    const today = new Date().toISOString().split("T")[0];


    const data = users.map((user) => {

      // Devolvemos la ultima asistencia
      const lastAttendance = user.attendances.length > 0 ? user.attendances[0].date.toISOString() : null;
      const attendedToday = user.attendances.length > 0 ? user.attendances[0].date.toISOString().split("T")[0] === today : false

      return ({
        ...user,
        lastAttendance,
        attendedToday
      })})

    return res.json(data);
  } catch (error: any) {
    return handleServerError(error, "Asistencia", res);
  }
};
export const getUserByIdController = async (req: Request, res: Response) => {
  const { id } = req.params;

  try {
    const user = await prisma.user.findFirstOrThrow({
      where: {
        id: +id,
        deleted: false // Solo incluimos los usuarios no eliminados
      },
    });

    return res.json(user);
  } catch (error: any) {
    return handleServerError(error, "Usuario", res);
  }
}

export const updateUserController = async (req: Request, res: Response) => {


  const {id,...body} = req.body as user;

  try {
    const user = await prisma.user.update({
      where: {
        id: id
      },
      data: body
    });

    return res.json(user);
  } catch (error: any) {
    return handleServerError(error, "Usuario", res);
  }
}