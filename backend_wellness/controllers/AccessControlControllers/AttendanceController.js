import { handleServerError } from "../../helpers/handleServerError.js";
import moment from "moment-timezone";
import { prisma } from "../../index.js";
import { graphBigIntToInt } from "../../helpers/helpers.js";
import { DateTime } from "luxon";
export const getAsistencia = async (req, res) => {
    const query = req.query;
    const limit = query.limit ? +query.limit : 100;
    const page = query.page ? +query.page : 0;
    const filterBy = query.filterBy ?? "name";
    const queryValue = query.query ?? "";
    const offset = page * limit;
    try {
        const total = await prisma.attendance.count({
            where: {
                user: {
                    [filterBy]: {
                        contains: queryValue,
                        mode: "insensitive",
                    },
                    deleted: false,
                },
            },
        });
        const totalPages = Math.ceil(total / limit);
        const asistencias = await prisma.attendance.findMany({
            take: limit,
            skip: offset,
            orderBy: {
                date: "desc",
            },
            include: {
                user: true,
            },
            where: {
                user: {
                    [filterBy]: {
                        contains: queryValue,
                        mode: "insensitive",
                    },
                    deleted: false,
                },
            },
        });
        const flattenedAsistencias = asistencias.map((asistencia) => ({
            ...asistencia,
            ...asistencia.user,
            user: undefined,
        }));
        return res.json({ attendances: flattenedAsistencias, totalPages });
    }
    catch (error) {
        return handleServerError(error, "Asistencia", res);
    }
};
const subtractTimeFromDate = (date, minutes) => {
    date.setMinutes(date.getMinutes() - minutes);
    return date;
};
export const getAsistenciaToday = async (_req, res) => {
    try {
        const horaActual = parseInt(new Date()
            .toLocaleTimeString("es-MX", {
            timeZone: "America/Mexico_City",
        })
            .split(":")[0]);
        const asistencias = await prisma.attendance.findMany({
            where: {
                date: {
                    gte: subtractTimeFromDate(new Date(), horaActual),
                    lte: new Date(),
                },
            },
            include: {
                user: {
                    select: {
                        gender: true,
                        program_key: true,
                    },
                },
            },
        });
        const asistenciasTransformed = asistencias.map(({ user, ...rest }) => ({
            ...rest,
            gender: user?.gender,
            program_key: user?.program_key,
        }));
        return res.json({ status: "success", data: asistenciasTransformed });
    }
    catch (error) {
        return handleServerError(error, "Asistencia", res);
    }
};
export const getAsistenciaCount = async (_req, res) => {
    try {
        const initial_date = moment().tz("America/Monterrey").startOf("day").toDate();
        const attendances = await prisma.attendance.count({
            where: {
                date: {
                    gte: initial_date,
                    lte: new Date(),
                },
            },
        });
        return res.json(attendances);
    }
    catch (error) {
        return handleServerError(error, "Asistencia", res);
    }
};
export const markAttendance = async (req, res) => {
    const { matricula } = req.body;
    try {
        const user = await prisma.user.findFirst({
            where: {
                registration: {
                    equals: matricula,
                    mode: "insensitive",
                },
                deleted: false,
            },
        });
        if (!user)
            return res.status(404).json({ msg: "No se encontró al usuario" });
        const startOfDay = DateTime.now().setZone("America/Monterrey").startOf("day").toJSDate();
        const endOfDay = DateTime.now().setZone("America/Monterrey").endOf("day").toJSDate();
        const lastAttendance = await prisma.attendance.findFirst({
            where: {
                userId: user.id,
                date: {
                    gte: startOfDay,
                    lte: endOfDay,
                },
            },
        });
        if (lastAttendance)
            return res
                .status(401)
                .json({
                msg: `Ya se registró una asistencia hoy a las ${DateTime.fromJSDate(lastAttendance.date).setZone("America/Monterrey").toFormat("hh:mm a")}`,
            });
        const newAttendance = await prisma.attendance.create({
            data: {
                userId: user.id,
                date: new Date(),
            },
        });
        return res.json({
            ...user,
            lastAttendance: newAttendance.date,
            attendedToday: true,
        });
    }
    catch (error) {
        return handleServerError(error, "Asistencia", res);
    }
};
export const updateAttendance = async (req, res) => {
    const { id_alumno, attendedToday } = req.body;
    try {
        const user = await prisma.user.findFirst({
            where: {
                id: id_alumno,
                deleted: false,
            },
        });
        if (!user)
            return res.status(404).json({ msg: "No se encontró al usuario" });
        if (attendedToday) {
            const attendance = await prisma.attendance.create({
                data: {
                    userId: id_alumno,
                    date: new Date(),
                },
            });
            return res.json({
                ...user,
                lastAttendance: attendance.date,
                attendedToday: true,
            });
        }
        const lastAttendance = await prisma.attendance.findFirst({
            where: {
                userId: id_alumno,
            },
            orderBy: {
                date: "desc",
            },
        });
        if (lastAttendance) {
            await prisma.attendance.delete({
                where: {
                    id: lastAttendance.id,
                },
            });
        }
        return res.json({
            ...user,
            lastAttendance: null,
            attendedToday: false,
        });
    }
    catch (error) {
        return handleServerError(error, "Asistencia", res);
    }
};
export const graphs = async (req, res) => {
    const { startDate, endDate } = req.params;
    try {
        const start = new Date(startDate);
        const end = new Date(endDate);
        const where = {
            date: {
                gte: start,
                lte: end,
            },
        };
        const assistanceByHour = await prisma.$queryRaw `
      SELECT
        EXTRACT(HOUR FROM DATE_TRUNC('hour', date AT TIME ZONE 'UTC' AT TIME ZONE 'America/Monterrey')) as label,
        COUNT(*) as value
      FROM
        attendance
      WHERE
        date AT TIME ZONE 'UTC' AT TIME ZONE 'America/Monterrey' >= ${start.toISOString()}::timestamptz
        AND date AT TIME ZONE 'UTC' AT TIME ZONE 'America/Monterrey' < ${end.toISOString()}::timestamptz
      GROUP BY
        label
      ORDER BY
        label;
    `;
        const assistenceByHourTransformed = graphBigIntToInt(assistanceByHour);
        const labels = assistenceByHourTransformed.map((item) => item.label);
        const values = assistenceByHourTransformed.map((item) => item.value);
        const asistencias = await prisma.attendance.count({
            where,
        });
        const alumnos = (await prisma.user.count({
            where: {
                attendances: {
                    some: where,
                },
            }
        }));
        const genderCounts = await prisma.user.groupBy({
            where: {
                attendances: {
                    some: where,
                },
            },
            by: ["gender"],
            _count: {
                id: true,
            },
        });
        const genderStats = genderCounts.map((item) => ({
            gender: item.gender,
            count: item._count.id,
        }));
        console.log(genderStats);
        const hombres = genderStats.reduce((acc, item) => {
            if (item.gender === "Masculino")
                return acc + item.count;
            return acc;
        }, 0);
        const mujeres = genderStats.reduce((acc, item) => {
            if (item.gender === "Femenino")
                return acc + item.count;
            return acc;
        }, 0);
        return res.json({
            hours: { labels, values },
            asistencias,
            alumnos,
            genderStats: { hombres, mujeres },
        });
    }
    catch (error) {
        return handleServerError(error, "Asistencia", res);
    }
};
