import { handleServerError } from "../../helpers/handleServerError.js";
import { Parser } from "json2csv";
import xl from "excel4node";
import { prisma } from "../../index.js";
import { quitarAcentos, } from "../../helpers/helpers.js";
import { transformRow } from "../../functions/csvUploadFunctions.js";
import fs from "fs";
import csvParser from "csv-parser";
import { DateTime } from "luxon";
import { randomUUID } from "crypto";
const transformAsistencia = (asistencia) => {
    const dateString = DateTime.fromJSDate(asistencia.date).setZone("America/Monterrey").toFormat("yyyy-MM-dd HH:mm:ss");
    const date = dateString.split(" ")[0];
    const time = dateString.split(" ")[1];
    return {
        id: asistencia.id,
        matricula: asistencia?.user?.registration ?? "Alumno Eliminado",
        "nombre completo": quitarAcentos(asistencia?.user?.name ?? "Alumno Eliminado"),
        fecha: date,
        hora: time,
        observaciones: asistencia.observation,
    };
};
export const exportUniqueAttendancesDataCsv = async (req, res) => {
    const { startDate, endDate } = req.body;
    try {
        const start = DateTime.fromISO(startDate).setZone("America/Monterrey").startOf("day").toJSDate();
        const end = DateTime.fromISO(endDate).setZone("America/Monterrey").endOf("day").toJSDate();
        const users = await prisma.user.findMany({
            where: {
                attendances: {
                    some: {
                        date: {
                            gte: start,
                            lte: end
                        }
                    }
                }
            },
            include: {
                attendances: {
                    where: {
                        date: {
                            gte: start,
                            lte: end
                        }
                    }
                }
            }
        });
        const mappedUsers = users.map((user) => ({
            matricula: user?.registration ?? "Alumno Eliminado",
            "nombre completo": quitarAcentos(user?.name ?? "Alumno Eliminado"),
            asistencias: user.attendances.length
        }));
        mappedUsers.sort((a, b) => b.asistencias - a.asistencias);
        if (mappedUsers.length > 0) {
            const asistenciasCsv = new Parser({ encoding: "utf-8" }).parse(mappedUsers);
            res.setHeader("Content-Disposition", "attachment; filename=asistencias.csv");
            res.setHeader("Content-Type", "text/csv ; charset=UTF-8");
            if (false) {
                await prisma.attendance.deleteMany();
            }
            return res.send(asistenciasCsv);
        }
        return res.status(500).json({
            error: "No hay registros en la tabla Asistencia para exportar.",
        });
    }
    catch (error) {
        return handleServerError(error, "Datos", res);
    }
};
export const exportAsistenciaDataCsv = async (req, res) => {
    const { startDate, endDate } = req.body;
    try {
        const start = DateTime.fromISO(startDate).setZone("America/Monterrey").startOf("day").toJSDate();
        const end = DateTime.fromISO(endDate).setZone("America/Monterrey").endOf("day").toJSDate();
        const asistenciasConMatricula = (await prisma.attendance.findMany({
            where: {
                date: {
                    gte: start,
                    lte: end,
                },
            },
            include: {
                user: {
                    select: {
                        registration: true,
                        name: true,
                    },
                },
            },
            orderBy: {
                date: "asc"
            }
        }));
        const asistenciasTransformadas = asistenciasConMatricula.map(transformAsistencia);
        if (asistenciasTransformadas.length > 0) {
            const asistenciasCsv = new Parser({ encoding: "utf-8" }).parse(asistenciasTransformadas);
            res.setHeader("Content-Disposition", "attachment; filename=asistencias.csv");
            res.setHeader("Content-Type", "text/csv ; charset=UTF-8");
            if (false) {
                await prisma.attendance.deleteMany();
            }
            return res.send(asistenciasCsv);
        }
        return res.status(500).json({
            error: "No hay registros en la tabla Asistencia para exportar.",
        });
    }
    catch (error) {
        return handleServerError(error, "Datos", res);
    }
};
export const exportAsistenciaDataExcel = async (req, res) => {
    const { startDate, endDate } = req.body;
    const start = DateTime.fromISO(startDate).setZone("America/Monterrey").startOf("day").toJSDate();
    const end = DateTime.fromISO(endDate).setZone("America/Monterrey").endOf("day").toJSDate();
    try {
        const asistenciasConMatricula = (await prisma.attendance.findMany({
            where: {
                date: {
                    gte: start,
                    lte: end,
                },
            },
            include: {
                user: {
                    select: {
                        registration: true,
                        name: true,
                    },
                },
            },
            orderBy: {
                date: "asc"
            }
        }));
        const asistenciasTransformadas = asistenciasConMatricula.map(transformAsistencia);
        if (asistenciasTransformadas.length > 0) {
            const wb = new xl.Workbook();
            const ws = wb.addWorksheet("Asistencia Alumnos");
            const headingColumnNames = [
                "ID",
                "Matrícula",
                "Nombre completo",
                "Fecha",
                "Hora",
                "Observaciones",
            ];
            let headingColumnIndex = 1;
            headingColumnNames.forEach((heading) => {
                ws.cell(1, headingColumnIndex++).string(heading);
            });
            let rowIndex = 2;
            asistenciasTransformadas.forEach((record) => {
                let columnIndex = 1;
                Object.keys(record).forEach((columnName) => {
                    ws.cell(rowIndex, columnIndex++).string(String(record[columnName]));
                });
                rowIndex++;
            });
            res.setHeader("Content-Disposition", "attachment; filename=asistencias.xlsx");
            res.setHeader("Content-Type", "application/vnd.ms-excel");
            if (false) {
                await prisma.attendance.deleteMany();
            }
            return wb.write("asistencias.xlsx", res);
        }
        return res.status(404).json({
            error: "No hay registros en la tabla Asistencia para exportar.",
        });
    }
    catch (error) {
        console.error("Error al exportar y/o eliminar registros de la tabla Asistencia:", error);
        res.status(500).json({ error: "Error al procesar la solicitud" });
    }
};
export const exportDataCsv = async (req, res) => {
    const { deleteData } = req.body;
    try {
        const users = await prisma.user.findMany({
            where: {
                deleted: false
            }
        });
        const usersTransformed = users.map((user) => ({
            id: user.id,
            nombre_completo: quitarAcentos(user.name),
            matricula: user.registration,
            clave_programa: quitarAcentos(user.program_key ?? ""),
            desc_genero: user.gender,
            desc_ejercicio_academico: user.academic_exercise,
            desc_programa: quitarAcentos(user.program ?? ""),
        }));
        if (usersTransformed.length > 0) {
            const usersCsv = new Parser().parse(usersTransformed);
            res.setHeader("Content-Disposition", "attachment; filename=users.csv");
            res.setHeader("Content-Type", "text/csv");
            if (deleteData) {
                await prisma.user.updateMany({
                    where: {
                        deleted: false
                    },
                    data: {
                        deleted: true
                    }
                });
            }
            return res.send(usersCsv);
        }
        return res
            .status(500)
            .json({ error: "No records in the User table to export." });
    }
    catch (error) {
        return handleServerError(error, "Datos", res);
    }
};
export const updateStudentsCsv = async (req, res) => {
    try {
        if (req.file.mimetype !== "text/csv") {
            return res.status(400).json({ error: "El archivo debe ser un csv" });
        }
        const mode = req.body.mode || 'replace';
        if (mode !== 'replace' && mode !== 'add') {
            return res.status(400).json({ error: "El modo debe ser 'replace' o 'add'" });
        }
        let alumnos = [];
        console.log("Inicio - Modo:", mode);
        const stream = fs.createReadStream(req.file.path)
            .pipe(csvParser())
            .on("data", (rawRow) => {
            Object.keys(rawRow).forEach((key) => {
                const normalizedKey = key.trim().replace(/[^a-zA-Z0-9]/g, '');
                rawRow[normalizedKey] = rawRow[key];
                if (key !== normalizedKey) {
                    delete rawRow[key];
                }
            });
            const row = {
                matricula: rawRow.Matricula,
                nombre_completo: rawRow["NombreCompleto"],
                desc_genero: rawRow["DescGenero"],
                clave_programa: rawRow["ClaveProgramaAcademico"],
                desc_programa: rawRow["DescProgramaAcademico"],
                desc_ejercicio_academico: rawRow["DescPeriodoAcademico"],
                desc_nivel_acad_alumno: rawRow["DescNivelAcademicoAlumno"]
            };
            try {
                const alumno = transformRow(row);
                if (!alumno)
                    return;
                alumnos.push(alumno);
            }
            catch (error) {
                console.log("Error al transformar la fila", error.message);
                console.log(rawRow);
                stream.destroy(error);
            }
        })
            .on("error", (error) => {
            return res.status(500).json({ msg: error.message });
        })
            .on("end", async () => {
            fs.unlinkSync(req.file.path);
            const session = mode === 'replace' ? randomUUID() : null;
            const alumnosDb = alumnos.map((alumno) => ({
                name: alumno.nombre_completo,
                academic_exercise: alumno.desc_ejercicio_academico,
                gender: alumno.desc_genero,
                registration: alumno.matricula,
                program: alumno.desc_programa,
                program_key: alumno.clave_programa,
                session_id: session,
                deleted: false
            }));
            const batchSize = 100;
            for (let i = 0; i < alumnosDb.length; i += batchSize) {
                const batch = alumnosDb.slice(i, i + batchSize);
                await prisma.$transaction(async (tx) => {
                    const upsertPromises = batch.map((alumno) => tx.user.upsert({
                        where: { registration: alumno.registration },
                        create: alumno,
                        update: alumno,
                    }));
                    await Promise.all(upsertPromises);
                });
            }
            if (mode === 'replace' && session) {
                await prisma.user.updateMany({
                    where: {
                        OR: [
                            {
                                session_id: {
                                    not: session
                                }
                            },
                            {
                                session_id: null
                            }
                        ],
                        deleted: false
                    },
                    data: {
                        deleted: true
                    }
                });
            }
            await prisma.$queryRaw `SELECT setval((SELECT pg_get_serial_sequence('user', 'id')), (SELECT MAX(id) FROM public."user") + 1);`;
            const message = mode === 'replace'
                ? "Archivo procesado correctamente. Los estudiantes existentes han sido reemplazados."
                : "Archivo procesado correctamente. Se han agregado nuevos estudiantes sin afectar los existentes.";
            return res.json({ msg: message });
        });
        return;
    }
    catch (error) {
        return handleServerError(error, "Datos", res);
    }
};
