import { Request, Response } from "express";
import { handleServerError } from "../../helpers/handleServerError.js";

import { exportAsistenciaDataRT, exportDataCsvRT } from "../../types/RequestTypes/AccessControl/DataControllerRT";

import { Parser } from "json2csv";
import xl from "excel4node";
import archiver from "archiver";

import { prisma } from "../../index.js";
import { attendance, user } from "@prisma/client";
import { ChangeDateToString } from "../../types/ConvertionTypes";
import {
  dateFormatter,
  hourFormatter,
  quitarAcentos,
  toMonterreyTimeZone,
} from "../../helpers/helpers.js";
import { Readable } from "stream";
import {
  AlumnoInt,
  RawAlumnoCsv,
  ReqFileInt,
} from "../../types/GeneralTypes.js";
import { AlumnoCsv, transformRow } from "../../functions/csvUploadFunctions.js";

import fs from "fs";
import csvParser from "csv-parser";
import { DateTime } from "luxon";
import { randomUUID } from "crypto";

// Creamos el tipado espacial para la funcion

type asistenciaPopulate = attendance & {
  user: user;
};

// Función que transforma los datos al formato deseado
const transformAsistencia = (asistencia: asistenciaPopulate) => {

  const dateString = DateTime.fromJSDate(asistencia.date).setZone("America/Monterrey").toFormat("yyyy-MM-dd HH:mm:ss");
  const date = dateString.split(" ")[0];
  const time = dateString.split(" ")[1];
  return {
    id: asistencia.id,
    matricula: asistencia?.user?.registration ?? "Alumno Eliminado",
    "nombre completo": quitarAcentos(asistencia?.user?.name ?? "Alumno Eliminado"),
    fecha: date,
    hora:time,
    observaciones: asistencia.observation,
  };
};

export const exportUniqueAttendancesDataCsv = async (req: Request, res: Response) => {
  const { startDate,endDate } = req.body as exportAsistenciaDataRT;

  try {

    const start = DateTime.fromISO(startDate).setZone("America/Monterrey").startOf("day").toJSDate();
    const end = DateTime.fromISO(endDate).setZone("America/Monterrey").endOf("day").toJSDate();


    // Obtenemos las matriculas y nombres de los alumnos que asistieron
    const users = await prisma.user.findMany({

      where:{
        attendances:{
          some:{
            date:{
              gte:start,
              lte:end
            }
          }
        }
      },
      include:{
        attendances:{
          where:{
            date:{
              gte:start,
              lte:end
            }
          }
        }
      }
    })

    // Mapeamos el resultado para que tenga el formato que esperamos

    const mappedUsers = users.map((user) => ({
      matricula: user?.registration ?? "Alumno Eliminado",
      "nombre completo": quitarAcentos(user?.name ?? "Alumno Eliminado"),
      asistencias: user.attendances.length
    }))

    // Ordenamos por numero de asistencias
    mappedUsers.sort((a,b) => b.asistencias - a.asistencias)

    if (mappedUsers.length > 0) {
      const asistenciasCsv = new Parser({ encoding: "utf-8" }).parse(
        mappedUsers
      );
      res.setHeader(
        "Content-Disposition",
        "attachment; filename=asistencias.csv"
      );
      res.setHeader("Content-Type", "text/csv ; charset=UTF-8");

      // De momento lo dejaremos desactivado para no borrar los registros, si hace falta se reactivara
      if (false) {
        await prisma.attendance.deleteMany();
      }

      return res.send(asistenciasCsv);
    }

    return res.status(500).json({
      error: "No hay registros en la tabla Asistencia para exportar.",
    });
  } catch (error: any) {
    return handleServerError(error, "Datos", res);
  }
};
export const exportAsistenciaDataCsv = async (req: Request, res: Response) => {
  const { startDate,endDate } = req.body as exportAsistenciaDataRT;

  try {

    const start = DateTime.fromISO(startDate).setZone("America/Monterrey").startOf("day").toJSDate();
    const end = DateTime.fromISO(endDate).setZone("America/Monterrey").endOf("day").toJSDate();


    const asistenciasConMatricula = (await prisma.attendance.findMany({
      where: {
        date: {
          gte: start,
          lte: end,
        },
      },
      include: {
        user: {
          select: {
            registration: true,
            name: true,
          },
        },
      },
      orderBy:{
        date:"asc"
      }
    })) as unknown as asistenciaPopulate[];

    const asistenciasTransformadas =
      asistenciasConMatricula.map(transformAsistencia);

    if (asistenciasTransformadas.length > 0) {
      const asistenciasCsv = new Parser({ encoding: "utf-8" }).parse(
        asistenciasTransformadas
      );
      res.setHeader(
        "Content-Disposition",
        "attachment; filename=asistencias.csv"
      );
      res.setHeader("Content-Type", "text/csv ; charset=UTF-8");

      // De momento lo dejaremos desactivado para no borrar los registros, si hace falta se reactivara
      if (false) {
        await prisma.attendance.deleteMany();
      }

      return res.send(asistenciasCsv);
    }

    return res.status(500).json({
      error: "No hay registros en la tabla Asistencia para exportar.",
    });
  } catch (error: any) {
    return handleServerError(error, "Datos", res);
  }
};

export const exportAsistenciaDataExcel = async (
  req: Request,
  res: Response
) => {
  // Extraemos la propiedad de eliminarRegistros
  const { startDate,endDate } = req.body as exportAsistenciaDataRT;

  const start = DateTime.fromISO(startDate).setZone("America/Monterrey").startOf("day").toJSDate();
    const end = DateTime.fromISO(endDate).setZone("America/Monterrey").endOf("day").toJSDate();


  try {
    // Obtenemos las asistencias menos las de relleno
    const asistenciasConMatricula = (await prisma.attendance.findMany({
      where: {
        date: {
          gte: start,
          lte: end,
        },
      },
      include: {
        user: {
          select: {
            registration: true,
            name: true,
          },
        },
      },
      orderBy:{
        date:"asc"
      }
    })) as unknown as asistenciaPopulate[];

    const asistenciasTransformadas =
      asistenciasConMatricula.map(transformAsistencia);

    // Si no hay datos de asistencia
    if (asistenciasTransformadas.length > 0) {
      // Creamos un nuevo libro de excel
      const wb = new xl.Workbook();
      // Creamos una nueva hoja
      const ws = wb.addWorksheet("Asistencia Alumnos");
      // Creamos el nombre de las columnas
      const headingColumnNames = [
        "ID",
        "Matrícula",
        "Nombre completo",
        "Fecha",
        "Hora",
        "Observaciones",
      ];
      // Nos colocamos en la primera celda
      let headingColumnIndex = 1;
      // Creamos las columnas con los nombres
      headingColumnNames.forEach((heading) => {
        ws.cell(1, headingColumnIndex++).string(heading);
      });
      // Nos colocamos en la segunda fila
      let rowIndex = 2;
      // Creamos las filas con los datos
      asistenciasTransformadas.forEach((record) => {
        // Nos colocamos al inicio en cada iteracion
        let columnIndex = 1;
        // Creamos las columnas con los datos
        Object.keys(record).forEach((columnName) => {
          // if(columnName === "fecha"){
          //   ws.cell(rowIndex, columnIndex++).string(String(record[columnName]));

          //   return;
          // }
          // if(columnName === "hora"){
          //   ws.cell(rowIndex, columnIndex++).string(String(hourFormatter(toMonterreyTimeZone(new Date(record[columnName])))));

          //   return;
          // }

          ws.cell(rowIndex, columnIndex++).string(String(record[columnName]));
        });
        // Aumentamos el indice de la fila
        rowIndex++;
      });

      // Indicamos que se descargara un archivo de excel

      res.setHeader(
        "Content-Disposition",
        "attachment; filename=asistencias.xlsx"
      );

      // Indicamos que el tipo de archivo es un excel
      res.setHeader("Content-Type", "application/vnd.ms-excel");

      // De momento lo dejaremos desactivado para no borrar los registros, si hace falta se reactivara
      if (false) {
        await prisma.attendance.deleteMany();
      }

      // Devolvemos el archivo de excel

      return wb.write("asistencias.xlsx", res);
    }
    return res.status(404).json({
      error: "No hay registros en la tabla Asistencia para exportar.",
    });
  } catch (error) {
    console.error(
      "Error al exportar y/o eliminar registros de la tabla Asistencia:",
      error
    );
    res.status(500).json({ error: "Error al procesar la solicitud" });
  }
};

export const exportDataCsv = async (req: Request, res: Response) => {
  const { deleteData } = req.body as exportDataCsvRT;

  try {
    // Obtenemos todos los usuarios no eliminados para exportar
    const users = await prisma.user.findMany({
      where: {
        deleted: false // Solo exportamos los usuarios no eliminados
      }
    });

    const usersTransformed = users.map(
      (user) =>
        ({
          id: user.id,
          nombre_completo: quitarAcentos(user.name),
          matricula: user.registration,
          clave_programa: quitarAcentos(user.program_key ?? ""),
          desc_genero: user.gender,
          desc_ejercicio_academico: user.academic_exercise,
          desc_programa: quitarAcentos(user.program ?? ""),
        } as AlumnoInt)
    );

    if (usersTransformed.length > 0) {
      const usersCsv = new Parser().parse(usersTransformed);
      res.setHeader("Content-Disposition", "attachment; filename=users.csv");
      res.setHeader("Content-Type", "text/csv");

      // Si deleteData es true, realizamos soft deletion después de exportar
      if (deleteData) {
        await prisma.user.updateMany({
          where: {
            deleted: false // Solo marcamos como eliminados los que no estén ya eliminados
          },
          data: {
            deleted: true // Soft delete: marcamos como eliminados en lugar de borrar físicamente
          }
        });
      }

      return res.send(usersCsv);
    }

    return res
      .status(500)
      .json({ error: "No records in the User table to export." });
  } catch (error: any) {
    return handleServerError(error, "Datos", res);
  }
};

export const updateStudentsCsv = async (req: ReqFileInt, res: Response) => {
  try {
    // Verificamos que el archivo sea un csv
    if (req.file.mimetype !== "text/csv") {
      return res.status(400).json({ error: "El archivo debe ser un csv" });
    }

    // Obtenemos el modo de operación del request body
    const mode = req.body.mode || 'replace'; // 'replace' o 'add'

    // Validamos que el modo sea válido
    if (mode !== 'replace' && mode !== 'add') {
      return res.status(400).json({ error: "El modo debe ser 'replace' o 'add'" });
    }

    // Creamos una variable para guardar los datos del archivo
    let alumnos: AlumnoCsv[] = [];

    console.log("Inicio - Modo:", mode);

    // Creamos un stream de lectura del archivo
    const stream = fs.createReadStream(req.file.path)
      // Ejecutamos el parser de csv
      .pipe(csvParser())
      // Hacemos el preprocesamiento de los datos
      .on("data", (rawRow: RawAlumnoCsv) => {
        // Hacemos el mapeo de los datos

        // Normalize the keys of the rawRow object
        Object.keys(rawRow).forEach((key) => {
          const normalizedKey = key.trim().replace(/[^a-zA-Z0-9]/g, '');
          rawRow[normalizedKey] = rawRow[key];
          if (key !== normalizedKey) {
            delete rawRow[key];
          }
        });

        const row: AlumnoCsv = {
          matricula: rawRow.Matricula,
          nombre_completo: rawRow["NombreCompleto"],
          desc_genero: rawRow["DescGenero"],
          clave_programa: rawRow["ClaveProgramaAcademico"],
          desc_programa: rawRow["DescProgramaAcademico"],
          desc_ejercicio_academico: rawRow["DescPeriodoAcademico"],
          desc_nivel_acad_alumno: rawRow["DescNivelAcademicoAlumno"]
        };

        // Transformamos los datos
        try {
          const alumno = transformRow(row);
          // Si es null entonces no lo agregamos
          if (!alumno) return;
          // Los agregamos al arreglo
          alumnos.push(alumno);
        } catch (error:any) {
          console.log("Error al transformar la fila", error.message);
          console.log(rawRow)
          stream.destroy(error)
        }

      })
      // Si hay un error
      .on("error", (error:any) => {
        return res.status(500).json({ msg: error.message });
      })
      .on("end", async () => {
        // Eliminamos el archivo al acabar de procesar
        fs.unlinkSync(req.file.path);

        // Creamos un identificador de sesión solo si estamos en modo 'replace'
        const session = mode === 'replace' ? randomUUID() : null;

        // Mapeamos los alumnos al formato de la base de datos
        const alumnosDb = alumnos.map((alumno) => ({
          name: alumno.nombre_completo,
          academic_exercise: alumno.desc_ejercicio_academico,
          gender: alumno.desc_genero,
          registration: alumno.matricula,
          program: alumno.desc_programa,
          program_key: alumno.clave_programa,
          session_id: session, // Solo asignamos session_id en modo 'replace'
          deleted: false // Aseguramos que los usuarios en el CSV no estén marcados como eliminados
        })) as user[];


        // Sincronizamos los usuarios con la base de datos

        // Creamos la transaccion, dividiremos en 10 lotes la información ya que las transacciones no pueden ser muy grandes

        const batchSize = 100;
        for (let i = 0; i < alumnosDb.length; i += batchSize) {
          const batch = alumnosDb.slice(i, i + batchSize);
          await prisma.$transaction(async (tx) => {
            const upsertPromises = batch.map((alumno) =>
              tx.user.upsert({
                where: { registration: alumno.registration },
                create: alumno,
                update: alumno,
              })
            );
            await Promise.all(upsertPromises);
          });
        }

        // Solo realizamos soft delete en modo 'replace'
        if (mode === 'replace' && session) {
          // Marcamos como eliminados (soft delete) los alumnos que no estén en la sesión actual
          // Esto incluye tanto usuarios con session_id diferente como usuarios con session_id NULL (legacy users)
          await prisma.user.updateMany({
            where:{
              OR: [
                {
                  session_id: {
                    not: session
                  }
                },
                {
                  session_id: null
                }
              ],
              deleted: false // Solo actualizamos los que no estén ya marcados como eliminados
            },
            data: {
              deleted: true
            }
          })
        }

        // Actualizamos el indice de id
        await prisma.$queryRaw`SELECT setval((SELECT pg_get_serial_sequence('user', 'id')), (SELECT MAX(id) FROM public."user") + 1);`

        // Si todo funcion sin errores entonces devolvemos un mensaje
        const message = mode === 'replace'
          ? "Archivo procesado correctamente. Los estudiantes existentes han sido reemplazados."
          : "Archivo procesado correctamente. Se han agregado nuevos estudiantes sin afectar los existentes.";

        return res.json({ msg: message });
      });

    return;
  } catch (error: any) {
    return handleServerError(error, "Datos", res);
  }
};
