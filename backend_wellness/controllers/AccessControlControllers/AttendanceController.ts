import { Request, Response } from "express";
import { handleServerError } from "../../helpers/handleServerError.js";

import {
  MarkAttendanceRT,
  getAsistenciaRT,
  graphsRT,
  updateAttendanceRT,
} from "../../types/RequestTypes/AccessControl/AsistenciaControllerRT";

import moment from "moment-timezone";

import { prisma } from "../../index.js";
import { GraphInt } from "../../types/GeneralTypes.js";
import { graphBigIntToInt } from "../../helpers/helpers.js";
import { DateTime } from "luxon";

export const getAsistencia = async (req: Request, res: Response) => {
  const query = req.query as unknown as getAsistenciaRT;

  const limit = query.limit ? +query.limit : 100;
  const page = query.page ? +query.page : 0;
  const filterBy = query.filterBy ?? "name";
  const queryValue = query.query ?? "";

  const offset = page * limit;

  try {
    // Obtenemos el total de asistencias que cumplan el criterio de busqueda (excluyendo usuarios eliminados)
    const total = await prisma.attendance.count({
      where: {
        user: {
          [filterBy]: {
            contains: queryValue,
            mode: "insensitive",
          },
          deleted: false, // Solo contamos asistencias de usuarios no eliminados
        },
      },
    });

    // Calculamos el numero de paginas
    const totalPages = Math.ceil(total / limit);

    // Obtenemos las asistencias con el limite y el offset (excluyendo usuarios eliminados)
    const asistencias = await prisma.attendance.findMany({
      take: limit,
      skip: offset,
      orderBy: {
        date: "desc",
      },
      include: {
        user: true,
      },
      where: {
        user: {
          [filterBy]: {
            contains: queryValue,
            mode: "insensitive",
          },
          deleted: false, // Solo incluimos asistencias de usuarios no eliminados
        },
      },
    });

    // Aplanamos el objeto de asistencias para que sea mas facil de manipular en la tabla
    const flattenedAsistencias = asistencias.map((asistencia) => ({
      ...asistencia,
      ...asistencia.user,
      user: undefined,
    }));

    return res.json({ attendances: flattenedAsistencias, totalPages });
  } catch (error: any) {
    return handleServerError(error, "Asistencia", res);
  }
};

const subtractTimeFromDate = (date: Date, minutes: number) => {
  date.setMinutes(date.getMinutes() - minutes);
  return date;
};

export const getAsistenciaToday = async (_req: Request, res: Response) => {
  try {
    const horaActual = parseInt(
      new Date()
        .toLocaleTimeString("es-MX", {
          timeZone: "America/Mexico_City",
        })
        .split(":")[0]
    );

    const asistencias = await prisma.attendance.findMany({
      where: {
        date: {
          gte: subtractTimeFromDate(new Date(), horaActual),
          lte: new Date(),
        },
      },
      include: {
        user: {
          select: {
            gender: true,
            program_key: true,
          },
        },
      },
    });

    const asistenciasTransformed = asistencias.map(({ user, ...rest }) => ({
      ...rest,
      gender: user?.gender,
      program_key: user?.program_key,
    }));

    return res.json({ status: "success", data: asistenciasTransformed });
  } catch (error: any) {
    return handleServerError(error, "Asistencia", res);
  }
};

export const getAsistenciaCount = async (_req: Request, res: Response) => {
  try {
    // Obtenemos el día de hoy y lo colocamos a la hora 0 en monterrey
    const initial_date = moment().tz("America/Monterrey").startOf("day").toDate();


    const attendances = await prisma.attendance.count({
      where: {
        date: {
          gte: initial_date,
          lte: new Date(),
        },
      },
    });

    return res.json(attendances);
  } catch (error: any) {
    return handleServerError(error, "Asistencia", res);
  }
};

export const markAttendance = async (req: Request, res: Response) => {
  const { matricula } = req.body as MarkAttendanceRT;
  try {
    // Primero buscamos al usuario (solo usuarios no eliminados)
    const user = await prisma.user.findFirst({
      where: {
        registration: {
          equals: matricula,
          mode: "insensitive",
        },
        deleted: false, // Solo permitimos marcar asistencia a usuarios no eliminados
      },
    });

    // Si no existe, devolvemos un error
    if (!user)
      return res.status(404).json({ msg: "No se encontró al usuario" });



    // Validamos que no haya una asistencia en el día

    const startOfDay = DateTime.now().setZone("America/Monterrey").startOf("day").toJSDate();
    const endOfDay = DateTime.now().setZone("America/Monterrey").endOf("day").toJSDate();

    const lastAttendance = await prisma.attendance.findFirst({
      where: {
        userId: user.id,
        date: {
          gte: startOfDay,
          lte: endOfDay,
        },
      },
    });

    // Si existe, devolvemos un error
    if (lastAttendance)
      return res
        .status(401)
        .json({
          msg: `Ya se registró una asistencia hoy a las ${DateTime.fromJSDate(lastAttendance.date).setZone("America/Monterrey").toFormat("hh:mm a")}`,
        });

    // Si no existe, creamos la asistencia
    const newAttendance = await prisma.attendance.create({
      data: {
        userId: user.id,
        date: new Date(),
      },
    });

    // Devolvemos el usuario con la fecha de la ultima asistencia y el atributo construido de todayAttendace como true
    return res.json({
      ...user,
      lastAttendance: newAttendance.date,
      attendedToday: true,
    });
  } catch (error: any) {
    return handleServerError(error, "Asistencia", res);
  }
};

export const updateAttendance = async (req: Request, res: Response) => {
  const { id_alumno, attendedToday } = req.body as updateAttendanceRT;
  try {
    // Obtenemos el usuario para devolverlo al final y para verificar si existe (solo usuarios no eliminados)
    const user = await prisma.user.findFirst({
      where: {
        id: id_alumno,
        deleted: false, // Solo permitimos actualizar asistencia de usuarios no eliminados
      },
    });

    // Si no existe, devolvemos un error
    if (!user)
      return res.status(404).json({ msg: "No se encontró al usuario" });

    // Verifica si el alumno asistió hoy
    if (attendedToday) {
      // Marcar asistencia: crea un nuevo registro en la tabla de Asistencia
      const attendance = await prisma.attendance.create({
        data: {
          userId: id_alumno,
          date: new Date(),
        },
      });

      return res.json({
        ...user,
        lastAttendance: attendance.date,
        attendedToday: true,
      });
    }

    // Desmarcar la última asistencia: busca el último registro de asistencia del alumno
    const lastAttendance = await prisma.attendance.findFirst({
      where: {
        userId: id_alumno,
      },
      orderBy: {
        date: "desc",
      },
    });

    // Si se encuentra un registro de última asistencia, lo elimina
    if (lastAttendance) {
      await prisma.attendance.delete({
        where: {
          id: lastAttendance.id,
        },
      });
    }

    return res.json({
      ...user,
      lastAttendance: null,
      attendedToday: false,
    });
  } catch (error: any) {
    return handleServerError(error, "Asistencia", res);
  }
};

export const graphs = async (req: Request, res: Response) => {
  const { startDate, endDate } = req.params as unknown as graphsRT;

  try {
    // Definimos nuestros objetos de fecha
    const start = new Date(startDate);
    const end = new Date(endDate);


    // Definimos el criterio de busqueda

    const where = {
      date: {
        gte: start,
        lte: end,
      },
    };

    // Asistencias por hora, lo haremos con un query de SQL
    const assistanceByHour: GraphInt[] = await prisma.$queryRaw`
      SELECT
        EXTRACT(HOUR FROM DATE_TRUNC('hour', date AT TIME ZONE 'UTC' AT TIME ZONE 'America/Monterrey')) as label,
        COUNT(*) as value
      FROM
        attendance
      WHERE
        date AT TIME ZONE 'UTC' AT TIME ZONE 'America/Monterrey' >= ${start.toISOString()}::timestamptz
        AND date AT TIME ZONE 'UTC' AT TIME ZONE 'America/Monterrey' < ${end.toISOString()}::timestamptz
      GROUP BY
        label
      ORDER BY
        label;
    `;

    // Transformar el formato del bigInt a number
    const assistenceByHourTransformed = graphBigIntToInt(assistanceByHour);

    // Crear arreglos de labels y values
    const labels = assistenceByHourTransformed.map((item) => item.label);
    const values = assistenceByHourTransformed.map((item) => item.value);

    // Obtenemos las asistencia entre las fechas dadas por hora en las fechas dadas

    const asistencias = await prisma.attendance.count({
      where,
    });


    // Obtenemos la cantidad de alumnos que hay en la tabla de usuario
    const alumnos = (
      await prisma.user.count({
        where: {
          attendances: {
            some: where,
          },
        }
      })
    )


    // Obtenemos los generos de las asistencias

    const genderCounts = await prisma.user.groupBy({
      where: {
        attendances: {
          some: where,
        },
      },
      by: ["gender"],
      _count: {
        id: true,
      },
    });

    // Mapeamos los resultados para tener la cantidad de mujeres y hombres
    const genderStats = genderCounts.map((item) => ({
      gender: item.gender,
      count: item._count.id,
    }));

    console.log(genderStats)

    // Segmentamos los resultados por genero

    const hombres = genderStats.reduce((acc, item) => {
      if (item.gender === "Masculino") return acc + item.count;

      return acc;
    }, 0);
    const mujeres = genderStats.reduce((acc, item) => {
      if (item.gender === "Femenino") return acc + item.count;

      return acc;
    }, 0);

    return res.json({
      hours: { labels, values },
      asistencias,
      alumnos,
      genderStats: { hombres, mujeres },
    });
  } catch (error: any) {
    return handleServerError(error, "Asistencia", res);
  }
};
