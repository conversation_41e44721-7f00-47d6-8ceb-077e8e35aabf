import { prisma } from "../../index.js";
import { handleServerError } from "../../helpers/handleServerError.js";
import moment from "moment";
export const addUser = async (req, res) => {
    const body = req.body;
    try {
        const user = await prisma.user.findUnique({
            where: {
                registration: body.registration.toLowerCase()
            }
        });
        if (user) {
            return res.status(400).json({ msg: "Ya existe un usuario con esa matricula" });
        }
        const newAlumno = await prisma.user.create({
            data: body,
        });
        return res.json(newAlumno);
    }
    catch (error) {
        return handleServerError(error, "Usuario", res);
    }
};
export const getUser = async (req, res) => {
    const { matricula } = req.params;
    try {
        const user = await prisma.user.findFirstOrThrow({
            where: {
                registration: matricula,
                deleted: false
            },
        });
        return res.json({ status: "success", data: user });
    }
    catch (error) {
        return handleServerError(error, "Asistencia", res);
    }
};
export const getUsers = async (req, res) => {
    const query = req.query;
    const limit = query.limit ? +query.limit : 100;
    const page = query.page ? +query.page : 0;
    const filterBy = query.filterBy ?? "name";
    const queryValue = query.query ?? "";
    const offset = (page) * limit;
    try {
        const total = await prisma.user.count({
            where: {
                [filterBy]: {
                    contains: queryValue,
                    mode: "insensitive",
                },
                deleted: false,
            },
        });
        const totalPages = Math.ceil(total / limit);
        const users = await prisma.user.findMany({
            take: limit,
            skip: offset,
            where: {
                [filterBy]: {
                    contains: queryValue,
                    mode: "insensitive",
                },
                deleted: false,
            },
        });
        return res.json({ users, totalPages });
    }
    catch (error) {
        return handleServerError(error, "Asistencia", res);
    }
};
export const getUsersLastAttendance = async (req, res) => {
    const { search } = req.params;
    const cleanSearch = search.replace(/%20/g, " ");
    try {
        const initial_date = moment().tz("America/Monterrey").startOf("day").toDate();
        const users = await prisma.user.findMany({
            where: {
                OR: [
                    {
                        name: {
                            contains: cleanSearch,
                            mode: "insensitive"
                        }
                    }
                ],
                deleted: false,
            },
            include: {
                attendances: {
                    where: {
                        date: {
                            gte: initial_date,
                            lte: new Date()
                        },
                    },
                    orderBy: {
                        date: "desc",
                    },
                    select: {
                        date: true,
                    },
                    take: 1,
                },
            },
            take: 10
        });
        const today = new Date().toISOString().split("T")[0];
        const data = users.map((user) => {
            const lastAttendance = user.attendances.length > 0 ? user.attendances[0].date.toISOString() : null;
            const attendedToday = user.attendances.length > 0 ? user.attendances[0].date.toISOString().split("T")[0] === today : false;
            return ({
                ...user,
                lastAttendance,
                attendedToday
            });
        });
        return res.json(data);
    }
    catch (error) {
        return handleServerError(error, "Asistencia", res);
    }
};
export const getUserByIdController = async (req, res) => {
    const { id } = req.params;
    try {
        const user = await prisma.user.findFirstOrThrow({
            where: {
                id: +id,
                deleted: false
            },
        });
        return res.json(user);
    }
    catch (error) {
        return handleServerError(error, "Usuario", res);
    }
};
export const updateUserController = async (req, res) => {
    const { id, ...body } = req.body;
    try {
        const user = await prisma.user.update({
            where: {
                id: id
            },
            data: body
        });
        return res.json(user);
    }
    catch (error) {
        return handleServerError(error, "Usuario", res);
    }
};
