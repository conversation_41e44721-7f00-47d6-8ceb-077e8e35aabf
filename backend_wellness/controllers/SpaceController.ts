import dotenv from "dotenv";
dotenv.config();


import { Request, Response } from "express";
import { ReqFileInt, ReqAdmin, ProyectionGraphInt } from "../types/GeneralTypes";
import { handleServerError } from "../helpers/handleServerError.js";
import {prisma} from "../index.js"

import moment from "moment-timezone";
import { v2 as cloudinary } from "cloudinary";
import { ReservationStatus } from "@prisma/client";

import {CreateSpaceRT, UpdateSpaceRT} from "../types/RequestTypes/SpaceControllerRT"
import { DateTime } from "luxon";
import { toleranceReserveMinutes } from "../constants/index.js";

cloudinary.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET,
  });



  

// Para crear un área
export const createSpace = async (req: ReqFileInt, res: Response) => {

    let space,image

    try{
        const {adminId,quota,materials_required,materials,onlineQuota,...data} = req.body as CreateSpaceRT;

        // Subimos la imagen
        
        image = await cloudinary.uploader.upload(req.file.path,{
            folder: `reservas`,
            quality: "auto:eco",
          });
        

        space = await prisma.space.create({
            data: {
                ...data,
                quota: +quota,
                onlineQuota: +onlineQuota,
                adminId:+adminId,
                materials_required: materials_required === "true" ? true:false,
                materials:materials.split(","),
                image: image.secure_url,
                image_id:image.public_id
                
            }
        })


        return res.status(200).json({msg: "Espacio creado!!"});
    }catch(error: any){
        // Si hay un error verificamos para hacer rollback de un cambio
        if(space){
            await prisma.space.delete({
                where:{
                    id: space.id
                }
            })
        }
        if(image){
            await cloudinary.uploader.destroy(image.public_id)
        }
        return handleServerError(error, "Admin", res);
    }
}

// Editar un area



export async function editSpace(req:ReqAdmin,res:Response){
    try {

        const {id,...data} = req.body as UpdateSpaceRT;

        // Verificamos si se nos paso una imagen
        const image = req?.file?.path

        // Verificamos si se proporciono una imagen
        if(image){
            // Obtenemos el espacio
            const space = await prisma.space.findFirst({
                where:{
                    id: +id
                }
            })
            // Si ya tenia una imagen se la borramos
            if(space?.image_id){
                await cloudinary.uploader.destroy(space?.image_id)
            }
            // Subimos la nueva imagen
            const newImage = await cloudinary.uploader.upload(image,{
                folder: `reservas`,
                quality: "auto:eco",
              });
            // Modificamos el espacio
             await prisma.space.update({
                where:{
                    id: +id
                },
                data:{
                    name: data.name,
                quota: +data.quota,
                onlineQuota: +data.onlineQuota,
                location: data.location,
                open_time: data.open_time,
                close_time: data.close_time,
                materials_required: data.materials_required === "true" ? true:false,
                    image: newImage.secure_url,
                    image_id:newImage.public_id
                }
            })

            // Devolvemos el espacio modificado
            return res.json({msg:"Espacio modificado correctamente"})
        }

        // Modificamos el espacio
         await prisma.space.update({
            where:{
                id: +id
            },
            data:{
                name: data.name,
                quota: +data.quota,
                onlineQuota: +data.onlineQuota,
                location: data.location,
                open_time: data.open_time,
                close_time: data.close_time,
                materials_required: data.materials_required === "true" ? true:false
            }
        })

        // Devolvemos el espacio modificado
        return res.json({msg:"Espacio modificado correctamente"})
        
    } catch (error:any) {
        return handleServerError(error, "Admin", res);
    }
}


// In progress...
export const getQuota = async (req: Request, res: Response) => {
    try{
        const {id} = req.body;
        const verify = await prisma.space.findFirst({
            where:{
                id: id
            }
        })
        if(!verify){
            return res.status(404).json({msg: "Area inexistente!!"})
        }
        return res.status(200).json({"quota": verify.quota});
    }catch(error: any){
        return handleServerError(error, "Admin", res);
    }
}
// Modificar el aforo predeterminado
export const changeQuota = async (req: Request, res: Response) => {
    try{
        const {id, quota} = req.body;
        const verify = await prisma.space.findFirst({
            where:{
                id: id
            }
        })
        if(!verify){
            return res.status(404).json({msg: "Area inexistente!!"});
        }
        if(quota<1 || quota>400){
            return res.status(404).json({msg: "Tiene que ser un número de aforo válido"})
        }
        await prisma.space.update({
            where:{
                id: id
            },
            data:{
                quota: quota
            }
        })
        const listaDeReservables = await prisma.reservable.findMany({
            where:{
                spaceId: id,
            },
            include:{
                reservations: {
                    where:{
                        status: ReservationStatus.APPROVED
                    }
                }
            }
        })
        for(const each of listaDeReservables){
           await prisma.$transaction(async (tx) => {
                if(each.reservations.length<=quota){
                    await tx.reservable.update({
                        where:{
                            id: each.id
                        },
                        data:{
                            quota: quota
                        }
                    })
                }
                else{
                    throw new Error("La transacción no se realizó correctamente");
                }
            })
        }
        return res.status(200).json({msg:"El aforo se ha cambiado exitosamente!!"})
    }catch(error:any){
        return handleServerError(error, "Admin", res)
    }
}

// Editar aforo reservable
export const changeQuotaReservable = async (req: Request, res: Response) => {
    try{
        const {id, quota} = req.body;
        const verify = await prisma.reservable.findFirst({
            where:{
                id: id
            }
        })
        if(!verify){
            return res.status(404).json({msg: "El espacio reservable no existe!!"});
        }
        await prisma.reservable.update({
            where:{
                id: id
            },
            data:{
                quota: quota
            }
        })
        return res.status(200).json({msg: "Se ha realizado el cambio correctamente!!"});
    }catch(error: any){
        return handleServerError(error, "Admin", res)
    }
}

// Crear reservables
export const createReservable = async (req: Request, res: Response) => {

    try{
        const {...data} = req.body

        await prisma.reservable.create({
            data:{
                ...data
            }
        })
        return res.status(200).json({msg: "Se ha creado un reservable de forma exitosa"})
    } catch(error: any) {
        return handleServerError(error, "Admin", res)
    }
}

// Crear reservacion

export const getUserSpaces = async (_req:ReqAdmin,res:Response)=>{
    try {

       const dateTimewithTolerance = DateTime.now().minus({ minutes: toleranceReserveMinutes }).toJSDate()
        // Obtenedremos solo los espacios que tengan reservables booking y tengan quotaOnline
        const spaces = await prisma.space.findMany({
            where:{
                reservables:{
                    some:{
                        booking:true,
                        init_date:{
                            gte: dateTimewithTolerance
                        },
                        onlineQuota:{
                            gt:0
                        }
                    },


                },
                
            },
            include:{
                closed_spaces:{
                    where:{
                        start_date:{
                            lt: new Date()
                        },
                        end_date:{
                            gt: dateTimewithTolerance
                        }
                    }
                }
            }
        })
        // destructure spaces to add open property and remove closed_spaces
        const newSpaces = spaces.map(space=>{
            const {closed_spaces,...data} = space
            return {
                ...data,
                open: closed_spaces.length===0
            }
        })
        return res.json(newSpaces)

    } catch (error:any) {
        return handleServerError(error,"Admin",res)
    }
}
export const getSpaces = async (_req:ReqAdmin,res:Response)=>{
    try {
        const dateTimewithTolerance = DateTime.now().minus({ minutes: toleranceReserveMinutes }).toJSDate()
        // Obtenedremos solo los espacios que tengan reservables booking
        const spaces = await prisma.space.findMany({
            where:{
                reservables:{
                    some:{
                        booking:true,
                        init_date:{
                            gte: dateTimewithTolerance
                        },
                        onlineQuota:{
                            gt:0
                        }
                    },
                },
            },
            include:{
                closed_spaces:{
                    where:{
                        start_date:{
                            lt: new Date()
                        },
                        end_date:{
                            gt: new Date()
                        }
                    }
                }
            },
            orderBy:{
                name:"asc"
            }
        })
        // destructure spaces to add open property and remove closed_spaces
        const newSpaces = spaces.map(space=>{
            const {closed_spaces,...data} = space
            return {
                ...data,
                open: closed_spaces.length===0
            }
        })
        return res.json(newSpaces)

    } catch (error:any) {
        return handleServerError(error,"Admin",res)
    }
}

export const openSpace = async (req:ReqAdmin,res:Response)=>{
    const {id} = req.params
    try {
        
        // Los eliminamos todos
        await prisma.closed_space.deleteMany({
            where:{
                spaceId:+id
            }
        })

        
        
        return res.json({msg:"Espacio Abierto"})
    } catch (error:any) {
        return handleServerError(error,"Admin",res)
    }
}

export const closeSpace = async (req:ReqAdmin,res:Response)=>{
    const data = req.body
    // Example request body
    // {
    //     "id": "1",
    //     "start_date": "2021-10-10T00:00:00.000Z",
    //     "end_date": "2021-10-20T00:00:00.000Z",
    //     "description": "Cerrado por remodelación"
    // }

    try {
        // Check if space is already closed
        const closedSpace = await prisma.closed_space.findFirst({
            where:{
                spaceId: Number(data.id),
                start_date:{
                    lt: new Date()
                },
                end_date:{
                    gt: new Date()
                }
            }
        })
        if(closedSpace){
            return res.status(404).json({msg:"El Espacio ya estaba cerrado"})
        }
        await prisma.closed_space.create({
            data:{
                spaceId: Number(data.id),
                start_date: new Date(data.start_date),
                end_date: new Date(data.end_date),
                description: data.description
            }
        })
        return res.json({msg:"Espacio Cerrado Correctemente"})
    } catch (error:any) {
        return handleServerError(error,"Admin",res)
    }
}

// Funcion encargada de obtener los datos de permanencia actuales

export const getPermanence = async (_req:Request,res:Response)=>{
    try {
        // Obtenemos la permanencia matutina
        const permancenceMorning = await prisma.permanenceTime.findFirst({
            where:{
                schedule:"MORNING"
            },
            orderBy:{
                id:"desc"
            }
        })
        // Obtenemos la permanencia despertina
        const permancenceNight = await prisma.permanenceTime.findFirst({
            where:{
                schedule:"NIGHT"
            },
            orderBy:{
                id:"desc"
            }
        })

        // Construimos la respuesta
        return {
            morning: permancenceMorning,
            night: permancenceNight
        }

    } catch (error:any) {
        return handleServerError(error,"Wellness",res)
        
    }
}

// Funcion encargada de modificar los datos de permanencia
export const setPermanence = async (req:ReqAdmin,res:Response)=>{
    const {schedule,hours,minutes} = req.body as {schedule:string,hours:number,minutes:number}

    try {
        // Obtenemos la permanencia actual con el schedule
        const permancence = await prisma.permanenceTime.create({
            data:{
                schedule: schedule.toUpperCase() as any,
                hours,
                minutes
            }
        })

        // Devolvemos la nueva permanencia
        return res.json(permancence)
        
    } catch (error:any) {
        return handleServerError(error,"Wellness",res)
        
    }
}

// Función encargada de obtener el estado del wellness center en tiempo real
export const getWellnessLiveStatus = async (_req:Request,res:Response)=>{

    try {

   
        // Obtenemos la hora actual en Monterrey
        const currentHour = DateTime.now().setZone("America/Monterrey").hour
        const currentMins = DateTime.now().setZone("America/Monterrey").minute

         // Obtenemos el aforo del wellness center buscando el espacio que contenga la palabra wellness

         const wellness = await prisma.space.findFirst({
            where:{
                name:{
                    contains:"wellness",
                    mode:"insensitive"
                }
            }
        })

        const wellnessQuota = wellness?.quota ?? 0

        // Si es entre las 3 y 3:30 de la tarde devolvemos que no hay nadie
        if((currentHour === 15 && currentMins >= 0 && currentMins <= 30)){
            return res.json([
                {label:"Llenado",value: 0},
                {label:"Restante",value: wellnessQuota}
            ])
        }

        // Con la hora ahora obtenemos los datos de con cuanto tiempo hacer el calculo
        let hours = 2
        let minutes = 0

        // Si la hora es menor a las 6 de la tarde que en este caso son las 18 - 1 horas(17)
        if(currentHour<17){
            // Obtenemos la configuracion de la mañana
            const morning = await prisma.permanenceTime.findFirst({
                where:{
                    schedule:"MORNING"
                },
                orderBy:{
                    id:"desc"
                }
            })

            // Modificamos el hours y minutes al de la configuracion
            hours = morning?.hours ?? 2
            minutes = morning?.minutes ?? 0
        }else{
            // Obtenemos la configuracion de la tarde
            const night = await prisma.permanenceTime.findFirst({
                where:{
                    schedule:"NIGHT"
                },
                orderBy:{
                    id:"desc"
                }
            })

            // Modificamos el hours y minutes al de la configuracion
            hours = night?.hours ?? 2
            minutes = night?.minutes ?? 0
        }

        // Ahora calculamos el tiempo total en minutos
        const totalMinutes = hours*60 + minutes

        // Calculamos la fecha de inicio
        const today = DateTime.now().setZone("America/Monterrey").minus({minutes:totalMinutes}).toJSDate()

        // Obtenemos las asistencias que hayan estado en el wellness center en la última hora y media
        const attendances = await prisma.attendance.count({
            where:{
                date:{
                    gt: today
                }
            }
        })

       
        const remaining = wellnessQuota - attendances < 0 ? 0 : wellnessQuota - attendances

        // Devolveremos un formato de label value percetage

        const fillPercentage = attendances / wellnessQuota * 100
        
        // Construimos la respuesta

        const response = [
            {label:"Llenado",value: Math.round(attendances)},
            {label:"Restante",value:Math.round(remaining)}
        ]

        return res.json(response)
        
    } catch (error:any) {
        return handleServerError(error,"Wellness",res)
        
    }
}

// Funcion encargada de mostrar los datos de asistencias por hora en el wellness y los proyectados

export const getWellnessAttendances = async (_req:Request,res:Response)=>{
    try {

         // Obtenemos la hora actual en Monterrey
         const currentLocalHour = DateTime.now().setZone("America/Monterrey").hour

         // Con la hora ahora obtenemos los datos de con cuanto tiempo hacer el calculo
         let hours = 2
         let minutes = 0
 
         // Si la hora es menor a las 6 de la tarde que en este caso son las 18 - 1 horas(17)
         if(currentLocalHour<17){
             // Obtenemos la configuracion de la mañana
             const morning = await prisma.permanenceTime.findFirst({
                 where:{
                     schedule:"MORNING"
                 },
                 orderBy:{
                     id:"desc"
                 }
             })
 
             // Modificamos el hours y minutes al de la configuracion
             hours = morning?.hours ?? 2
             minutes = morning?.minutes ?? 0
         }else{
             // Obtenemos la configuracion de la tarde
             const night = await prisma.permanenceTime.findFirst({
                 where:{
                     schedule:"NIGHT"
                 },
                 orderBy:{
                     id:"desc"
                 }
             })
 
             // Modificamos el hours y minutes al de la configuracion
             hours = night?.hours ?? 2
             minutes = night?.minutes ?? 0
         }
 
         // Ahora calculamos el tiempo total en minutos
         const totalMinutes = hours*60 + minutes

        // Obtenemos la hora actual en la zona horaria de Monterrey
        const currentDateTime = new Date().toLocaleString("en-US", {
          timeZone: "America/Monterrey",
        });
        const currentDate = new Date(currentDateTime);
        const currentHour = currentDate.getHours();
      
        // Array para almacenar las asistencias pasadas y proyecciones futuras
        let attendancesData: ProyectionGraphInt[] = [];
      
        // Función para convertir la hora a formato 12 horas con AM o PM
        const formatHour = (hour: number) => {
          const isPM = hour >= 12;
          const adjustedHour = hour % 12 === 0 ? 12 : hour % 12;
          return `${adjustedHour}:00 ${isPM ? "PM" : "AM"}`;
        };
      
        // Obtener asistencias pasadas
        for (let hour = 0; hour <= currentHour; hour++) {
          const startTime = moment
            .tz(
              {
                year: currentDate.getFullYear(),
                month: currentDate.getMonth(),
                day: currentDate.getDate(),
                hour: hour,
              },
              "America/Monterrey"
            )
            .toDate();
          const endTime = new Date(startTime.getTime());
          endTime.setMinutes(endTime.getMinutes() - totalMinutes);

          // Si se encuentra entre las 3 y 3:30 de la tarde marcamos 0
            if(hour === 15 && startTime.getMinutes() >= 0 && startTime.getMinutes() <= 30){
                attendancesData.push({
                label: formatHour(hour),
                value: 0,
                projection: false,
                });
                continue;
            }
      
          const count = await prisma.attendance.count({
            where: {
              date: {
                lte: startTime,
                gte: endTime,
              },
            },
          });
      
          if (count > 0) {
            // Solo añadir si el conteo es mayor a 0
            attendancesData.push({
              label: formatHour(hour),
              value: count,
              projection: false,
            });
          }
        }
      
        // Calcular proyecciones futuras usando el mismo día de la semana de la semana pasada
        const lastWeekDate = new Date(currentDate.getTime());
        lastWeekDate.setDate(currentDate.getDate() - 7);
      
        for (let hour = currentHour + 1; hour < 24; hour++) {
          const startTime = moment
            .tz(
              {
                year: lastWeekDate.getFullYear(),
                month: lastWeekDate.getMonth(),
                day: lastWeekDate.getDate(),
                hour: hour,
              },
              "America/Monterrey"
            )
            .toDate();
          const endTime = new Date(startTime.getTime());
          endTime.setMinutes(endTime.getMinutes() + totalMinutes); 

          // Si se encuentra entre las 3 y 3:30 de la tarde marcamos 0
            if(hour === 15 && startTime.getMinutes() >= 0 && startTime.getMinutes() <= 30){
                attendancesData.push({
                label: formatHour(hour),
                value: 0,
                projection: true,
                });
                continue;
            }
      
          const projectionCount = await prisma.attendance.count({
            where: {
              date: {
                gte: startTime,
                lt: endTime,
              },
            },
          });
      
          if (projectionCount > 0) {
            // Solo añadir si la proyección es mayor a 0
            attendancesData.push({
              label: formatHour(hour),
              value: projectionCount,
              projection: true,
            });
          }
        }
      
        // Respuesta con las asistencias pasadas y las proyecciones
       return res.json(attendancesData);
      } catch (error: any) {
        return handleServerError(error, "Wellness", res);
      }
}
