import { handleServerError } from "../helpers/handleServerError.js";
import { prisma } from "../index.js";
export async function countPerson(_req, res) {
    try {
        await prisma.personExit.create({
            data: {
                date: new Date(),
            },
        });
        return res.json({ msg: "Salida registrada correctamente" });
    }
    catch (error) {
        return handleServerError(error, "PersonCounter", res);
    }
}
