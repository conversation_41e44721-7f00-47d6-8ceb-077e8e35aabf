import { handleServerError } from "../helpers/handleServerError.js";
import { prisma } from "../index.js";
import { DateTime } from "luxon";
export const getReservationsStats = async (req, res) => {
    try {
        const { end, spaceId, start, groupBy } = req.query;
        console.log(start, end, spaceId, groupBy);
        const startDate = DateTime.fromISO(start);
        const endDate = DateTime.fromISO(end);
        const reservations = await prisma.reservation.findMany({
            where: {
                reservation_date: {
                    gte: startDate.toJSDate(),
                    lte: endDate.toJSDate(),
                },
                reservable: {
                    spaceId: +spaceId,
                },
            },
        });
        let reservationsData = [];
        if (groupBy == "day") {
            const reservationsByDay = reservations.reduce((acc, curr) => {
                const label = DateTime.fromJSDate(curr.reservation_date)
                    .setLocale("es-MX")
                    .toFormat("cccc");
                const existingDay = acc.find((el) => el.label === label);
                if (!existingDay) {
                    acc.push({ label, value: 1 });
                }
                else {
                    existingDay.value++;
                }
                return acc;
            }, []);
            const daysOfWeek = ["lunes", "martes", "miércoles", "jueves", "viernes", "sábado", "domingo"];
            reservationsData = reservationsByDay.sort((a, b) => daysOfWeek.indexOf(a.label.toLowerCase()) - daysOfWeek.indexOf(b.label.toLowerCase()));
        }
        else if (groupBy == "week") {
            const reservationsByWeek = reservations.reduce((acc, curr) => {
                const dt = DateTime.fromJSDate(curr.reservation_date).setLocale("es-MX");
                const weekNumber = dt.weekNumber;
                const weekYear = dt.weekYear;
                const weekStart = dt.startOf("week").toFormat("dd/MM");
                const weekEnd = dt.endOf("week").toFormat("dd/MM");
                const label = `Semana del ${weekStart} al ${weekEnd}`;
                const weekKey = `${weekYear}-${weekNumber}`;
                const existingWeek = acc.find((el) => el.key === weekKey);
                if (!existingWeek) {
                    acc.push({ key: weekKey, label, value: 1 });
                }
                else {
                    existingWeek.value++;
                }
                return acc;
            }, []);
            reservationsByWeek.sort((a, b) => {
                const [yearA, weekA] = a.key.split("-").map(Number);
                const [yearB, weekB] = b.key.split("-").map(Number);
                if (yearA !== yearB)
                    return yearA - yearB;
                return weekA - weekB;
            });
            reservationsData = reservationsByWeek.map(({ label, value }) => ({ label, value }));
        }
        return res.json(reservationsData);
    }
    catch (error) {
        return handleServerError(error, "Admin", res);
    }
};
