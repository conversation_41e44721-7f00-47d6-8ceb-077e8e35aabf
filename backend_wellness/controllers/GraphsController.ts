import { Request, Response } from "express";
import { handleServerError } from "../helpers/handleServerError.js";
import { prisma } from "../index.js";
import { GraphsControllerRT } from "../types/RequestTypes/GraphsControllerRT.js";
import { GraphInt } from "../types/GeneralTypes.js";
import { DateTime } from "luxon";

export const getReservationsStats = async (req: Request, res: Response) => {
    try {
        const { end, spaceId, start,groupBy} = req.query as unknown as GraphsControllerRT & { groupBy: "day" | "week" };

        console.log(start, end, spaceId,groupBy);

        const startDate = DateTime.fromISO(start);
        const endDate = DateTime.fromISO(end);

        // const dateDiff = endDate.diff(startDate, "days").days;

        // Obtenemos las reservaciones en el periodo de tiempo
        const reservations = await prisma.reservation.findMany({
            where: {
                reservation_date: {
                    gte:startDate.toJSDate(),
                    lte: endDate.toJSDate(),
                },
                reservable: {
                    spaceId: +spaceId,
                },
            },
        });

        let reservationsData: GraphInt[] = [];

        if (groupBy == "day") {
            // Las agrupamos por día de la semana
            const reservationsByDay = reservations.reduce((acc, curr) => {
                const label = DateTime.fromJSDate(curr.reservation_date)
                    .setLocale("es-MX")
                    .toFormat("cccc");
                const existingDay = acc.find((el) => el.label === label);
                if (!existingDay) {
                    acc.push({ label, value: 1 });
                } else {
                    existingDay.value++;
                }
                return acc;
            }, [] as GraphInt[]);

            const daysOfWeek = ["lunes", "martes", "miércoles", "jueves", "viernes", "sábado", "domingo"];

            // Ordenamos los datos por día de la semana
            reservationsData = reservationsByDay.sort(
                (a, b) =>
                    daysOfWeek.indexOf(a.label.toLowerCase()) - daysOfWeek.indexOf(b.label.toLowerCase())
            );
        } else if (groupBy=="week"){
            // Agrupamos por semana
            const reservationsByWeek = reservations.reduce((acc, curr) => {
                const dt = DateTime.fromJSDate(curr.reservation_date).setLocale("es-MX");
                const weekNumber = dt.weekNumber;
                const weekYear = dt.weekYear;

                const weekStart = dt.startOf("week").toFormat("dd/MM");
                const weekEnd = dt.endOf("week").toFormat("dd/MM");

                const label = `Semana del ${weekStart} al ${weekEnd}`;
                const weekKey = `${weekYear}-${weekNumber}`;

                const existingWeek = acc.find((el) => el.key === weekKey);
                if (!existingWeek) {
                    acc.push({ key: weekKey, label, value: 1 });
                } else {
                    existingWeek.value++;
                }
                return acc;
            }, [] as { key: string; label: string; value: number }[]);

            // Ordenamos las semanas
            reservationsByWeek.sort((a, b) => {
                const [yearA, weekA] = a.key.split("-").map(Number);
                const [yearB, weekB] = b.key.split("-").map(Number);
                if (yearA !== yearB) return yearA - yearB;
                return weekA - weekB;
            });

            // Formateamos los datos para la respuesta
            reservationsData = reservationsByWeek.map(({ label, value }) => ({ label, value }));
        }

        return res.json(reservationsData);
    } catch (error: any) {
        return handleServerError(error, "Admin", res);
    }
};