import { handleServerError } from "../helpers/handleServerError.js";
import { prisma } from "../index.js";
export const createAnnounce = async (req, res) => {
    const body = req.body;
    try {
        await prisma.announce.create({
            data: {
                ...body,
                init_date: new Date(body.init_date),
                end_date: new Date(body.end_date),
                event_date: new Date(body.event_date)
            }
        });
        return res.json({ message: "Se ha creado correctamente el anuncio." });
    }
    catch (error) {
        return handleServerError(error, "Anuncio", res);
    }
};
export const editAnnounce = async (req, res) => {
    const body = req.body;
    try {
        await prisma.announce.update({
            where: {
                id: body.id
            },
            data: {
                ...body,
                init_date: new Date(body.init_date),
                end_date: new Date(body.end_date),
                event_date: new Date(body.event_date)
            }
        });
        return res.json({ message: "Se ha editado el anuncio correctamente" });
    }
    catch (error) {
        return handleServerError(error, "Anuncio", res);
    }
};
export const deleteAnnounce = async (req, res) => {
    const { id } = req.params;
    try {
        await prisma.announce.delete({
            where: {
                id: +id
            }
        });
        return res.json({ message: "Se ha eliminado el anuncio correctamente" });
    }
    catch (error) {
        return handleServerError(error, "Anuncio", res);
    }
};
export const getAnnounce = async (req, res) => {
    const { id } = req.params;
    try {
        const announce = await prisma.announce.findUnique({
            where: {
                id: +id
            }
        });
        return res.json({ announce });
    }
    catch (error) {
        return handleServerError(error, "Anuncio", res);
    }
};
export const getAnnounces = async (_req, res) => {
    try {
        const announces = await prisma.announce.findMany({
            where: {
                init_date: {
                    lte: new Date()
                },
                end_date: {
                    gte: new Date()
                }
            }
        });
        return res.json(announces);
    }
    catch (error) {
        return handleServerError(error, "Anuncio", res);
    }
};
export const getAllAnnounces = async (_req, res) => {
    try {
        const announces = await prisma.announce.findMany();
        return res.json(announces);
    }
    catch (error) {
        return handleServerError(error, "Anuncio", res);
    }
};
