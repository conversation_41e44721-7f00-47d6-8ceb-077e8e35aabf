import { Request, Response } from "express"
import { handleServerError } from "../helpers/handleServerError.js"

import {createAnnounceRT, deleteAnnounceRT, editAnnounceRT, getAnnounceRT} from "../types/RequestTypes/AnnounceControllerRT"
import { prisma } from "../index.js"


export const createAnnounce = async (req:Request,res:Response)=>{
    const body = req.body as createAnnounceRT
    try {

        // Creamos el anuncio
        await prisma.announce.create({
            data:{
                ...body,
                init_date:new Date(body.init_date),
                end_date:new Date(body.end_date),
                event_date: new Date(body.event_date)
            }
        })

        return res.json({message:"Se ha creado correctamente el anuncio."})
        
    } catch (error:any) {
        return handleServerError(error,"Anuncio",res)
    }
}


export const editAnnounce = async (req:Request,res:Response)=>{
    const body = req.body as editAnnounceRT
    try {

        // Modificamos el anuncio
        await prisma.announce.update({
            where:{
                id:body.id
            },
            data:{
                ...body,
                init_date:new Date(body.init_date),
                end_date:new Date(body.end_date),
                event_date: new Date(body.event_date)
            }
        })

        return res.json({message:"Se ha editado el anuncio correctamente"})
        
    } catch (error:any) {
        return handleServerError(error,"Anuncio",res)
    }
}

export const deleteAnnounce = async (req:Request,res:Response)=>{
    const {id} = req.params as unknown as deleteAnnounceRT
    try {

        // Eliminamos el anuncio
        await prisma.announce.delete({
            where:{
                id:+id
            }
        })

        return res.json({message:"Se ha eliminado el anuncio correctamente"})

    } catch (error:any) {
        return handleServerError(error,"Anuncio",res)
    }
}

export const getAnnounce = async (req:Request,res:Response)=>{
    const {id} = req.params as unknown as getAnnounceRT
    try {

        // Obtenemos el anuncio
        const announce = await prisma.announce.findUnique({
            where:{
                id:+id
            }
        })

        return res.json({announce})

    } catch (error:any) {
        return handleServerError(error,"Anuncio",res)
    }
}

export const getAnnounces = async (_req:Request,res:Response)=>{
    try {

        // Obtenemos los anuncios que esten en el rango activo
        const announces = await prisma.announce.findMany({
            where:{
                init_date:{
                    lte:new Date()
                },
                end_date:{
                    gte:new Date()
                }
            }
        })

        return res.json(announces)

    } catch (error:any) {
        return handleServerError(error,"Anuncio",res)
    }
} 
export const getAllAnnounces = async (_req:Request,res:Response)=>{
    try {

        // Obtenemos todos los anuncios
        const announces = await prisma.announce.findMany()

        return res.json(announces)

    } catch (error:any) {
        return handleServerError(error,"Anuncio",res)
    }
} 