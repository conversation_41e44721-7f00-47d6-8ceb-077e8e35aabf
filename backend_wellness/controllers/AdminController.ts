import dotenv from "dotenv";
dotenv.config();

import { Request, Response } from "express";
import { ReqAdmin, ReqFileInt } from "../types/GeneralTypes";
import { handleServerError } from "../helpers/handleServerError.js";
import { prisma } from "../index.js";
import { checkPassword, hashPassword } from "../functions/passwordFunctions.js";
import generarJWT from "../helpers/generarJWT.js";

import {
  createUserRT,
  deleteAdminRT,
  updateAdminRT,
} from "../types/RequestTypes/AdminControllerRT";
import { DateTime } from "luxon";
import { v2 as cloudinary } from "cloudinary";

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});


export const login = async (req: Request, res: Response) => {
  const { email, password } = req.body;

  try {
    const admin = await prisma.admin.findFirst({
      where: {
        email: {
          mode: "insensitive",
          equals: email,
        },
      },
    });

    if (!admin) {
      return res.status(404).json({ msg: "No se encontro" });
    }
    const verify = await checkPassword(password, admin.password);
    if (!verify) {
      return res.status(401).json({ msg: "Constraseña incorrecta" });
    }
    const token = generarJWT(admin.id);
    return res.json({ ...admin, token });
  } catch (error: any) {
    return handleServerError(error, "Admin", res);
  }
};

export const profile = async (req: ReqAdmin, res: Response) => {
  const { admin } = req;

  try {
    return res.json(admin);
  } catch (error: any) {
    return handleServerError(error, "Admin", res);
  }
};

export const getAdmins = async (_req: Request, res: Response) => {
  try {
    const admins = await prisma.admin.findMany({
      orderBy: {
        name: "asc",
      },
    });

    return res.json(admins);
  } catch (error: any) {
    return handleServerError(error, "Administradores", res);
  }
};

export const createUser = async (req: Request, res: Response) => {
  const { name, email, password, role } = req.body as createUserRT;
  try {
    const exists = await prisma.admin.findFirst({
      where: {
        email: email,
      },
    });
    if (exists) {
      return res
        .status(404)
        .json({ msg: "El usuario ya existe en la plataforma" });
    }
    const admin = await prisma.admin.create({
      data: {
        name: name,
        email: email,
        password: await hashPassword(password),
        role: role,
      },
    });
    return res.json(admin);
  } catch (error: any) {
    return handleServerError(error, "Admin", res);
  }
};

export const updateAdmin = async (req: Request, res: Response) => {
  const { id, ...body } = req.body as updateAdminRT;

  const { password } = body;
  try {
    // Modificamos el administrador
    const admin = await prisma.admin.update({
      where: {
        id: id,
      },
      data: {
        ...body,
        // Si se proporciono un password entonces lo cambiamos hasheandolo
        ...(password &&
          password !== "" && { password: await hashPassword(password) }),
      },
    });

    return res.json({ msg: "Administrador editado correctamente" });
  } catch (error: any) {
    return handleServerError(error, "Administradores", res);
  }
};
export const deleteAdmin = async (req: Request, res: Response) => {
  const { id } = req.params as unknown as deleteAdminRT;

  try {
    // Eliminamos el administrador
    await prisma.admin.delete({
      where: {
        id: +id,
      },
    });

    return res.json({ msg: "Admin eliminado correctamente" });
  } catch (error: any) {
    return handleServerError(error, "Administradores", res);
  }
};

export async function adminNextClass(req: ReqAdmin, res: Response) {
    const { admin } = req;

  try {
    // Verificamos si si se paso un administrador
    if (!admin) {
      return res.status(404).json({ msg: "No se proporciono el administrador, vuelve a iniciar sesión" });
    }

    // Obtenemos el reservable mas cercano a la fecha actual
    const reservable = await prisma.reservable.findFirst({
      where:{
        adminId: admin.id,
        init_date:{
          gte: DateTime.now(
            
          ).minus({minutes: 10}).toJSDate() // Damos un margen de 10 minutos
        }
      },
        orderBy:{
            init_date: "asc"
        }
    });

    // Si no se encontro ningun reservable
    if (!reservable) {
      return res.status(404).json({ msg: "No hay reservables proximos" });
    }

    // Si si se encontro de devuelve el id del reservable
    return res.json({ reservableId: reservable.id });
  } catch (error: any) {
    return handleServerError(error, "Administradores", res);
  }
}


// subir imagen
export const uploadCarouselImage = async (req: ReqFileInt, res: Response) => {
  try {
    const { path } = req.file;

    // Obtenemos el orden de la ultima imagen
    const lastImage = await prisma.carouselImage.findFirst({
      orderBy: {
        order: "desc",
      },
    });

  

    const image = await cloudinary.uploader.upload(path, {
      folder: "imagenCarrusel",
      quality: "auto:eco",
    });

    const savedImage = await prisma.carouselImage.create({
      data: {
        url: image.secure_url,
        image_id: image.public_id,
        order: lastImage ? lastImage.order + 1 : 1,
      },
    });

    return res.status(200).json({
      imageUrl: savedImage.url,
      imageId: savedImage.image_id,
    });
  } catch (error: any) {
    return handleServerError(error, "Admin", res);
  }
};


// Eliminacion imagen
export const deleteCarouselImage = async (req: Request, res: Response) => {
try {
  const { id } = req.params;

  
  
  const imageDelete = await prisma.carouselImage.delete({
    where: {
      id: +id,
    },
  });
  // Eliminamos la imagen de Cloudinary
  await cloudinary.uploader.destroy(imageDelete.image_id);

  // Actualizamos todas las imagenes que tengan un orden mayor al de la imagen eliminada y le restamos 1
  await prisma.carouselImage.updateMany({
    where: {
      order: {
        gt: imageDelete.order,
      },
    },
    data: {
      order: {
        decrement: 1,
      },
    },
  });

  return res.status(200).json({ msg: "Imagen eliminada correctamente" });
} catch (error: any) {
  return handleServerError(error, "Admin", res);
}
};

// Actualizacion de la imagen, no es para actualizar la imagen en si, sino para actualizar la informacion de la imagen
export const updateCarouselImage = async (req: Request, res: Response) => {
  try {

    const { id,order } = req.body;

    console.log(id,order);

    // Obtenemos la imagen actual
    const image = await prisma.carouselImage.findFirst({
      where: {
        id: id,
      },
    });

    // Obtenemos la imagen que tiene el orden que se quiere actualizar
    const imageOrder = await prisma.carouselImage.findFirst({
      where: {
        order: order,
      },
    });

    if(!image || !imageOrder){
      return res.status(404).json({msg: "No se encontro la imagen"});
    }

    // Actualizamos la imagen que tiene el orden
    await prisma.carouselImage.update({
      where: {
        id: imageOrder.id,
      },
      data: {
        order: image.order,
      },
    });

    // Actualizamos la imagen
    await prisma.carouselImage.update({
      where: {
        id: id,
      },
      data: {
        order: order,
      },
    });

    return res.status(200).json({ msg: "Imagen actualizada correctamente" });


    
  } catch (error:any) {
    return handleServerError(error, "Admin", res);
  }
}


// Obtener las imagenes
export const getCarouselImages = async (_req: Request, res: Response) => {
  try {
    const images = await prisma.carouselImage.findMany({
      orderBy: {
        order: "asc",
      },
    });

    return res.json(images);
  } catch (error: any) {
    return handleServerError(error, "Admin", res);
  }
}