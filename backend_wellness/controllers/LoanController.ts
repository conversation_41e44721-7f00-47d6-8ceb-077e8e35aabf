// controllers/LoanController.ts
import { Request, Response } from "express";
import { PrismaClient, LoanStatus } from "@prisma/client";
import {
  CreateLoanRT,
  UpdateLoanStatusRT,
  GetLoansByStatusRT,
  GetLoansByResponsibleRT,
  ValidateStudentIdsRT
} from "../types/RequestTypes/LoanControllerRT.js";

const prisma = new PrismaClient();

export async function createLoan(req: Request, res: Response): Promise<Response | void> {
  try {
    const {
      materialId,
      responsibleId,
      studentIds,
      quantity,
      pickupDate,
      returnDate,
      pickupTime,
      returnTime,
    } = req.body as CreateLoanRT;

    // Verificar que el material existe y tiene suficiente cantidad disponible
    const material = await prisma.material.findUnique({
      where: { id: parseInt(materialId as string) }
    });

    if (!material) {
      return res.status(404).json({ msg: "Material no encontrado" });
    }

    if (material.quantity < parseInt(quantity as string)) {
      return res.status(400).json({ msg: "No hay suficiente cantidad disponible de este material" });
    }

    // Verificar que la cantidad de matrículas es suficiente
    if (studentIds.length < material.minMatriculas) {
      return res.status(400).json({
        msg: `Se requieren al menos ${material.minMatriculas} matrículas para este material`
      });
    }

    const loan = await prisma.loan.create({
      data: {
        materialId: parseInt(materialId as string),
        responsibleId,
        studentIds,
        quantity: parseInt(quantity as string),
        pickupDate: new Date(pickupDate),
        returnDate: new Date(returnDate),
        pickupTime,
        returnTime,
        status: LoanStatus.PENDING,
      },
    });

    res.json({ msg: "Préstamo creado", loan });
  } catch (error: any) {
    console.error("Error al crear préstamo:", error);
    res.status(500).json({ msg: "Error al crear préstamo", error: error.message });
  }
}



export async function getAllLoans(_: Request, res: Response): Promise<Response | void> {
    try {
      console.log("Getting all loans...");

      // Use Prisma to get loans with material information
      const loans = await prisma.loan.findMany({
        include: {
          material: {
            select: {
              name: true,
              image: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      // Transform the result to match the expected format
      const formattedLoans = loans.map(loan => ({
        ...loan,
        material_name: loan.material.name,
        material_image: loan.material.image
      }));

      console.log("Loans fetched successfully:", formattedLoans.length);
      res.json(formattedLoans);
    } catch (error: any) {
      console.error("Error in getAllLoans:", error.message, error.stack);
      res.status(500).json({ msg: "Error al obtener préstamos", error: error.message });
    }
  }

export async function updateLoanStatus(req: Request, res: Response): Promise<Response | void> {
  try {
    console.log("Updating loan status...");
    const { id } = req.params;
    const { status, notes } = req.body as UpdateLoanStatusRT;

    const loanId = parseInt(id);

    // Get the current loan using Prisma
    const currentLoan = await prisma.loan.findUnique({
      where: { id: loanId },
      include: {
        material: {
          select: {
            id: true,
            quantity: true
          }
        }
      }
    });

    if (!currentLoan) {
      return res.status(404).json({ msg: "Préstamo no encontrado" });
    }

    // Transform to match the expected format
    const formattedLoan = {
      ...currentLoan,
      material_quantity: currentLoan.material.quantity
    };

    console.log("Current loan:", formattedLoan);

    // Update the loan status using Prisma's update method
    try {
      // Si estamos cambiando a ON_LOAN, actualizar la cantidad disponible del material
      if (status === LoanStatus.ON_LOAN && formattedLoan.status !== LoanStatus.ON_LOAN) {
        await prisma.material.update({
          where: { id: formattedLoan.materialId },
          data: { quantity: { decrement: formattedLoan.quantity } }
        });
        console.log("Material quantity decreased");
      }

      // Si estamos devolviendo el material (de ON_LOAN o LATE a RETURNED)
      if ((formattedLoan.status === LoanStatus.ON_LOAN || formattedLoan.status === LoanStatus.LATE) && status === LoanStatus.RETURNED) {
        // Actualizar la cantidad disponible del material
        await prisma.material.update({
          where: { id: formattedLoan.materialId },
          data: { quantity: { increment: formattedLoan.quantity } }
        });
        console.log("Material quantity increased");
      }

      // Update the loan status
      const updateData: any = {
        status: status,
        penaltyApplied: status === LoanStatus.LATE
      };

      // Add notes if provided
      if (notes) {
        updateData.penaltyNotes = notes;
      }

      // Update the loan
      await prisma.loan.update({
        where: { id: loanId },
        data: updateData
      });
    } catch (error) {
      console.error("Error updating with Prisma methods:", error);
      throw error;
    }

    // Get the updated loan using Prisma
    const updatedLoan = await prisma.loan.findUnique({
      where: { id: loanId },
      include: {
        material: {
          select: {
            name: true,
            image: true
          }
        }
      }
    });

    // Transform to match the expected format
    const updated = updatedLoan ? {
      ...updatedLoan,
      material_name: updatedLoan.material.name,
      material_image: updatedLoan.material.image
    } : null;

    console.log("Loan updated successfully");

    res.json({ msg: "Estado actualizado", updated });
  } catch (error: any) {
    console.error("Error al actualizar estado:", error);
    res.status(500).json({ msg: "Error al actualizar estado", error: error.message });
  }
}

// Obtener préstamos por matrícula del responsable
export async function getLoansByResponsible(req: Request, res: Response): Promise<Response | void> {
  try {
    const { registration } = req.params as unknown as GetLoansByResponsibleRT;

    const loans = await prisma.loan.findMany({
      where: {
        responsibleId: registration
      },
      include: { material: true },
      orderBy: { createdAt: "desc" }
    });

    res.json(loans);
  } catch (error: any) {
    console.error("Error al obtener préstamos del usuario:", error);
    res.status(500).json({ msg: "Error al obtener préstamos del usuario", error: error.message });
  }
}

// Obtener préstamos por estado
export async function getLoansByStatus(req: Request, res: Response): Promise<Response | void> {
  try {
    const { status } = req.params as unknown as GetLoansByStatusRT;

    const loans = await prisma.loan.findMany({
      where: { status: status as LoanStatus },
      include: { material: true },
      orderBy: { createdAt: "desc" }
    });

    res.json(loans);
  } catch (error: any) {
    console.error("Error al obtener préstamos por estado:", error);
    res.status(500).json({ msg: "Error al obtener préstamos por estado", error: error.message });
  }
}

// Verificar préstamos atrasados
export async function checkLateLoans(_req: Request, res: Response): Promise<Response | void> {
  try {
    console.log("Checking for late loans...");
    const now = new Date();

    // Find loans that are late using Prisma
    const lateLoans = await prisma.loan.findMany({
      where: {
        status: LoanStatus.ON_LOAN,
        returnDate: {
          lt: now
        }
      },
      select: {
        id: true,
        materialId: true,
        responsibleId: true
      }
    });

    // Format the date for the penalty notes
    const formattedDate = now.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });

    // Update each late loan
    const updatePromises = lateLoans.map(loan =>
      prisma.loan.update({
        where: { id: loan.id },
        data: {
          status: LoanStatus.LATE,
          penaltyApplied: true,
          penaltyNotes: `Préstamo marcado automáticamente como atrasado el ${formattedDate}`
        }
      })
    );

    // Execute all updates
    await Promise.all(updatePromises);

    console.log("Late loans updated:", lateLoans.length);

    res.json({
      msg: `${lateLoans.length} préstamos marcados como atrasados`,
      lateLoans: lateLoans
    });
  } catch (error: any) {
    console.error("Error al verificar préstamos atrasados:", error);
    res.status(500).json({ msg: "Error al verificar préstamos atrasados", error: error.message });
  }
}

// Validar matrículas de estudiantes
export async function validateStudentIds(req: Request, res: Response): Promise<Response | void> {
  try {
    const { studentIds } = req.body as ValidateStudentIdsRT;

    if (!studentIds || !Array.isArray(studentIds)) {
      return res.status(400).json({ msg: "Se requiere un array de matrículas" });
    }

    const validStudents: any[] = [];
    const invalidStudents: string[] = [];

    // Verificar cada matrícula
    for (const id of studentIds) {
      const student = await prisma.user.findUnique({
        where: { registration: id }
      });

      if (student) {
        validStudents.push({
          registration: id,
          name: student.name
        });
      } else {
        invalidStudents.push(id);
      }
    }

    res.json({
      valid: invalidStudents.length === 0,
      validStudents,
      invalidStudents
    });
  } catch (error: any) {
    console.error("Error al validar matrículas:", error);
    res.status(500).json({ msg: "Error al validar matrículas", error: error.message });
  }
}