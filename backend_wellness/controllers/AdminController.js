import dotenv from "dotenv";
dotenv.config();
import { handleServerError } from "../helpers/handleServerError.js";
import { prisma } from "../index.js";
import { checkPassword, hashPassword } from "../functions/passwordFunctions.js";
import generarJWT from "../helpers/generarJWT.js";
import { DateTime } from "luxon";
import { v2 as cloudinary } from "cloudinary";
cloudinary.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET,
});
export const login = async (req, res) => {
    const { email, password } = req.body;
    try {
        const admin = await prisma.admin.findFirst({
            where: {
                email: {
                    mode: "insensitive",
                    equals: email,
                },
            },
        });
        if (!admin) {
            return res.status(404).json({ msg: "No se encontro" });
        }
        const verify = await checkPassword(password, admin.password);
        if (!verify) {
            return res.status(401).json({ msg: "Constraseña incorrecta" });
        }
        const token = generarJWT(admin.id);
        return res.json({ ...admin, token });
    }
    catch (error) {
        return handleServerError(error, "Admin", res);
    }
};
export const profile = async (req, res) => {
    const { admin } = req;
    try {
        return res.json(admin);
    }
    catch (error) {
        return handleServerError(error, "Admin", res);
    }
};
export const getAdmins = async (_req, res) => {
    try {
        const admins = await prisma.admin.findMany({
            orderBy: {
                name: "asc",
            },
        });
        return res.json(admins);
    }
    catch (error) {
        return handleServerError(error, "Administradores", res);
    }
};
export const createUser = async (req, res) => {
    const { name, email, password, role } = req.body;
    try {
        const exists = await prisma.admin.findFirst({
            where: {
                email: email,
            },
        });
        if (exists) {
            return res
                .status(404)
                .json({ msg: "El usuario ya existe en la plataforma" });
        }
        const admin = await prisma.admin.create({
            data: {
                name: name,
                email: email,
                password: await hashPassword(password),
                role: role,
            },
        });
        return res.json(admin);
    }
    catch (error) {
        return handleServerError(error, "Admin", res);
    }
};
export const updateAdmin = async (req, res) => {
    const { id, ...body } = req.body;
    const { password } = body;
    try {
        const admin = await prisma.admin.update({
            where: {
                id: id,
            },
            data: {
                ...body,
                ...(password &&
                    password !== "" && { password: await hashPassword(password) }),
            },
        });
        return res.json({ msg: "Administrador editado correctamente" });
    }
    catch (error) {
        return handleServerError(error, "Administradores", res);
    }
};
export const deleteAdmin = async (req, res) => {
    const { id } = req.params;
    try {
        await prisma.admin.delete({
            where: {
                id: +id,
            },
        });
        return res.json({ msg: "Admin eliminado correctamente" });
    }
    catch (error) {
        return handleServerError(error, "Administradores", res);
    }
};
export async function adminNextClass(req, res) {
    const { admin } = req;
    try {
        if (!admin) {
            return res.status(404).json({ msg: "No se proporciono el administrador, vuelve a iniciar sesión" });
        }
        const reservable = await prisma.reservable.findFirst({
            where: {
                adminId: admin.id,
                init_date: {
                    gte: DateTime.now().minus({ minutes: 10 }).toJSDate()
                }
            },
            orderBy: {
                init_date: "asc"
            }
        });
        if (!reservable) {
            return res.status(404).json({ msg: "No hay reservables proximos" });
        }
        return res.json({ reservableId: reservable.id });
    }
    catch (error) {
        return handleServerError(error, "Administradores", res);
    }
}
export const uploadCarouselImage = async (req, res) => {
    try {
        const { path } = req.file;
        const lastImage = await prisma.carouselImage.findFirst({
            orderBy: {
                order: "desc",
            },
        });
        const image = await cloudinary.uploader.upload(path, {
            folder: "imagenCarrusel",
            quality: "auto:eco",
        });
        const savedImage = await prisma.carouselImage.create({
            data: {
                url: image.secure_url,
                image_id: image.public_id,
                order: lastImage ? lastImage.order + 1 : 1,
            },
        });
        return res.status(200).json({
            imageUrl: savedImage.url,
            imageId: savedImage.image_id,
        });
    }
    catch (error) {
        return handleServerError(error, "Admin", res);
    }
};
export const deleteCarouselImage = async (req, res) => {
    try {
        const { id } = req.params;
        const imageDelete = await prisma.carouselImage.delete({
            where: {
                id: +id,
            },
        });
        await cloudinary.uploader.destroy(imageDelete.image_id);
        await prisma.carouselImage.updateMany({
            where: {
                order: {
                    gt: imageDelete.order,
                },
            },
            data: {
                order: {
                    decrement: 1,
                },
            },
        });
        return res.status(200).json({ msg: "Imagen eliminada correctamente" });
    }
    catch (error) {
        return handleServerError(error, "Admin", res);
    }
};
export const updateCarouselImage = async (req, res) => {
    try {
        const { id, order } = req.body;
        console.log(id, order);
        const image = await prisma.carouselImage.findFirst({
            where: {
                id: id,
            },
        });
        const imageOrder = await prisma.carouselImage.findFirst({
            where: {
                order: order,
            },
        });
        if (!image || !imageOrder) {
            return res.status(404).json({ msg: "No se encontro la imagen" });
        }
        await prisma.carouselImage.update({
            where: {
                id: imageOrder.id,
            },
            data: {
                order: image.order,
            },
        });
        await prisma.carouselImage.update({
            where: {
                id: id,
            },
            data: {
                order: order,
            },
        });
        return res.status(200).json({ msg: "Imagen actualizada correctamente" });
    }
    catch (error) {
        return handleServerError(error, "Admin", res);
    }
};
export const getCarouselImages = async (_req, res) => {
    try {
        const images = await prisma.carouselImage.findMany({
            orderBy: {
                order: "asc",
            },
        });
        return res.json(images);
    }
    catch (error) {
        return handleServerError(error, "Admin", res);
    }
};
