import { Request, Response } from "express";
import { ReqAdmin, ReqUser } from "../types/GeneralTypes";
import { handleServerError } from "../helpers/handleServerError.js";
import { prisma } from "../index.js";
import { checkPassword, hashPassword } from "../functions/passwordFunctions.js";

import {
  LoginRT,
  RegisterRT,
  ReserveRT,
} from "../types/RequestTypes/UserControllerRT";

import generarJWT from "../helpers/generarJWT.js";

// Endpoint para crear un usuario
export const create = async (req: Request, res: Response) => {
  const { password, ...data } = req.body as RegisterRT;

  try {
    // Primero verificamos que su matricula este registrada en la base de datos
    const matricula = await prisma.user.findFirst({
      where: {
        registration: {
          equals: data.registration,
          mode: "insensitive",
        },
      },
    });

    // Si no esta su matricula le indicamos que solo se pueden registrar usuarios con matricula
    if (!matricula) {
      return res
        .status(400)
        .json({
          msg: "Solo se pueden registrar usuarios con matricula registrada",
        });
    }

    // Si si esta entonces le colocamos contraseña al usuario

    const user = await prisma.user.update({
      where: {
        registration: matricula.registration,
      },
      data: {
        password: await hashPassword(password),
      },
    });
    // Devolvemos el usuario junto con el token
    const token = generarJWT(user.id);
    return res.json({ ...user, token });
  } catch (error: any) {
    return handleServerError(error, "User", res);
  }
};

export const login = async (req: Request, res: Response) => {
  const { registration, password } = req.body as LoginRT;

  try {
    // Buscamos si esta la matricula
    const user = await prisma.user.findFirst({
      where: {
        registration: {
          mode: "insensitive",
          equals: registration,
        },
      },
    });

    if (!user) {
      return res.status(404).json({ msg: "No se encontro la matricula" });
    }

    // Si esta la matricula verificamos si tiene una contraseña colocada
    if (!user.password) {
      return res
        .status(401)
        .json({
          msg: "Necesitas registrar una contraseña primero, ve a la seccion de registro",
        });
    }

    const verify = await checkPassword(password, user.password ?? "");
    if (!verify) {
      return res.status(401).json({ msg: "Constraseña incorrecta" });
    }
    const token = generarJWT(user.id);
    return res.json({ ...user, token });
  } catch (error: any) {
    return handleServerError(error, "Admin", res);
  }
};

export const profile = async (req: ReqUser, res: Response) => {
  const { user } = req;

  return res.json(user);
};

export async function searchUser(req: ReqAdmin, res: Response) {
  const { query } = req.params;

  // Quitamos los espacios en blanco y los %20
  const cleanedQuery = query.replace(/%20/g, " ").trim();

  try {
    const users = await prisma.user.findMany({
      where: {
        OR: [
          {
            registration: {
              contains: cleanedQuery,
              mode: "insensitive",
            },
          },
          {
            name: {
              contains: cleanedQuery,
              mode: "insensitive",
            },
          },
        ],
        deleted: false // Solo incluimos los usuarios no eliminados
      },
      take: 6,
    });

    return res.json(users);
  } catch (error: any) {
    return handleServerError(error, "Admin", res);
  }
}
