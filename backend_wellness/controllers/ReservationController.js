import { handleServerError } from "../helpers/handleServerError.js";
import { prisma } from "../index.js";
import { DateTime } from "luxon";
import { toleranceReserveMinutes } from "../constants/index.js";
import { Parser } from "json2csv";
export const userCreateReservation = async (req, res) => {
    const { id } = req.params;
    const { user } = req;
    if (!user) {
        return res.status(404).json({ msg: "No se encontro el usuario" });
    }
    try {
        const reservable = await prisma.reservable.findUnique({
            where: {
                id: +id,
            },
            include: {
                reservations: {
                    where: {
                        type: "ONLINE",
                        status: {
                            not: "CANCELED",
                        },
                    },
                },
                space: {
                    include: {
                        closed_spaces: {
                            where: {
                                AND: [
                                    {
                                        start_date: {
                                            lt: new Date(),
                                        },
                                    },
                                    {
                                        end_date: {
                                            gt: new Date(),
                                        },
                                    },
                                ],
                            },
                        },
                    },
                },
            },
        });
        if (!reservable) {
            return res.status(404).json({ msg: "No se encontro el reservable" });
        }
        if (reservable.space.closed_spaces.length > 0) {
            return res.status(400).json({ msg: "El espacio esta cerrado" });
        }
        if (reservable.reservations.length >= reservable.onlineQuota) {
            return res
                .status(400)
                .json({ msg: "No hay espacio disponible, intenta en otro horario" });
        }
        const reservableDate = DateTime.fromJSDate(reservable.init_date).toJSDate();
        let now = DateTime.now().plus({ day: 1 }).toJSDate();
        if (now <= reservableDate) {
            return res.status(400).json({
                msg: "No puedes reservar con menos de 24 horas de anticipación",
            });
        }
        now = DateTime.now().toJSDate();
        const reservableToleranceDate = DateTime.fromJSDate(reservable.init_date)
            .plus({ minutes: toleranceReserveMinutes })
            .toJSDate();
        if (now > reservableToleranceDate) {
            return res.status(400).json({
                msg: `No puedes reservar despues de ${toleranceReserveMinutes} minutos de la hora de inicio`,
            });
        }
        await prisma.reservation.create({
            data: {
                userId: user?.id,
                reservableId: +id,
                reservation_date: reservable.init_date,
                type: "ONLINE",
            },
        });
        return res.status(200).json({ msg: "Reservación creada correctamente" });
    }
    catch (error) {
        return handleServerError(error, "Usuario", res);
    }
};
export async function adminCreateReservation(req, res) {
    const { reservableId, userId } = req.body;
    try {
        const reservable = await prisma.reservable.findUnique({
            where: {
                id: reservableId,
            },
            include: {
                reservations: {
                    where: {
                        type: "PRESENTIAL",
                        status: {
                            not: "CANCELED",
                        },
                    },
                },
                space: {
                    include: {
                        closed_spaces: {
                            where: {
                                AND: [
                                    {
                                        start_date: {
                                            lt: new Date(),
                                        },
                                    },
                                    {
                                        end_date: {
                                            gt: new Date(),
                                        },
                                    },
                                ],
                            },
                        },
                    },
                },
            },
        });
        if (!reservable) {
            return res.status(404).json({ msg: "No se encontro el reservable" });
        }
        if (reservable.space.closed_spaces.length > 0) {
            return res.status(400).json({ msg: "El espacio esta cerrado" });
        }
        if (reservable.reservations.length >= reservable.quota) {
            return res
                .status(400)
                .json({ msg: "No hay espacio disponible, intenta en otro horario" });
        }
        await prisma.reservation.create({
            data: {
                userId,
                reservableId,
                reservation_date: reservable.init_date,
                type: "PRESENTIAL",
            },
        });
        return res.status(200).json({ msg: "Reservación creada correctamente" });
    }
    catch (error) {
        return handleServerError(error, "Usuario", res);
    }
}
export const userReservations = async (req, res) => {
    const { user } = req;
    if (!user) {
        return res.status(404).json({ msg: "No se encontro el usuario" });
    }
    try {
        const reserves = await prisma.reservation.findMany({
            where: {
                userId: user.id,
                reservation_date: {
                    gte: new Date(),
                },
            },
            include: {
                reservable: {
                    include: {
                        space: true,
                    },
                },
            },
        });
        return res.status(200).json(reserves);
    }
    catch (error) {
        return handleServerError(error, "Usuario", res);
    }
};
export const adminGetAllReservations = async (req, res) => {
    const { reservableId } = req.params;
    try {
        const reservations = await prisma.reservation.findMany({
            where: {
                reservableId: +reservableId,
            },
            include: {
                user: true,
            },
        });
        return res.json(reservations);
    }
    catch (error) {
        return handleServerError(error, "Reservation", res);
    }
};
export const adminUpdateReservation = async (req, res) => {
    const { id, ...data } = req.body;
    try {
        await prisma.reservation.update({
            where: {
                id: +id,
            },
            data,
        });
        return res.json({ msg: "Reservación actualizada correctamente" });
    }
    catch (error) {
        return handleServerError(error, "Reservation", res);
    }
};
export const userDeleteReservation = async (req, res) => {
    const { id } = req.params;
    const { user } = req;
    try {
        const reservation = await prisma.reservation.findUnique({
            where: {
                id: +id,
            },
        });
        if (!reservation) {
            return res.status(404).json({ msg: "No se encontro la reservación" });
        }
        if (reservation.userId !== user?.id) {
            return res
                .status(401)
                .json({ msg: "No tienes permiso para borrar esta reservación" });
        }
        await prisma.reservation.delete({
            where: {
                id: +id,
            },
        });
        return res.json({ msg: "Reservación eliminada correctamente" });
    }
    catch (error) {
        return handleServerError(error, "Reservation", res);
    }
};
export async function adminDownloadReservations(req, res) {
    const { startDate, endDate } = req.body;
    try {
        const start = DateTime.fromISO(startDate)
            .startOf("day")
            .toJSDate();
        const end = DateTime.fromISO(endDate)
            .endOf("day")
            .toJSDate();
        const reservations = await prisma.reservation.findMany({
            where: {
                AND: [
                    {
                        reservation_date: {
                            gte: start
                        }
                    },
                    {
                        reservation_date: {
                            lte: end
                        }
                    }
                ]
            },
            include: {
                user: true,
                reservable: {
                    include: {
                        space: true
                    }
                }
            }
        });
        const flattenedData = reservations.map(reservation => {
            const { user, userId, reservableId, reservable, ...rest } = reservation;
            return {
                ...rest,
                alumno: user?.registration ?? "Alumno Eliminado",
                espacio: reservable?.space?.name ?? "Espacio eliminado",
            };
        });
        if (flattenedData.length > 0) {
            const reservationsCsv = new Parser({ encoding: "utf-8" }).parse(flattenedData);
            res.setHeader("Content-Disposition", "attachment; filename=reservaciones.csv");
            res.setHeader("Content-Type", "text/csv ; charset=UTF-8");
            return res.send(reservationsCsv);
        }
        return res.status(500).json({
            error: "No hay registros en la tabla Asistencia para exportar.",
        });
    }
    catch (error) {
        return handleServerError(error, "Reservation", res);
    }
}
