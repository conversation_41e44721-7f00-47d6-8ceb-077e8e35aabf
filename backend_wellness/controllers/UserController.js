import { handleServerError } from "../helpers/handleServerError.js";
import { prisma } from "../index.js";
import { checkPassword, hashPassword } from "../functions/passwordFunctions.js";
import generarJWT from "../helpers/generarJWT.js";
export const create = async (req, res) => {
    const { password, ...data } = req.body;
    try {
        const matricula = await prisma.user.findFirst({
            where: {
                registration: {
                    equals: data.registration,
                    mode: "insensitive",
                },
            },
        });
        if (!matricula) {
            return res
                .status(400)
                .json({
                msg: "Solo se pueden registrar usuarios con matricula registrada",
            });
        }
        const user = await prisma.user.update({
            where: {
                registration: matricula.registration,
            },
            data: {
                password: await hashPassword(password),
            },
        });
        const token = generarJWT(user.id);
        return res.json({ ...user, token });
    }
    catch (error) {
        return handleServerError(error, "User", res);
    }
};
export const login = async (req, res) => {
    const { registration, password } = req.body;
    try {
        const user = await prisma.user.findFirst({
            where: {
                registration: {
                    mode: "insensitive",
                    equals: registration,
                },
            },
        });
        if (!user) {
            return res.status(404).json({ msg: "No se encontro la matricula" });
        }
        if (!user.password) {
            return res
                .status(401)
                .json({
                msg: "Necesitas registrar una contraseña primero, ve a la seccion de registro",
            });
        }
        const verify = await checkPassword(password, user.password ?? "");
        if (!verify) {
            return res.status(401).json({ msg: "Constraseña incorrecta" });
        }
        const token = generarJWT(user.id);
        return res.json({ ...user, token });
    }
    catch (error) {
        return handleServerError(error, "Admin", res);
    }
};
export const profile = async (req, res) => {
    const { user } = req;
    return res.json(user);
};
export async function searchUser(req, res) {
    const { query } = req.params;
    const cleanedQuery = query.replace(/%20/g, " ").trim();
    try {
        const users = await prisma.user.findMany({
            where: {
                OR: [
                    {
                        registration: {
                            contains: cleanedQuery,
                            mode: "insensitive",
                        },
                    },
                    {
                        name: {
                            contains: cleanedQuery,
                            mode: "insensitive",
                        },
                    },
                ],
                deleted: false
            },
            take: 6,
        });
        return res.json(users);
    }
    catch (error) {
        return handleServerError(error, "Admin", res);
    }
}
