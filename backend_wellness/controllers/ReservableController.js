import { handleServerError } from "../helpers/handleServerError.js";
import { prisma } from "../index.js";
import { v4 as uuidv4 } from "uuid";
import { DateTime } from "luxon";
import { toleranceReserveMinutes } from "../constants/index.js";
export const createReservable = async (req, res) => {
    function adjustDayIndex(index) {
        let adjustedIndex = index - 1;
        if (index === 0) {
            adjustedIndex = 6;
        }
        return adjustedIndex;
    }
    try {
        const data = req.body;
        const newData = {
            spaceId: data.spaceId,
            init_date: new Date(data.init_date),
            end_date: new Date(data.end_date),
            adminId: data.adminId,
            quota: data.quota,
            onlineQuota: data.onlineQuota,
            color: data.color,
            booking: data.booking,
        };
        if (data.repeats) {
            let fechaActual = new Date(data.init_date);
            let fechaFinal = new Date(data.end_date);
            const uuid = uuidv4();
            while (fechaActual <= new Date(data.repeticion_fecha)) {
                if (data.repeticion.includes(adjustDayIndex(fechaActual.getDay())) ||
                    fechaActual.toISOString() == new Date(data.init_date).toISOString()) {
                    newData.init_date = fechaActual;
                    newData.end_date = fechaFinal;
                    await prisma.reservable.create({
                        data: {
                            ...newData,
                            reservableGroup: uuid,
                        },
                    });
                }
                fechaActual.setDate(fechaActual.getDate() + 1);
                fechaFinal.setDate(fechaFinal.getDate() + 1);
            }
        }
        else {
            await prisma.reservable.create({
                data: newData,
            });
        }
        return res.status(200).json({ msg: "Area reservable!!" });
    }
    catch (error) {
        return handleServerError(error, "Reservable", res);
    }
};
export const updateReservable = async (req, res) => {
    const body = req.body;
    try {
        const { all, id, ...data } = body;
        if (all) {
            const ogReservable = await prisma.reservable.findUnique({
                where: {
                    id,
                },
            });
            if (!ogReservable) {
                return res.status(404).json({ msg: "No se encontro el reservable" });
            }
            const newData = {
                spaceId: data.spaceId,
                adminId: data.adminId,
                quota: data.quota,
                onlineQuota: data.onlineQuota,
                color: data.color,
                booking: data.booking,
            };
            const initTime = new Date(data.init_date).getHours();
            const endTime = new Date(data.end_date).getHours();
            const reservables = await prisma.reservable.findMany({
                where: {
                    reservableGroup: ogReservable.reservableGroup,
                },
                include: {
                    reservations: true,
                },
            });
            await prisma.$transaction(async (tx) => {
                for (const reservable of reservables) {
                    const newInitDate = new Date(reservable.init_date);
                    newInitDate.setHours(initTime);
                    const newEndDate = new Date(reservable.end_date);
                    newEndDate.setHours(endTime);
                    if (reservable.reservations.length > data.quota + data.onlineQuota) {
                        continue;
                    }
                    await tx.reservable.update({
                        where: {
                            id: reservable.id,
                        },
                        data: {
                            ...newData,
                            init_date: newInitDate,
                            end_date: newEndDate,
                        },
                    });
                }
            });
            return res.json({
                msg: "Se modificaron todos los reservables correctamente",
            });
        }
        const reservable = await prisma.reservable.findFirstOrThrow({
            where: {
                id: Number(id),
            },
            include: {
                reservations: true,
            },
        });
        if (reservable.reservations.length > body.quota) {
            return res.status(400).json({
                msg: "No se puede modificar la cuota a una menor que la cantidad de reservaciones actuales",
            });
        }
        await prisma.reservable.update({
            where: {
                id: Number(id),
            },
            data,
        });
        return res.json({ msg: "Reservable editado correctamente" });
    }
    catch (error) {
        return handleServerError(error, "Reservable", res);
    }
};
export const deteteReservables = async (req, res) => {
    const { id } = req.params;
    const all = req.params.all === "true";
    try {
        if (all) {
            const reservable = await prisma.reservable.findFirstOrThrow({
                where: {
                    id: Number(id),
                },
            });
            await prisma.reservable.deleteMany({
                where: {
                    reservableGroup: reservable.reservableGroup,
                },
            });
            return res.json({
                msg: "Se eliminaron todos los reservables correctamente",
            });
        }
        await prisma.reservable.delete({
            where: {
                id: Number(id),
            },
        });
        return res.json({ msg: "Se eliminó el reservable correctamente" });
    }
    catch (error) {
        return handleServerError(error, "Reservable", res);
    }
};
export const getReservable = async (req, res) => {
    const { id } = req.params;
    try {
        const reservable = await prisma.reservable.findFirst({
            where: {
                id: Number(id),
            },
            include: {
                admin: true,
                reservations: {
                    where: {
                        status: {
                            not: "CANCELED",
                        },
                    },
                },
                space: true,
            },
        });
        if (!reservable) {
            return res.status(404).json({ msg: "No se encontro el reservable" });
        }
        const onlineReservations = reservable.reservations.filter((reservation) => reservation.type === "ONLINE").length;
        const presentialReservations = reservable.reservations.length - onlineReservations;
        const reservableResponse = {
            ...reservable,
            presentialQuotaString: `${presentialReservations}/${reservable.quota}`,
            onlineQuotaString: `${onlineReservations}/${reservable.onlineQuota}`,
        };
        return res.json(reservableResponse);
    }
    catch (error) {
        return handleServerError(error, "Reservable", res);
    }
};
export const getReservables = async (req, res) => {
    try {
        const { spaceId } = req.params;
        const oneWeek = DateTime.now().plus({ days: 7 }).toJSDate();
        const currentDateMinusTime = DateTime.now()
            .minus({ minutes: toleranceReserveMinutes })
            .toJSDate();
        const reservables = await prisma.reservable.findMany({
            where: {
                spaceId: Number(spaceId),
                end_date: {
                    lte: oneWeek,
                },
                init_date: {
                    gte: currentDateMinusTime,
                },
                booking: true,
            },
            include: {
                reservations: true,
                admin: true,
            },
            orderBy: {
                init_date: "asc",
            },
        });
        if (!reservables) {
            return res.status(404).json({ msg: "Esa area no cuenta con espacios" });
        }
        const groupedReservables = new Map();
        for (const reservable of reservables) {
            const date = DateTime.fromJSDate(reservable.init_date).setZone("America/Monterrey").toFormat("yyyy-MM-dd");
            if (!groupedReservables.has(date)) {
                groupedReservables.set(date, []);
            }
            groupedReservables.get(date).push(reservable);
        }
        const sortedReservables = Array.from(groupedReservables.entries()).sort((a, b) => new Date(a[0]).getTime() - new Date(b[0]).getTime());
        return res.json(sortedReservables);
    }
    catch (error) {
        return handleServerError(error, "Reservable", res);
    }
};
export const displayReservable = async (_req, res) => {
    try {
        const reservables = await prisma.reservable.findMany({
            where: {
                init_date: {
                    lt: new Date(),
                },
                end_date: {
                    gt: new Date(),
                },
            },
            include: {
                reservations: {},
                admin: {},
                space: {},
            },
        });
        if (!reservables) {
            return res
                .status(404)
                .json({ msg: "Error al conseguir los reservables" });
        }
        return res.status(200).json(reservables.map((reservable) => {
            const { init_date, end_date, reservations, quota, admin, space } = reservable;
            return {
                init_date,
                end_date,
                quota,
                actual: reservations.length,
                coach: admin?.name ?? "Admin Borrado",
                nombre: space.name,
                foto: space.image,
            };
        }));
    }
    catch (error) {
        return handleServerError(error, "Reservable", res);
    }
};
export const getAllReservables = async (req, res) => {
    const { isBooking, adminId, spaceId } = req.query;
    try {
        const reservables = await prisma.reservable.findMany({
            include: {
                space: true,
                admin: true,
            },
            where: {
                booking: isBooking === "true" ? true : false,
                ...(adminId ? { adminId: Number(adminId) } : {}),
                ...(spaceId ? { spaceId: Number(spaceId) } : {}),
            },
        });
        return res.status(200).json(reservables.map((reservable) => {
            const { admin, ...data } = reservable;
            return {
                coach: admin?.name ?? "No asignado",
                ...data,
            };
        }));
    }
    catch (error) {
        return handleServerError(error, "Reservable", res);
    }
};
