import { handleServerError } from "../helpers/handleServerError.js";
import { prisma } from "../index.js";
import cloudinary from "../config/cloudinaryConfig.js";
import fs from "fs";
import csvParser from "csv-parser";
import { normalizeRowKeys, validateMaterialRow } from "../functions/materialCsvFunctions.js";
export const createMaterial = async (req, res) => {
    let material, image;
    try {
        if (!req.file) {
            return res.status(400).json({ msg: "No se proporcionó ninguna imagen" });
        }
        const { name, quantity, minMatriculas, leadTimeDays, rules, replacementCost } = req.body;
        image = await cloudinary.uploader.upload(req.file.path, {
            folder: `materiales`,
            quality: "auto:eco",
        });
        if (fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }
        material = await prisma.material.create({
            data: {
                name,
                quantity: +quantity,
                minMatriculas: +minMatriculas,
                leadTimeDays: +leadTimeDays,
                rules,
                replacementCost: +replacementCost,
                image: image.secure_url,
                image_id: image.public_id,
                deleted: false
            }
        });
        return res.status(200).json({ msg: "Material creado correctamente" });
    }
    catch (error) {
        console.error("Error al crear material:", error);
        if (material) {
            await prisma.material.delete({
                where: {
                    id: material.id
                }
            });
        }
        if (image && image.public_id) {
            await cloudinary.uploader.destroy(image.public_id);
        }
        if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }
        return handleServerError(error, "Material", res);
    }
};
export const getAllMaterials = async (_req, res) => {
    try {
        const materials = await prisma.material.findMany({
            where: {
                deleted: false
            },
            orderBy: {
                name: 'asc'
            }
        });
        return res.json(materials);
    }
    catch (error) {
        console.error("Error al obtener materiales:", error);
        return handleServerError(error, "Material", res);
    }
};
export const getAvailableMaterials = async (_req, res) => {
    try {
        const materials = await prisma.material.findMany({
            where: {
                deleted: false,
                quantity: {
                    gt: 0
                }
            },
            orderBy: {
                name: 'asc'
            }
        });
        return res.json(materials);
    }
    catch (error) {
        console.error("Error al obtener materiales disponibles:", error);
        return handleServerError(error, "Material", res);
    }
};
export const getMaterialById = async (req, res) => {
    try {
        const { id } = req.params;
        const material = await prisma.material.findUnique({
            where: {
                id: parseInt(id)
            }
        });
        if (!material) {
            return res.status(404).json({ msg: "Material no encontrado" });
        }
        if (material.deleted) {
            return res.status(410).json({ msg: "Este material ha sido eliminado" });
        }
        return res.json(material);
    }
    catch (error) {
        console.error("Error al obtener material:", error);
        return handleServerError(error, "Material", res);
    }
};
export const updateMaterial = async (req, res) => {
    let updatedImage;
    try {
        const { id } = req.params;
        const { name, quantity, minMatriculas, leadTimeDays, rules, replacementCost } = req.body;
        const material = await prisma.material.findUnique({
            where: {
                id: parseInt(id)
            }
        });
        if (!material) {
            return res.status(404).json({ msg: "Material no encontrado" });
        }
        const updateData = {
            name,
            quantity: +quantity,
            minMatriculas: +minMatriculas,
            leadTimeDays: +leadTimeDays,
            rules,
            replacementCost: +replacementCost
        };
        if (req.file) {
            updatedImage = await cloudinary.uploader.upload(req.file.path, {
                folder: `materiales`,
                quality: "auto:eco",
            });
            if (fs.existsSync(req.file.path)) {
                fs.unlinkSync(req.file.path);
            }
            if (material.image_id) {
                await cloudinary.uploader.destroy(material.image_id);
            }
            updateData.image = updatedImage.secure_url;
            updateData.image_id = updatedImage.public_id;
        }
        const updated = await prisma.material.update({
            where: {
                id: parseInt(id)
            },
            data: updateData
        });
        return res.json({ msg: "Material actualizado correctamente", material: updated });
    }
    catch (error) {
        console.error("Error al actualizar material:", error);
        if (updatedImage && updatedImage.public_id) {
            await cloudinary.uploader.destroy(updatedImage.public_id);
        }
        if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }
        return handleServerError(error, "Material", res);
    }
};
export const deleteMaterial = async (req, res) => {
    try {
        const { id } = req.params;
        const material = await prisma.material.findUnique({
            where: {
                id: parseInt(id)
            }
        });
        if (!material) {
            return res.status(404).json({ msg: "Material no encontrado" });
        }
        const activeLoansResult = await prisma.$queryRaw `
            SELECT COUNT(*) as count
            FROM "loan"
            WHERE "materialId" = ${parseInt(id)}
            AND "status" IN ('PENDING', 'AWAITING_PICKUP', 'ON_LOAN')
        `;
        const count = Number(activeLoansResult[0].count);
        if (count > 0) {
            return res.status(400).json({
                msg: "No se puede eliminar este material porque tiene préstamos activos"
            });
        }
        await prisma.material.update({
            where: {
                id: parseInt(id)
            },
            data: {
                deleted: true
            }
        });
        return res.json({ msg: "Material eliminado correctamente" });
    }
    catch (error) {
        console.error("Error al eliminar material:", error);
        return handleServerError(error, "Material", res);
    }
};
export const uploadCsv = async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ msg: "No se proporcionó ningún archivo" });
        }
        if (req.file.mimetype !== "text/csv" && !req.file.originalname.endsWith('.csv')) {
            return res.status(400).json({ msg: "El archivo debe ser un CSV" });
        }
        const validMaterials = [];
        let totalRows = 0;
        let validRows = 0;
        let invalidRows = 0;
        const processFile = () => {
            return new Promise((resolve, reject) => {
                fs.createReadStream(req.file.path)
                    .pipe(csvParser())
                    .on('data', (row) => {
                    totalRows++;
                    const normalizedRow = normalizeRowKeys(row);
                    const validatedRow = validateMaterialRow(normalizedRow);
                    if (validatedRow) {
                        validMaterials.push(validatedRow);
                        validRows++;
                    }
                    else {
                        invalidRows++;
                    }
                })
                    .on('end', () => {
                    resolve();
                })
                    .on('error', (error) => {
                    reject(error);
                });
            });
        };
        await processFile();
        if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }
        if (validMaterials.length === 0) {
            return res.status(400).json({
                msg: "No se encontraron materiales válidos en el archivo CSV"
            });
        }
        let createdCount = 0;
        for (const material of validMaterials) {
            try {
                await prisma.material.create({
                    data: {
                        name: material.name,
                        quantity: parseInt(material.quantity),
                        minMatriculas: parseInt(material.minMatriculas),
                        leadTimeDays: parseInt(material.leadTimeDays),
                        rules: material.rules,
                        replacementCost: parseFloat(material.replacementCost),
                        image: material.image || "https://res.cloudinary.com/dkddd5aky/image/upload/v1711488618/materiales/default-material_yvpwgz.jpg",
                        image_id: "default",
                        deleted: false
                    }
                });
                createdCount++;
            }
            catch (error) {
                console.error(`Error al crear material ${material.name}:`, error);
            }
        }
        return res.status(200).json({
            msg: `Se importaron ${createdCount} materiales correctamente`,
            stats: {
                totalRows,
                validRows,
                invalidRows,
                createdCount
            }
        });
    }
    catch (error) {
        console.error("Error al importar materiales desde CSV:", error);
        if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }
        return handleServerError(error, "Material", res);
    }
};
