import { PrismaClient, LoanStatus } from "@prisma/client";
const prisma = new PrismaClient();
export async function createLoan(req, res) {
    try {
        const { materialId, responsibleId, studentIds, quantity, pickupDate, returnDate, pickupTime, returnTime, } = req.body;
        const material = await prisma.material.findUnique({
            where: { id: parseInt(materialId) }
        });
        if (!material) {
            return res.status(404).json({ msg: "Material no encontrado" });
        }
        if (material.quantity < parseInt(quantity)) {
            return res.status(400).json({ msg: "No hay suficiente cantidad disponible de este material" });
        }
        if (studentIds.length < material.minMatriculas) {
            return res.status(400).json({
                msg: `Se requieren al menos ${material.minMatriculas} matrículas para este material`
            });
        }
        const loan = await prisma.loan.create({
            data: {
                materialId: parseInt(materialId),
                responsibleId,
                studentIds,
                quantity: parseInt(quantity),
                pickupDate: new Date(pickupDate),
                returnDate: new Date(returnDate),
                pickupTime,
                returnTime,
                status: LoanStatus.PENDING,
            },
        });
        res.json({ msg: "Préstamo creado", loan });
    }
    catch (error) {
        console.error("Error al crear préstamo:", error);
        res.status(500).json({ msg: "Error al crear préstamo", error: error.message });
    }
}
export async function getAllLoans(_, res) {
    try {
        console.log("Getting all loans...");
        const loans = await prisma.loan.findMany({
            include: {
                material: {
                    select: {
                        name: true,
                        image: true
                    }
                }
            },
            orderBy: {
                createdAt: 'desc'
            }
        });
        const formattedLoans = loans.map(loan => ({
            ...loan,
            material_name: loan.material.name,
            material_image: loan.material.image
        }));
        console.log("Loans fetched successfully:", formattedLoans.length);
        res.json(formattedLoans);
    }
    catch (error) {
        console.error("Error in getAllLoans:", error.message, error.stack);
        res.status(500).json({ msg: "Error al obtener préstamos", error: error.message });
    }
}
export async function updateLoanStatus(req, res) {
    try {
        console.log("Updating loan status...");
        const { id } = req.params;
        const { status, notes } = req.body;
        const loanId = parseInt(id);
        const currentLoan = await prisma.loan.findUnique({
            where: { id: loanId },
            include: {
                material: {
                    select: {
                        id: true,
                        quantity: true
                    }
                }
            }
        });
        if (!currentLoan) {
            return res.status(404).json({ msg: "Préstamo no encontrado" });
        }
        const formattedLoan = {
            ...currentLoan,
            material_quantity: currentLoan.material.quantity
        };
        console.log("Current loan:", formattedLoan);
        try {
            if (status === LoanStatus.ON_LOAN && formattedLoan.status !== LoanStatus.ON_LOAN) {
                await prisma.material.update({
                    where: { id: formattedLoan.materialId },
                    data: { quantity: { decrement: formattedLoan.quantity } }
                });
                console.log("Material quantity decreased");
            }
            if ((formattedLoan.status === LoanStatus.ON_LOAN || formattedLoan.status === LoanStatus.LATE) && status === LoanStatus.RETURNED) {
                await prisma.material.update({
                    where: { id: formattedLoan.materialId },
                    data: { quantity: { increment: formattedLoan.quantity } }
                });
                console.log("Material quantity increased");
            }
            const updateData = {
                status: status,
                penaltyApplied: status === LoanStatus.LATE
            };
            if (notes) {
                updateData.penaltyNotes = notes;
            }
            await prisma.loan.update({
                where: { id: loanId },
                data: updateData
            });
        }
        catch (error) {
            console.error("Error updating with Prisma methods:", error);
            throw error;
        }
        const updatedLoan = await prisma.loan.findUnique({
            where: { id: loanId },
            include: {
                material: {
                    select: {
                        name: true,
                        image: true
                    }
                }
            }
        });
        const updated = updatedLoan ? {
            ...updatedLoan,
            material_name: updatedLoan.material.name,
            material_image: updatedLoan.material.image
        } : null;
        console.log("Loan updated successfully");
        res.json({ msg: "Estado actualizado", updated });
    }
    catch (error) {
        console.error("Error al actualizar estado:", error);
        res.status(500).json({ msg: "Error al actualizar estado", error: error.message });
    }
}
export async function getLoansByResponsible(req, res) {
    try {
        const { registration } = req.params;
        const loans = await prisma.loan.findMany({
            where: {
                responsibleId: registration
            },
            include: { material: true },
            orderBy: { createdAt: "desc" }
        });
        res.json(loans);
    }
    catch (error) {
        console.error("Error al obtener préstamos del usuario:", error);
        res.status(500).json({ msg: "Error al obtener préstamos del usuario", error: error.message });
    }
}
export async function getLoansByStatus(req, res) {
    try {
        const { status } = req.params;
        const loans = await prisma.loan.findMany({
            where: { status: status },
            include: { material: true },
            orderBy: { createdAt: "desc" }
        });
        res.json(loans);
    }
    catch (error) {
        console.error("Error al obtener préstamos por estado:", error);
        res.status(500).json({ msg: "Error al obtener préstamos por estado", error: error.message });
    }
}
export async function checkLateLoans(_req, res) {
    try {
        console.log("Checking for late loans...");
        const now = new Date();
        const lateLoans = await prisma.loan.findMany({
            where: {
                status: LoanStatus.ON_LOAN,
                returnDate: {
                    lt: now
                }
            },
            select: {
                id: true,
                materialId: true,
                responsibleId: true
            }
        });
        const formattedDate = now.toLocaleDateString('es-ES', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
        const updatePromises = lateLoans.map(loan => prisma.loan.update({
            where: { id: loan.id },
            data: {
                status: LoanStatus.LATE,
                penaltyApplied: true,
                penaltyNotes: `Préstamo marcado automáticamente como atrasado el ${formattedDate}`
            }
        }));
        await Promise.all(updatePromises);
        console.log("Late loans updated:", lateLoans.length);
        res.json({
            msg: `${lateLoans.length} préstamos marcados como atrasados`,
            lateLoans: lateLoans
        });
    }
    catch (error) {
        console.error("Error al verificar préstamos atrasados:", error);
        res.status(500).json({ msg: "Error al verificar préstamos atrasados", error: error.message });
    }
}
export async function validateStudentIds(req, res) {
    try {
        const { studentIds } = req.body;
        if (!studentIds || !Array.isArray(studentIds)) {
            return res.status(400).json({ msg: "Se requiere un array de matrículas" });
        }
        const validStudents = [];
        const invalidStudents = [];
        for (const id of studentIds) {
            const student = await prisma.user.findUnique({
                where: { registration: id }
            });
            if (student) {
                validStudents.push({
                    registration: id,
                    name: student.name
                });
            }
            else {
                invalidStudents.push(id);
            }
        }
        res.json({
            valid: invalidStudents.length === 0,
            validStudents,
            invalidStudents
        });
    }
    catch (error) {
        console.error("Error al validar matrículas:", error);
        res.status(500).json({ msg: "Error al validar matrículas", error: error.message });
    }
}
