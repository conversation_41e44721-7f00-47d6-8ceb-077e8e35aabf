import { Request, Response } from "express";
import { ReqAdmin } from "../types/GeneralTypes";
import { handleServerError } from "../helpers/handleServerError.js";
import { prisma } from "../index.js";

import {
  CreateReservableRT,
  DeleteReservableRT,
  GetReservableRT,
  UpdateReservableRT,
} from "../types/RequestTypes/ReservableControllerRT";

import { v4 as uuidv4 } from "uuid";
import { DateTime } from "luxon";
import { toleranceReserveMinutes } from "../constants/index.js";

export const createReservable = async (req: Request, res: Response) => {
  // Función para ajustar el indice del dia
  function adjustDayIndex(index: number) {
    let adjustedIndex = index - 1;

    // Si el indice es 0, lo ajustamos a 6
    if (index === 0) {
      adjustedIndex = 6;
    }

    return adjustedIndex;
  }

  try {
    // Obtenemos los datos del cuerpo de la petición
    const data = req.body as CreateReservableRT;

    // Creamos un nuevo objeto con los datos necesarios
    const newData = {
      spaceId: data.spaceId,
      init_date: new Date(data.init_date),
      end_date: new Date(data.end_date),
      adminId: data.adminId,
      quota: data.quota,
      onlineQuota: data.onlineQuota,
      color: data.color,
      booking: data.booking,
    };

    // Si es un reservable que requiere de repeticion entonces hay que manejar la lógica
    if (data.repeats) {
      // Obtenemos la fecha actual y la fecha de finalización de la repetición
      let fechaActual = new Date(data.init_date);
      let fechaFinal = new Date(data.end_date);

      // Al ser de repeticion generaremos un ID unico el cual nos servira para poder modificar todos los reservables o uno solo
      const uuid = uuidv4();

      while (fechaActual <= new Date(data.repeticion_fecha)) {
        // Si el dia de la semana de la fecha es el mismo que el del arreglo de repeticion entonces creamos el reservabile
        if (
          data.repeticion.includes(adjustDayIndex(fechaActual.getDay())) ||
          fechaActual.toISOString() == new Date(data.init_date).toISOString()
        ) {
          newData.init_date = fechaActual;
          newData.end_date = fechaFinal;
          await prisma.reservable.create({
            data: {
              ...newData,
              reservableGroup: uuid,
            },
          });
        }
        // Aumentamos en uno el dia de la fecha para la siguiente iteración
        fechaActual.setDate(fechaActual.getDate() + 1);
        fechaFinal.setDate(fechaFinal.getDate() + 1);
      }
    } else {
      // Si no es de repeticion entonces creamos el reservable
      await prisma.reservable.create({
        data: newData,
      });
    }
    return res.status(200).json({ msg: "Area reservable!!" });
  } catch (error: any) {
    return handleServerError(error, "Reservable", res);
  }
};

// Este endpoint será el responsable de editar los reservables, tiene la capacidad de editar uno o todo un grupo de reservables
export const updateReservable = async (req: Request, res: Response) => {
  // Obtenemos el body para tiparlo
  const body = req.body as UpdateReservableRT;

  try {
    // Obtenemos si quieren editar todos los reservables del grupo o solo ese
    // Igualmente separamos los datos que son únicamente de parametros del endpoint y los que son de la entidad
    const { all, id, ...data } = body;

    // Si quieren editar todos los reservables del grupo
    if (all) {
      // Buscamos el reservable original para obtener su reservableGroup
      const ogReservable = await prisma.reservable.findUnique({
        where: {
          id,
        },
      });

      if (!ogReservable) {
        return res.status(404).json({ msg: "No se encontro el reservable" });
      }

      // Creamos un nuevo objeto con los datos necesarios que todos se modifican y no necesitan tratamiento especial
      const newData = {
        spaceId: data.spaceId,
        adminId: data.adminId,
        quota: data.quota,
        onlineQuota: data.onlineQuota,
        color: data.color,
        booking: data.booking,
      };

      // Extraemos las horas de inicio y final de los eventos sin ver la fecha
      const initTime = new Date(data.init_date).getHours();
      const endTime = new Date(data.end_date).getHours();

      // Obtendremos todos los reservables para irlos editando, creamos una transacción para asegurar la integridad de los datos
      const reservables = await prisma.reservable.findMany({
        where: {
          reservableGroup: ogReservable.reservableGroup,
        },
        include: {
          reservations: true,
        },
      });

      await prisma.$transaction(async (tx) => {
        // Recorremos todos los reservables
        for (const reservable of reservables) {
          // Creamos las nuevas fechas de inicio y finalizacion
          const newInitDate = new Date(reservable.init_date);
          newInitDate.setHours(initTime);
          const newEndDate = new Date(reservable.end_date);
          newEndDate.setHours(endTime);

          // Antes de editar el reservable verificamos que no se este modificando la cuota del reservable teniendo más reservas
          if (reservable.reservations.length > data.quota + data.onlineQuota) {
            // Si resulta que tiene más reservas que la nueva cuota entonces continuamos con el siguiente reservable
            continue;
          }

          // Ahora si se puede modificar el reservable
          await tx.reservable.update({
            where: {
              id: reservable.id,
            },
            data: {
              ...newData,
              init_date: newInitDate,
              end_date: newEndDate,
            },
          });
        }
      });

      // Si la transacción se completo correctamente entonces ya indicamos que se hizo el cambio correctamente
      return res.json({
        msg: "Se modificaron todos los reservables correctamente",
      });
    }

    // Ahora considerando el caso de modificación de un reservable único

    // Obtenemos el reservable para hacer la comprobación de las reservaciones
    const reservable = await prisma.reservable.findFirstOrThrow({
      where: {
        id: Number(id),
      },
      include: {
        reservations: true,
      },
    });

    // Ahora debemos verificar que no se este modificando la cuota a una menor que la cantidad de reservaciones actuales
    if (reservable.reservations.length > body.quota) {
      return res.status(400).json({
        msg: "No se puede modificar la cuota a una menor que la cantidad de reservaciones actuales",
      });
    }

    // Ahora si se puede modificar el reservable

    await prisma.reservable.update({
      where: {
        id: Number(id),
      },
      data,
    });

    return res.json({ msg: "Reservable editado correctamente" });
  } catch (error: any) {
    return handleServerError(error, "Reservable", res);
  }
};

export const deteteReservables = async (req: Request, res: Response) => {
  // Obtenemos los parametros
  const { id } = req.params as unknown as DeleteReservableRT;

  const all = req.params.all === "true";

  try {
    // Primero verificamos si se buscan eliminar todos o solo uno
    if (all) {
      // Obtenemos el reservable para obtener el grupo de reservables
      const reservable = await prisma.reservable.findFirstOrThrow({
        where: {
          id: Number(id),
        },
      });

      // Ahora eliminamos todos los reservables
      await prisma.reservable.deleteMany({
        where: {
          reservableGroup: reservable.reservableGroup,
        },
      });

      // Si se eliminaron correctamente entonces indicamos que se eliminaron correctamente
      return res.json({
        msg: "Se eliminaron todos los reservables correctamente",
      });
    }

    // Ahora considerando el caso de eliminación de un reservable único
    await prisma.reservable.delete({
      where: {
        id: Number(id),
      },
    });

    // Si se eliminaron correctamente entonces indicamos que se eliminaron correctamente
    return res.json({ msg: "Se eliminó el reservable correctamente" });
  } catch (error: any) {
    return handleServerError(error, "Reservable", res);
  }
};

// Función encargada de devolver el reservable que el usuario haya dado click para tener su estado en vivo
export const getReservable = async (req: Request, res: Response) => {
  const { id } = req.params as unknown as GetReservableRT;

  try {
    // Con el id buscamos el reservable y lo devolvemos incluyendo al admin
    const reservable = await prisma.reservable.findFirst({
      where: {
        id: Number(id),
      },
      include: {
        admin: true,
        reservations: {
          where: {
            status: {
              not: "CANCELED",
            },
          },
        },
        space: true,
      },
    });

    // Si no se encontro el reservable entonces indicamos que no se encontro
    if (!reservable) {
      return res.status(404).json({ msg: "No se encontro el reservable" });
    }

    // calculamos cuantas reservas son online y cuantas presenciales
    const onlineReservations = reservable.reservations.filter(
      (reservation) => reservation.type === "ONLINE"
    ).length;

    const presentialReservations =
      reservable.reservations.length - onlineReservations;

    const reservableResponse = {
      ...reservable,
      presentialQuotaString: `${presentialReservations}/${reservable.quota}`,
      onlineQuotaString: `${onlineReservations}/${reservable.onlineQuota}`,
    };

    // Si se encontro entonces lo devolvemos
    return res.json(reservableResponse);
  } catch (error: any) {
    return handleServerError(error, "Reservable", res);
  }
};

export const getReservables = async (req: ReqAdmin, res: Response) => {
  try {
    const { spaceId } = req.params;

    // Creamos una fecha de hoy a una semana para la busqueda
    // const oneWeek = new Date();
    // oneWeek.setDate(oneWeek.getDate() + 7);

    const oneWeek = DateTime.now().plus({ days: 7 }).toJSDate();
    const currentDateMinusTime = DateTime.now()
      .minus({ minutes: toleranceReserveMinutes })
      .toJSDate();

    const reservables = await prisma.reservable.findMany({
      where: {
        spaceId: Number(spaceId),
        end_date: {
          lte: oneWeek,
        },
        init_date: {
          gte: currentDateMinusTime,
        },
        booking: true,
      },
      include: {
        reservations: true,
        admin: true,
      },
      orderBy: {
        init_date: "asc",
      },
    });

    if (!reservables) {
      return res.status(404).json({ msg: "Esa area no cuenta con espacios" });
    }

    // Creamos un Map para almacenar los grupos de reservables
    const groupedReservables = new Map();

    // Recorremos los reservables
    for (const reservable of reservables) {
      // Convertimos la fecha de inicio a un objeto Date y luego a una cadena de texto en formato YYYY-MM-DD
      const date = DateTime.fromJSDate(reservable.init_date).setZone("America/Monterrey").toFormat(
        "yyyy-MM-dd"
      );

      // Si el grupo no existe en el Map, lo creamos
      if (!groupedReservables.has(date)) {
        groupedReservables.set(date, []);
      }

      // Agregamos el reservable al grupo correspondiente
      groupedReservables.get(date).push(reservable);
    }

    // Convertimos el Map a un array de [key, value] y lo ordenamos por la clave (fecha)
    const sortedReservables = Array.from(groupedReservables.entries()).sort(
      (a, b) => new Date(a[0]).getTime() - new Date(b[0]).getTime()
    );

    return res.json(sortedReservables);
  } catch (error: any) {
    return handleServerError(error, "Reservable", res);
  }
};



export const displayReservable = async (_req: Request, res: Response) => {
  try {
    const reservables = await prisma.reservable.findMany({
      where: {
        init_date: {
          lt: new Date(),
        },
        end_date: {
          gt: new Date(),
        },
      },
      include: {
        reservations: {},
        admin: {},
        space: {},
      },
    });
    if (!reservables) {
      return res
        .status(404)
        .json({ msg: "Error al conseguir los reservables" });
    }
    return res.status(200).json(
      reservables.map((reservable) => {
        const { init_date, end_date, reservations, quota, admin, space } =
          reservable;
        return {
          init_date,
          end_date,
          quota,
          actual: reservations.length,
          coach: admin?.name ?? "Admin Borrado",
          nombre: space.name,
          foto: space.image,
        };
      })
    );
  } catch (error: any) {
    return handleServerError(error, "Reservable", res);
  }
};
export const getAllReservables = async (req: Request, res: Response) => {
  interface ReservablesQuery {
    isBooking: string;
    adminId?: string;
    spaceId?: string;
  }

  const { isBooking, adminId, spaceId } =
    req.query as unknown as ReservablesQuery;
    
  try {
    const reservables = await prisma.reservable.findMany({
      include: {
        space: true,
        admin: true,
      },
      where: {
        booking: isBooking === "true" ? true : false,
        ...(adminId ? { adminId: Number(adminId) } : {}),
        ...(spaceId ? { spaceId: Number(spaceId) } : {}),
      },
    });

    return res.status(200).json(
      reservables.map((reservable) => {
        const { admin, ...data } = reservable;
        return {
          coach: admin?.name ?? "No asignado",
          ...data,
        };
      })
    );
  } catch (error: any) {
    return handleServerError(error, "Reservable", res);
  }
};