import { Request, Response } from "express";
import { handleServerError } from "../helpers/handleServerError.js";
import { prisma } from "../index.js";

export async function countPerson(_req: Request, res: Response) {

    try {

        // Creamos un nuevo registro de salida en base de datos
        await prisma.personExit.create({
            data: {
                date: new Date(),
            },
        });
        
        return res.json({ msg: "Salida registrada correctamente" });
        
    } catch (error:any) {
        return handleServerError(error, "PersonCounter", res)
    }

}