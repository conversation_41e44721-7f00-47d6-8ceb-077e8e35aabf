import { Request, Response } from "express";
import { ReqAdmin, ReqUser } from "../types/GeneralTypes";
import { handleServerError } from "../helpers/handleServerError.js";
import { prisma } from "../index.js";
import { checkPassword, hashPassword } from "../functions/passwordFunctions.js";
import generarJWT from "../helpers/generarJWT.js";
import {
  AdminReserveRT,
  ReserveRT,
} from "../types/RequestTypes/UserControllerRT";
import { DateTime } from "luxon";
import { toleranceReserveMinutes } from "../constants/index.js";
import { Parser } from "json2csv";

export const userCreateReservation = async (req: ReqUser, res: Response) => {
  // Obtenemos los parametros de la url
  const { id } = req.params as unknown as ReserveRT;
  // Obtenemos el usuario que solicito la reservación
  const { user } = req;

  // Si no hay un usuario lo notificamos
  if (!user) {
    return res.status(404).json({ msg: "No se encontro el usuario" });
  }

  try {
    // Creamos una transacción para asegurarnos de que si surge algún problema se haga un rollback y se mantenga la integridad de los datos

    // Obtendremos el reservable para saber si todavía hay espacio, y que no este cerrado
    const reservable = await prisma.reservable.findUnique({
      where: {
        id: +id,
      },
      include: {
        reservations: {
          where: {
            type: "ONLINE",
            status: {
              not: "CANCELED",
            },
          },
        },
        space: {
          include: {
            closed_spaces: {
              where: {
                AND: [
                  {
                    start_date: {
                      lt: new Date(),
                    },
                  },
                  {
                    end_date: {
                      gt: new Date(),
                    },
                  },
                ],
              },
            },
          },
        },
      },
    });

    // Si no se encontro el reservable a reservar entonces lo notificamos
    if (!reservable) {
      return res.status(404).json({ msg: "No se encontro el reservable" });
    }

    // Primero verificamos que no este cerrado
    if (reservable.space.closed_spaces.length > 0) {
      return res.status(400).json({ msg: "El espacio esta cerrado" });
    }

    // Verificamos que todavía haya espacio
    if (reservable.reservations.length >= reservable.onlineQuota) {
      return res
        .status(400)
        .json({ msg: "No hay espacio disponible, intenta en otro horario" });
    }

    // Verificamos si la fecha actual mas un dia es mayor que el inicio del reservable
    const reservableDate = DateTime.fromJSDate(reservable.init_date).toJSDate();
    let now = DateTime.now().plus({ day: 1 }).toJSDate();
    if (now <= reservableDate) {
      return res.status(400).json({
        msg: "No puedes reservar con menos de 24 horas de anticipación",
      });
    }

    now = DateTime.now().toJSDate();
    const reservableToleranceDate = DateTime.fromJSDate(reservable.init_date)
      .plus({ minutes: toleranceReserveMinutes })
      .toJSDate();

    if (now > reservableToleranceDate) {
      return res.status(400).json({
        msg: `No puedes reservar despues de ${toleranceReserveMinutes} minutos de la hora de inicio`,
      });
    }

    // Si todo esta bien entonces creamos la reserva
    await prisma.reservation.create({
      data: {
        userId: user?.id,
        reservableId: +id,
        reservation_date: reservable.init_date,
        type: "ONLINE",
      },
    });

    // Le indicamos al usuario que todo se hizo correctamente
    return res.status(200).json({ msg: "Reservación creada correctamente" });
  } catch (error: any) {
    return handleServerError(error, "Usuario", res);
  }
};

export async function adminCreateReservation(req: ReqAdmin, res: Response) {
  // Obtenemos los parametros de la url
  const { reservableId, userId } = req.body as AdminReserveRT;

  try {
    // Creamos una transacción para asegurarnos de que si surge algún problema se haga un rollback y se mantenga la integridad de los datos

    // Obtendremos el reservable para saber si todavía hay espacio, y que no este cerrado
    const reservable = await prisma.reservable.findUnique({
      where: {
        id: reservableId,
      },
      include: {
        reservations: {
          where: {
            type: "PRESENTIAL",
            status: {
              not: "CANCELED",
            },
          },
        },
        space: {
          include: {
            closed_spaces: {
              where: {
                AND: [
                  {
                    start_date: {
                      lt: new Date(),
                    },
                  },
                  {
                    end_date: {
                      gt: new Date(),
                    },
                  },
                ],
              },
            },
          },
        },
      },
    });

    // Si no se encontro el reservable a reservar entonces lo notificamos
    if (!reservable) {
      return res.status(404).json({ msg: "No se encontro el reservable" });
    }

    // Primero verificamos que no este cerrado
    if (reservable.space.closed_spaces.length > 0) {
      return res.status(400).json({ msg: "El espacio esta cerrado" });
    }

    // Verificamos que todavía haya espacio
    if (reservable.reservations.length >= reservable.quota) {
      return res
        .status(400)
        .json({ msg: "No hay espacio disponible, intenta en otro horario" });
    }

    // Si todo esta bien entonces creamos la reserva
    await prisma.reservation.create({
      data: {
        userId,
        reservableId,
        reservation_date: reservable.init_date,
        type: "PRESENTIAL",
      },
    });

    // Le indicamos al usuario que todo se hizo correctamente
    return res.status(200).json({ msg: "Reservación creada correctamente" });
  } catch (error: any) {
    return handleServerError(error, "Usuario", res);
  }
}

export const userReservations = async (req: ReqUser, res: Response) => {
  // Obtenemos el usuario
  const { user } = req;

  // Si no hay usuario entonces notificamos
  if (!user) {
    return res.status(404).json({ msg: "No se encontro el usuario" });
  }

  try {
    // Obtenemos las reservas del usuario
    const reserves = await prisma.reservation.findMany({
      where: {
        userId: user.id,
        reservation_date: {
          gte: new Date(),
        },
      },
      include: {
        reservable: {
          include: {
            space: true,
          },
        },
      },
    });

    // Devolvemos las reservas
    return res.status(200).json(reserves);
  } catch (error: any) {
    return handleServerError(error, "Usuario", res);
  }
};

export const adminGetAllReservations = async (req: Request, res: Response) => {
  const { reservableId } = req.params;
  try {
    // Obtenemos las reservaciones del reservable
    const reservations = await prisma.reservation.findMany({
      where: {
        reservableId: +reservableId,
      },
      include: {
        user: true,
      },
    });

    return res.json(reservations);
  } catch (error: any) {
    return handleServerError(error, "Reservation", res);
  }
};

export const adminUpdateReservation = async (req: Request, res: Response) => {
  const { id, ...data } = req.body;
  try {
    // Actualizamos la reserva
    await prisma.reservation.update({
      where: {
        id: +id,
      },
      data,
    });

    return res.json({ msg: "Reservación actualizada correctamente" });
  } catch (error: any) {
    return handleServerError(error, "Reservation", res);
  }
};

export const userDeleteReservation = async (req: ReqUser, res: Response) => {
  const { id } = req.params as unknown as ReserveRT;
  const { user } = req;
  try {
    // Obtenemos la reserva
    const reservation = await prisma.reservation.findUnique({
      where: {
        id: +id,
      },
    });

    // Si no se encontro la reserva entonces notificamos
    if (!reservation) {
      return res.status(404).json({ msg: "No se encontro la reservación" });
    }

    // Verificamos que la reservación sea del usuario
    if (reservation.userId !== user?.id) {
      return res
        .status(401)
        .json({ msg: "No tienes permiso para borrar esta reservación" });
    }

    // Si todo esta bien entonces borramos la reservación
    await prisma.reservation.delete({
      where: {
        id: +id,
      },
    });

    return res.json({ msg: "Reservación eliminada correctamente" });
  } catch (error: any) {
    return handleServerError(error, "Reservation", res);
  }
};

export async function adminDownloadReservations(req: Request, res: Response) {
  const { startDate, endDate } = req.body
  try {
    const start = DateTime.fromISO(startDate)
      .startOf("day")
      .toJSDate();
    const end = DateTime.fromISO(endDate)
      .endOf("day")
      .toJSDate();

      // Obtenemos las reservaciones en ese rango
      const reservations = await prisma.reservation.findMany({
          where:{
              AND:[
                  {
                      reservation_date:{
                          gte:start
                      }
                  },
                  {
                      reservation_date:{
                          lte:end
                      }
                  }
              ]
          },
          include:{
              user:true,
              reservable:{
                include:{
                  space:true
                }
              }
          }
      })

      // Aplanamos y limpiamos los datos de salida

      const flattenedData = reservations.map(reservation=>{
        // Extraemos los datos no necesarios
        const {user,userId,reservableId,reservable, ...rest} = reservation

        return{
          ...rest,
          alumno: user?.registration ?? "Alumno Eliminado",
          espacio: reservable?.space?.name ?? "Espacio eliminado",
          
        }
      } )

      if (flattenedData.length > 0) {
        const reservationsCsv = new Parser({ encoding: "utf-8" }).parse(
          flattenedData
        );
        res.setHeader(
          "Content-Disposition",
          "attachment; filename=reservaciones.csv"
        );
        res.setHeader("Content-Type", "text/csv ; charset=UTF-8");
  
        
  
        return res.send(reservationsCsv);
      }
  
      return res.status(500).json({
        error: "No hay registros en la tabla Asistencia para exportar.",
      });
  } catch (error:any) {
    return handleServerError(error, "Reservation", res)
  }
}