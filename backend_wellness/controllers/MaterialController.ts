// MaterialController.ts
import { Request, Response } from "express";
import { handleServerError } from "../helpers/handleServerError.js";
import { prisma } from "../index.js";
import cloudinary from "../config/cloudinaryConfig.js";
import fs from "fs";
import csvParser from "csv-parser";
import { MaterialCsvRow, normalizeRowKeys, validateMaterialRow } from "../functions/materialCsvFunctions.js";

// Type for request with file from multer
interface RequestWithFile extends Request {
    file?: Express.Multer.File;
}

export const createMaterial = async (req: RequestWithFile, res: Response) => {
    let material: any, image: any;

    try {
        // Verificar si hay un archivo
        if (!req.file) {
            return res.status(400).json({ msg: "No se proporcionó ninguna imagen" });
        }

        const {
            name,
            quantity,
            minMatriculas,
            leadTimeDays,
            rules,
            replacementCost
        } = req.body;

        // Subimos la imagen a Cloudinary
        image = await cloudinary.uploader.upload(req.file.path, {
            folder: `materiales`,
            quality: "auto:eco",
        });

        // Limpiamos el archivo temporal después de subirlo
        if (fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }

        // Creamos el material en la base de datos
        material = await prisma.material.create({
            data: {
                name,
                quantity: +quantity,
                minMatriculas: +minMatriculas,
                leadTimeDays: +leadTimeDays,
                rules,
                replacementCost: +replacementCost,
                image: image.secure_url,
                image_id: image.public_id,
                deleted: false
            }
        });

        return res.status(200).json({ msg: "Material creado correctamente" });
    } catch (error: any) {
        console.error("Error al crear material:", error);

        // Si hay un error verificamos para hacer rollback de un cambio
        if (material) {
            await prisma.material.delete({
                where: {
                    id: material.id
                }
            });
        }
        if (image && image.public_id) {
            await cloudinary.uploader.destroy(image.public_id);
        }

        // Limpiamos el archivo temporal si aún existe
        if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }

        return handleServerError(error, "Material", res);
    }
};

// Obtener todos los materiales
export const getAllMaterials = async (_req: Request, res: Response) => {
    try {
        const materials = await prisma.material.findMany({
            where: {
                deleted: false
            },
            orderBy: {
                name: 'asc'
            }
        });

        return res.json(materials);
    } catch (error: any) {
        console.error("Error al obtener materiales:", error);
        return handleServerError(error, "Material", res);
    }
};

// Obtener materiales disponibles (con cantidad > 0)
export const getAvailableMaterials = async (_req: Request, res: Response) => {
    try {
        const materials = await prisma.material.findMany({
            where: {
                deleted: false,
                quantity: {
                    gt: 0
                }
            },
            orderBy: {
                name: 'asc'
            }
        });

        return res.json(materials);
    } catch (error: any) {
        console.error("Error al obtener materiales disponibles:", error);
        return handleServerError(error, "Material", res);
    }
};

// Obtener un material por ID
export const getMaterialById = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;

        const material = await prisma.material.findUnique({
            where: {
                id: parseInt(id)
            }
        });

        if (!material) {
            return res.status(404).json({ msg: "Material no encontrado" });
        }

        if (material.deleted) {
            return res.status(410).json({ msg: "Este material ha sido eliminado" });
        }

        return res.json(material);
    } catch (error: any) {
        console.error("Error al obtener material:", error);
        return handleServerError(error, "Material", res);
    }
};

// Actualizar un material
export const updateMaterial = async (req: RequestWithFile, res: Response) => {
    let updatedImage: any;
    try {
        const { id } = req.params;
        const { name, quantity, minMatriculas, leadTimeDays, rules, replacementCost } = req.body;

        // Verificar que el material existe
        const material = await prisma.material.findUnique({
            where: {
                id: parseInt(id)
            }
        });

        if (!material) {
            return res.status(404).json({ msg: "Material no encontrado" });
        }

        // Datos para actualizar
        const updateData: any = {
            name,
            quantity: +quantity,
            minMatriculas: +minMatriculas,
            leadTimeDays: +leadTimeDays,
            rules,
            replacementCost: +replacementCost
        };

        // Si hay una nueva imagen, actualizarla
        if (req.file) {
            // Subir la nueva imagen
            updatedImage = await cloudinary.uploader.upload(req.file.path, {
                folder: `materiales`,
                quality: "auto:eco",
            });

            // Eliminar la imagen temporal
            if (fs.existsSync(req.file.path)) {
                fs.unlinkSync(req.file.path);
            }

            // Eliminar la imagen anterior de Cloudinary
            if (material.image_id) {
                await cloudinary.uploader.destroy(material.image_id);
            }

            // Actualizar los datos de la imagen
            updateData.image = updatedImage.secure_url;
            updateData.image_id = updatedImage.public_id;
        }

        // Actualizar el material
        const updated = await prisma.material.update({
            where: {
                id: parseInt(id)
            },
            data: updateData
        });

        return res.json({ msg: "Material actualizado correctamente", material: updated });
    } catch (error: any) {
        console.error("Error al actualizar material:", error);

        // Si hubo un error y se subió una imagen, eliminarla
        if (updatedImage && updatedImage.public_id) {
            await cloudinary.uploader.destroy(updatedImage.public_id);
        }

        // Eliminar el archivo temporal si existe
        if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }

        return handleServerError(error, "Material", res);
    }
};

// Eliminar un material (soft delete)
export const deleteMaterial = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;

        // Verificar que el material existe
        const material = await prisma.material.findUnique({
            where: {
                id: parseInt(id)
            }
        });

        if (!material) {
            return res.status(404).json({ msg: "Material no encontrado" });
        }

        // Verificar si hay préstamos activos de este material
        // Usamos una consulta SQL nativa para evitar problemas con los enums
        const activeLoansResult: any = await prisma.$queryRaw`
            SELECT COUNT(*) as count
            FROM "loan"
            WHERE "materialId" = ${parseInt(id)}
            AND "status" IN ('PENDING', 'AWAITING_PICKUP', 'ON_LOAN')
        `;

        const count = Number(activeLoansResult[0].count);

        if (count > 0) {
            return res.status(400).json({
                msg: "No se puede eliminar este material porque tiene préstamos activos"
            });
        }

        // Realizar soft delete
        await prisma.material.update({
            where: {
                id: parseInt(id)
            },
            data: {
                deleted: true
            }
        });

        return res.json({ msg: "Material eliminado correctamente" });
    } catch (error: any) {
        console.error("Error al eliminar material:", error);
        return handleServerError(error, "Material", res);
    }
};

// Importar materiales desde CSV
export const uploadCsv = async (req: RequestWithFile, res: Response) => {
    try {
        // Verificar que se proporcionó un archivo
        if (!req.file) {
            return res.status(400).json({ msg: "No se proporcionó ningún archivo" });
        }

        // Verificar que el archivo sea un CSV
        if (req.file.mimetype !== "text/csv" && !req.file.originalname.endsWith('.csv')) {
            return res.status(400).json({ msg: "El archivo debe ser un CSV" });
        }

        // Array para almacenar los materiales validados
        const validMaterials: MaterialCsvRow[] = [];

        // Estadísticas para el reporte
        let totalRows = 0;
        let validRows = 0;
        let invalidRows = 0;

        // Procesar el archivo CSV
        const processFile = () => {
            return new Promise<void>((resolve, reject) => {
                // Ya verificamos que req.file existe, así que es seguro usarlo
                fs.createReadStream(req.file!.path)
                    .pipe(csvParser())
                    .on('data', (row: any) => {
                        totalRows++;

                        // Normalizar las claves del objeto
                        const normalizedRow = normalizeRowKeys(row);

                        // Validar la fila
                        const validatedRow = validateMaterialRow(normalizedRow);

                        if (validatedRow) {
                            validMaterials.push(validatedRow);
                            validRows++;
                        } else {
                            invalidRows++;
                        }
                    })
                    .on('end', () => {
                        resolve();
                    })
                    .on('error', (error) => {
                        reject(error);
                    });
            });
        };

        // Procesar el archivo
        await processFile();

        // Eliminar el archivo temporal
        if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }

        // Si no hay materiales válidos, retornar error
        if (validMaterials.length === 0) {
            return res.status(400).json({
                msg: "No se encontraron materiales válidos en el archivo CSV"
            });
        }

        // Crear los materiales en la base de datos
        let createdCount = 0;

        for (const material of validMaterials) {
            try {
                // Crear el material con una imagen por defecto si no se proporciona
                await prisma.material.create({
                    data: {
                        name: material.name,
                        quantity: parseInt(material.quantity),
                        minMatriculas: parseInt(material.minMatriculas),
                        leadTimeDays: parseInt(material.leadTimeDays),
                        rules: material.rules,
                        replacementCost: parseFloat(material.replacementCost),
                        // Usar una imagen por defecto si no se proporciona
                        image: material.image || "https://res.cloudinary.com/dkddd5aky/image/upload/v1711488618/materiales/default-material_yvpwgz.jpg",
                        image_id: "default",
                        deleted: false
                    }
                });
                createdCount++;
            } catch (error) {
                console.error(`Error al crear material ${material.name}:`, error);
                // Continuar con el siguiente material
            }
        }

        return res.status(200).json({
            msg: `Se importaron ${createdCount} materiales correctamente`,
            stats: {
                totalRows,
                validRows,
                invalidRows,
                createdCount
            }
        });
    } catch (error: any) {
        console.error("Error al importar materiales desde CSV:", error);

        // Limpiar el archivo temporal si existe
        if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }

        return handleServerError(error, "Material", res);
    }
};