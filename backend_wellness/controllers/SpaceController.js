import dotenv from "dotenv";
dotenv.config();
import { handleServerError } from "../helpers/handleServerError.js";
import { prisma } from "../index.js";
import moment from "moment-timezone";
import { v2 as cloudinary } from "cloudinary";
import { ReservationStatus } from "@prisma/client";
import { DateTime } from "luxon";
import { toleranceReserveMinutes } from "../constants/index.js";
cloudinary.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET,
});
export const createSpace = async (req, res) => {
    let space, image;
    try {
        const { adminId, quota, materials_required, materials, onlineQuota, ...data } = req.body;
        image = await cloudinary.uploader.upload(req.file.path, {
            folder: `reservas`,
            quality: "auto:eco",
        });
        space = await prisma.space.create({
            data: {
                ...data,
                quota: +quota,
                onlineQuota: +onlineQuota,
                adminId: +adminId,
                materials_required: materials_required === "true" ? true : false,
                materials: materials.split(","),
                image: image.secure_url,
                image_id: image.public_id
            }
        });
        return res.status(200).json({ msg: "Espacio creado!!" });
    }
    catch (error) {
        if (space) {
            await prisma.space.delete({
                where: {
                    id: space.id
                }
            });
        }
        if (image) {
            await cloudinary.uploader.destroy(image.public_id);
        }
        return handleServerError(error, "Admin", res);
    }
};
export async function editSpace(req, res) {
    try {
        const { id, ...data } = req.body;
        const image = req?.file?.path;
        if (image) {
            const space = await prisma.space.findFirst({
                where: {
                    id: +id
                }
            });
            if (space?.image_id) {
                await cloudinary.uploader.destroy(space?.image_id);
            }
            const newImage = await cloudinary.uploader.upload(image, {
                folder: `reservas`,
                quality: "auto:eco",
            });
            await prisma.space.update({
                where: {
                    id: +id
                },
                data: {
                    name: data.name,
                    quota: +data.quota,
                    onlineQuota: +data.onlineQuota,
                    location: data.location,
                    open_time: data.open_time,
                    close_time: data.close_time,
                    materials_required: data.materials_required === "true" ? true : false,
                    image: newImage.secure_url,
                    image_id: newImage.public_id
                }
            });
            return res.json({ msg: "Espacio modificado correctamente" });
        }
        await prisma.space.update({
            where: {
                id: +id
            },
            data: {
                name: data.name,
                quota: +data.quota,
                onlineQuota: +data.onlineQuota,
                location: data.location,
                open_time: data.open_time,
                close_time: data.close_time,
                materials_required: data.materials_required === "true" ? true : false
            }
        });
        return res.json({ msg: "Espacio modificado correctamente" });
    }
    catch (error) {
        return handleServerError(error, "Admin", res);
    }
}
export const getQuota = async (req, res) => {
    try {
        const { id } = req.body;
        const verify = await prisma.space.findFirst({
            where: {
                id: id
            }
        });
        if (!verify) {
            return res.status(404).json({ msg: "Area inexistente!!" });
        }
        return res.status(200).json({ "quota": verify.quota });
    }
    catch (error) {
        return handleServerError(error, "Admin", res);
    }
};
export const changeQuota = async (req, res) => {
    try {
        const { id, quota } = req.body;
        const verify = await prisma.space.findFirst({
            where: {
                id: id
            }
        });
        if (!verify) {
            return res.status(404).json({ msg: "Area inexistente!!" });
        }
        if (quota < 1 || quota > 400) {
            return res.status(404).json({ msg: "Tiene que ser un número de aforo válido" });
        }
        await prisma.space.update({
            where: {
                id: id
            },
            data: {
                quota: quota
            }
        });
        const listaDeReservables = await prisma.reservable.findMany({
            where: {
                spaceId: id,
            },
            include: {
                reservations: {
                    where: {
                        status: ReservationStatus.APPROVED
                    }
                }
            }
        });
        for (const each of listaDeReservables) {
            await prisma.$transaction(async (tx) => {
                if (each.reservations.length <= quota) {
                    await tx.reservable.update({
                        where: {
                            id: each.id
                        },
                        data: {
                            quota: quota
                        }
                    });
                }
                else {
                    throw new Error("La transacción no se realizó correctamente");
                }
            });
        }
        return res.status(200).json({ msg: "El aforo se ha cambiado exitosamente!!" });
    }
    catch (error) {
        return handleServerError(error, "Admin", res);
    }
};
export const changeQuotaReservable = async (req, res) => {
    try {
        const { id, quota } = req.body;
        const verify = await prisma.reservable.findFirst({
            where: {
                id: id
            }
        });
        if (!verify) {
            return res.status(404).json({ msg: "El espacio reservable no existe!!" });
        }
        await prisma.reservable.update({
            where: {
                id: id
            },
            data: {
                quota: quota
            }
        });
        return res.status(200).json({ msg: "Se ha realizado el cambio correctamente!!" });
    }
    catch (error) {
        return handleServerError(error, "Admin", res);
    }
};
export const createReservable = async (req, res) => {
    try {
        const { ...data } = req.body;
        await prisma.reservable.create({
            data: {
                ...data
            }
        });
        return res.status(200).json({ msg: "Se ha creado un reservable de forma exitosa" });
    }
    catch (error) {
        return handleServerError(error, "Admin", res);
    }
};
export const getUserSpaces = async (_req, res) => {
    try {
        const dateTimewithTolerance = DateTime.now().minus({ minutes: toleranceReserveMinutes }).toJSDate();
        const spaces = await prisma.space.findMany({
            where: {
                reservables: {
                    some: {
                        booking: true,
                        init_date: {
                            gte: dateTimewithTolerance
                        },
                        onlineQuota: {
                            gt: 0
                        }
                    },
                },
            },
            include: {
                closed_spaces: {
                    where: {
                        start_date: {
                            lt: new Date()
                        },
                        end_date: {
                            gt: dateTimewithTolerance
                        }
                    }
                }
            }
        });
        const newSpaces = spaces.map(space => {
            const { closed_spaces, ...data } = space;
            return {
                ...data,
                open: closed_spaces.length === 0
            };
        });
        return res.json(newSpaces);
    }
    catch (error) {
        return handleServerError(error, "Admin", res);
    }
};
export const getSpaces = async (_req, res) => {
    try {
        const dateTimewithTolerance = DateTime.now().minus({ minutes: toleranceReserveMinutes }).toJSDate();
        const spaces = await prisma.space.findMany({
            where: {
                reservables: {
                    some: {
                        booking: true,
                        init_date: {
                            gte: dateTimewithTolerance
                        },
                        onlineQuota: {
                            gt: 0
                        }
                    },
                },
            },
            include: {
                closed_spaces: {
                    where: {
                        start_date: {
                            lt: new Date()
                        },
                        end_date: {
                            gt: new Date()
                        }
                    }
                }
            },
            orderBy: {
                name: "asc"
            }
        });
        const newSpaces = spaces.map(space => {
            const { closed_spaces, ...data } = space;
            return {
                ...data,
                open: closed_spaces.length === 0
            };
        });
        return res.json(newSpaces);
    }
    catch (error) {
        return handleServerError(error, "Admin", res);
    }
};
export const openSpace = async (req, res) => {
    const { id } = req.params;
    try {
        await prisma.closed_space.deleteMany({
            where: {
                spaceId: +id
            }
        });
        return res.json({ msg: "Espacio Abierto" });
    }
    catch (error) {
        return handleServerError(error, "Admin", res);
    }
};
export const closeSpace = async (req, res) => {
    const data = req.body;
    try {
        const closedSpace = await prisma.closed_space.findFirst({
            where: {
                spaceId: Number(data.id),
                start_date: {
                    lt: new Date()
                },
                end_date: {
                    gt: new Date()
                }
            }
        });
        if (closedSpace) {
            return res.status(404).json({ msg: "El Espacio ya estaba cerrado" });
        }
        await prisma.closed_space.create({
            data: {
                spaceId: Number(data.id),
                start_date: new Date(data.start_date),
                end_date: new Date(data.end_date),
                description: data.description
            }
        });
        return res.json({ msg: "Espacio Cerrado Correctemente" });
    }
    catch (error) {
        return handleServerError(error, "Admin", res);
    }
};
export const getPermanence = async (_req, res) => {
    try {
        const permancenceMorning = await prisma.permanenceTime.findFirst({
            where: {
                schedule: "MORNING"
            },
            orderBy: {
                id: "desc"
            }
        });
        const permancenceNight = await prisma.permanenceTime.findFirst({
            where: {
                schedule: "NIGHT"
            },
            orderBy: {
                id: "desc"
            }
        });
        return {
            morning: permancenceMorning,
            night: permancenceNight
        };
    }
    catch (error) {
        return handleServerError(error, "Wellness", res);
    }
};
export const setPermanence = async (req, res) => {
    const { schedule, hours, minutes } = req.body;
    try {
        const permancence = await prisma.permanenceTime.create({
            data: {
                schedule: schedule.toUpperCase(),
                hours,
                minutes
            }
        });
        return res.json(permancence);
    }
    catch (error) {
        return handleServerError(error, "Wellness", res);
    }
};
export const getWellnessLiveStatus = async (_req, res) => {
    try {
        const currentHour = DateTime.now().setZone("America/Monterrey").hour;
        const currentMins = DateTime.now().setZone("America/Monterrey").minute;
        const wellness = await prisma.space.findFirst({
            where: {
                name: {
                    contains: "wellness",
                    mode: "insensitive"
                }
            }
        });
        const wellnessQuota = wellness?.quota ?? 0;
        if ((currentHour === 15 && currentMins >= 0 && currentMins <= 30)) {
            return res.json([
                { label: "Llenado", value: 0 },
                { label: "Restante", value: wellnessQuota }
            ]);
        }
        let hours = 2;
        let minutes = 0;
        if (currentHour < 17) {
            const morning = await prisma.permanenceTime.findFirst({
                where: {
                    schedule: "MORNING"
                },
                orderBy: {
                    id: "desc"
                }
            });
            hours = morning?.hours ?? 2;
            minutes = morning?.minutes ?? 0;
        }
        else {
            const night = await prisma.permanenceTime.findFirst({
                where: {
                    schedule: "NIGHT"
                },
                orderBy: {
                    id: "desc"
                }
            });
            hours = night?.hours ?? 2;
            minutes = night?.minutes ?? 0;
        }
        const totalMinutes = hours * 60 + minutes;
        const today = DateTime.now().setZone("America/Monterrey").minus({ minutes: totalMinutes }).toJSDate();
        const attendances = await prisma.attendance.count({
            where: {
                date: {
                    gt: today
                }
            }
        });
        const remaining = wellnessQuota - attendances < 0 ? 0 : wellnessQuota - attendances;
        const fillPercentage = attendances / wellnessQuota * 100;
        const response = [
            { label: "Llenado", value: Math.round(attendances) },
            { label: "Restante", value: Math.round(remaining) }
        ];
        return res.json(response);
    }
    catch (error) {
        return handleServerError(error, "Wellness", res);
    }
};
export const getWellnessAttendances = async (_req, res) => {
    try {
        const currentLocalHour = DateTime.now().setZone("America/Monterrey").hour;
        let hours = 2;
        let minutes = 0;
        if (currentLocalHour < 17) {
            const morning = await prisma.permanenceTime.findFirst({
                where: {
                    schedule: "MORNING"
                },
                orderBy: {
                    id: "desc"
                }
            });
            hours = morning?.hours ?? 2;
            minutes = morning?.minutes ?? 0;
        }
        else {
            const night = await prisma.permanenceTime.findFirst({
                where: {
                    schedule: "NIGHT"
                },
                orderBy: {
                    id: "desc"
                }
            });
            hours = night?.hours ?? 2;
            minutes = night?.minutes ?? 0;
        }
        const totalMinutes = hours * 60 + minutes;
        const currentDateTime = new Date().toLocaleString("en-US", {
            timeZone: "America/Monterrey",
        });
        const currentDate = new Date(currentDateTime);
        const currentHour = currentDate.getHours();
        let attendancesData = [];
        const formatHour = (hour) => {
            const isPM = hour >= 12;
            const adjustedHour = hour % 12 === 0 ? 12 : hour % 12;
            return `${adjustedHour}:00 ${isPM ? "PM" : "AM"}`;
        };
        for (let hour = 0; hour <= currentHour; hour++) {
            const startTime = moment
                .tz({
                year: currentDate.getFullYear(),
                month: currentDate.getMonth(),
                day: currentDate.getDate(),
                hour: hour,
            }, "America/Monterrey")
                .toDate();
            const endTime = new Date(startTime.getTime());
            endTime.setMinutes(endTime.getMinutes() - totalMinutes);
            if (hour === 15 && startTime.getMinutes() >= 0 && startTime.getMinutes() <= 30) {
                attendancesData.push({
                    label: formatHour(hour),
                    value: 0,
                    projection: false,
                });
                continue;
            }
            const count = await prisma.attendance.count({
                where: {
                    date: {
                        lte: startTime,
                        gte: endTime,
                    },
                },
            });
            if (count > 0) {
                attendancesData.push({
                    label: formatHour(hour),
                    value: count,
                    projection: false,
                });
            }
        }
        const lastWeekDate = new Date(currentDate.getTime());
        lastWeekDate.setDate(currentDate.getDate() - 7);
        for (let hour = currentHour + 1; hour < 24; hour++) {
            const startTime = moment
                .tz({
                year: lastWeekDate.getFullYear(),
                month: lastWeekDate.getMonth(),
                day: lastWeekDate.getDate(),
                hour: hour,
            }, "America/Monterrey")
                .toDate();
            const endTime = new Date(startTime.getTime());
            endTime.setMinutes(endTime.getMinutes() + totalMinutes);
            if (hour === 15 && startTime.getMinutes() >= 0 && startTime.getMinutes() <= 30) {
                attendancesData.push({
                    label: formatHour(hour),
                    value: 0,
                    projection: true,
                });
                continue;
            }
            const projectionCount = await prisma.attendance.count({
                where: {
                    date: {
                        gte: startTime,
                        lt: endTime,
                    },
                },
            });
            if (projectionCount > 0) {
                attendancesData.push({
                    label: formatHour(hour),
                    value: projectionCount,
                    projection: true,
                });
            }
        }
        return res.json(attendancesData);
    }
    catch (error) {
        return handleServerError(error, "Wellness", res);
    }
};
